# Correção do Erro 500 na API

## 🔍 Problema

A API `/api/v1/client-posts/pending` estava retornando erro 500 (Erro interno do servidor).

## 🔧 Correções Aplicadas

### 1. **Logs de Debug Adicionados**
- Logs detalhados em cada etapa do processamento
- Identificação específica de onde o erro ocorre
- Stack trace completo em caso de erro

### 2. **Query Simplificada**
- Removido `DISTINCT` que pode causar problemas
- Mudado `INNER JOIN` para `LEFT JOIN` para evitar perda de dados
- Adicionado `COALESCE` para campos que podem ser NULL
- Teste inicial com query simples antes da query completa

### 3. **Tratamento de Erros Melhorado**
- Try-catch em cada etapa crítica
- Verificação de dados NULL/vazios
- Fallbacks para dados ausentes

### 4. **Endpoint de Teste Criado**
- `/api/v1/client-posts/test` para verificar conectividade básica
- Teste simples de contagem de posts pendentes

## 📋 Alterações no Código

### Arquivo: `controllers/ApiController.php`

1. **Método `testPendingPosts()` (NOVO)**:
   ```php
   public function testPendingPosts() {
       // Teste básico de conectividade
       $count = $this->db->fetchAll("SELECT COUNT(*) as total FROM client_posts WHERE status = 'pending'");
       return ApiResponse::success(['pending_count' => $count[0]['total']]);
   }
   ```

2. **Método `getPendingClientPosts()` (CORRIGIDO)**:
   - ✅ Logs de debug detalhados
   - ✅ Query simplificada com LEFT JOIN
   - ✅ COALESCE para campos NULL
   - ✅ Tratamento de erro específico
   - ✅ Verificação de posts vazios

### Arquivo: `routes/api.php`

3. **Nova Rota de Teste**:
   ```php
   $router->get('/api/v1/client-posts/test', 'Api@testPendingPosts');
   ```

## 🧪 Como Testar

### 1. **Teste Básico**
```bash
curl -X GET "https://app.melhorcupom.shop/api/v1/client-posts/test"
```

**Resposta Esperada:**
```json
{
  "success": true,
  "message": "Sucesso",
  "data": {
    "test": "success",
    "pending_count": 2,
    "timestamp": "2025-06-23 17:45:00"
  }
}
```

### 2. **Teste Principal**
```bash
curl -X GET "https://app.melhorcupom.shop/api/v1/client-posts/pending"
```

**Resposta Esperada:**
```json
{
  "success": true,
  "message": "Sucesso",
  "data": {
    "total_pending": 2,
    "clients_with_pending": 1,
    "sites_with_pending": 1,
    "posts_by_client": [...],
    "posts_by_site": [...],
    "all_posts": [...]
  }
}
```

## 🔍 Debug de Erros

### Verificar Logs
Os logs agora incluem:
- `=== getPendingClientPosts DEBUG ===`
- `Posts encontrados (versão simples): X`
- `Tentando query completa...`
- `Posts com dados completos: X`
- `Processando URLs das imagens...`
- `Organização concluída - Sites: X, Clientes: Y`

### Possíveis Causas do Erro 500

1. **Problema na Query SQL**:
   - Campo inexistente
   - JOIN incorreto
   - Sintaxe SQL inválida

2. **Problema no Processamento**:
   - Erro na função `processPostImages()`
   - Dados NULL não tratados
   - Problema na organização dos dados

3. **Problema na Resposta**:
   - Erro na serialização JSON
   - Headers já enviados
   - Problema no `ApiResponse::success()`

## ✅ Melhorias Implementadas

### 1. **Robustez**
- ✅ LEFT JOIN em vez de INNER JOIN
- ✅ COALESCE para campos NULL
- ✅ Verificação de dados vazios
- ✅ Try-catch granular

### 2. **Debug**
- ✅ Logs detalhados em cada etapa
- ✅ Stack trace completo em erros
- ✅ Endpoint de teste separado
- ✅ Contadores de registros

### 3. **Compatibilidade**
- ✅ Funciona mesmo com dados incompletos
- ✅ Fallbacks para sites/usuários ausentes
- ✅ Resposta consistente mesmo sem posts

## 🎯 Próximos Passos

1. **Teste o endpoint básico** primeiro: `/api/v1/client-posts/test`
2. **Se funcionar**, teste o principal: `/api/v1/client-posts/pending`
3. **Verifique os logs** do servidor para identificar onde para
4. **Se ainda der erro**, verifique:
   - Estrutura das tabelas no banco
   - Permissões de acesso
   - Configuração do servidor web

## 🚨 Se Ainda Houver Erro

Verifique os logs do servidor web (Apache/Nginx) e do PHP para mensagens específicas:

```bash
# Logs do Apache
tail -f /var/log/apache2/error.log

# Logs do PHP
tail -f /var/log/php/error.log

# Logs personalizados (se configurados)
tail -f /path/to/app/logs/error.log
```

O erro 500 agora deve fornecer informações mais específicas sobre onde exatamente está falhando.
