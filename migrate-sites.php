<?php
/**
 * <PERSON>ript para executar migração da tabela client_sites
 */

require_once __DIR__ . '/bootstrap.php';

try {
    $db = Database::getInstance();
    
    echo "Executando migração da tabela client_sites...\n";
    
    $sql = "
    CREATE TABLE IF NOT EXISTS client_sites (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        site_name VARCHAR(255) NOT NULL,
        site_url VARCHAR(500) NOT NULL,
        status ENUM('pending', 'connected', 'disconnected', 'suspended') DEFAULT 'pending',
        plugin_version VARCHAR(50) NULL,
        last_connection TIMESTAMP NULL,
        api_key_id INT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        FOREIG<PERSON> KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (api_key_id) REFERENCES api_keys(id) ON DELETE SET NULL,
        
        UNIQUE KEY unique_user_url (user_id, site_url),
        INDEX idx_user_id (user_id),
        INDEX idx_status (status)
    );
    ";
    
    $db->getConnection()->exec($sql);
    
    echo "✅ Tabela client_sites criada com sucesso!\n";
    
    // Verificar se a tabela foi criada
    $result = $db->fetch("SHOW TABLES LIKE 'client_sites'");
    if ($result) {
        echo "✅ Verificação: Tabela existe no banco de dados\n";
        
        // Mostrar estrutura da tabela
        $columns = $db->fetchAll("DESCRIBE client_sites");
        echo "\n📋 Estrutura da tabela:\n";
        foreach ($columns as $column) {
            echo "  - {$column['Field']}: {$column['Type']}\n";
        }
    } else {
        echo "❌ Erro: Tabela não foi criada\n";
    }
    
} catch (Exception $e) {
    echo "❌ Erro na migração: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n🎉 Migração concluída!\n";
