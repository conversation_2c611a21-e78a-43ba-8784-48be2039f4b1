-- Script SQL para corrigir URLs temporárias no banco de dados
-- Execute este SQL no phpMyAdmin

-- 1. Verificar posts com URLs temporárias ou placeholders
SELECT
    id,
    product_images,
    post_cover,
    'ANTES DA CORREÇÃO' as status
FROM client_posts
WHERE product_images LIKE '%temp_%'
   OR post_cover LIKE '%temp_%'
   OR product_images LIKE '%PLACEHOLDER%'
   OR post_cover LIKE '%PLACEHOLDER%';

-- 2. Corrigir URLs das imagens do produto
-- Remove o padrão /temp_XXXXXXX_XXXX/ e substitui por /ID_DO_POST/
UPDATE client_posts 
SET product_images = REGEXP_REPLACE(
    product_images, 
    '"/uploads/posts/temp_[0-9]+_[0-9]+/', 
    CONCAT('"/uploads/posts/', id, '/')
)
WHERE product_images LIKE '%temp_%';

-- 3. Corrigir URLs das capas dos posts
-- Remove o padrão /temp_XXXXXXX_XXXX/ e substitui por /ID_DO_POST/
UPDATE client_posts 
SET post_cover = REGEXP_REPLACE(
    post_cover, 
    '/uploads/posts/temp_[0-9]+_[0-9]+/', 
    CONCAT('/uploads/posts/', id, '/')
)
WHERE post_cover LIKE '%temp_%';

-- 4. Verificar se a correção funcionou
SELECT 
    id, 
    product_images, 
    post_cover,
    'APÓS CORREÇÃO' as status
FROM client_posts 
WHERE product_images LIKE '%temp_%' OR post_cover LIKE '%temp_%';

-- 5. Mostrar todos os posts com imagens para verificação
SELECT 
    id, 
    product_images, 
    post_cover,
    created_at
FROM client_posts 
WHERE product_images IS NOT NULL OR post_cover IS NOT NULL
ORDER BY id DESC
LIMIT 10;
