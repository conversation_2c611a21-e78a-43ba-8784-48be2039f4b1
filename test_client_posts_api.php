<?php
/**
 * Script de Teste para API Client Posts
 * Sistema de Dashboard E1Copy AI
 */

// Configurações
$baseUrl = 'https://app.melhorcupom.shop';
$endpoints = [
    'all_posts' => '/api/v1/client-posts',
    'pending_posts' => '/api/v1/client-posts?status=pending',
    'product_reviews' => '/api/v1/client-posts?post_type=product_review',
    'with_content' => '/api/v1/client-posts?include_content=true&limit=2',
    'without_content' => '/api/v1/client-posts?include_content=false&limit=5',
    'paginated' => '/api/v1/client-posts?limit=3&offset=0',
    'stats' => '/api/v1/client-posts/stats',
    'specific_post' => '/api/v1/client-posts/1'
];

echo "=== TESTE DA API CLIENT POSTS ===\n\n";

function makeRequest($url) {
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_USERAGENT => 'E1Copy-API-Test/1.0'
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        return ['error' => $error, 'http_code' => 0];
    }
    
    return [
        'data' => json_decode($response, true),
        'http_code' => $httpCode,
        'raw_response' => $response
    ];
}

function displayResult($title, $result) {
    echo "--- {$title} ---\n";
    echo "HTTP Code: {$result['http_code']}\n";
    
    if (isset($result['error'])) {
        echo "❌ Erro: {$result['error']}\n\n";
        return;
    }
    
    if ($result['http_code'] !== 200) {
        echo "❌ Erro HTTP: {$result['http_code']}\n";
        echo "Response: {$result['raw_response']}\n\n";
        return;
    }
    
    $data = $result['data'];
    
    if (!$data || !isset($data['success'])) {
        echo "❌ Resposta inválida\n\n";
        return;
    }
    
    if (!$data['success']) {
        echo "❌ API Error: " . ($data['error'] ?? 'Unknown error') . "\n\n";
        return;
    }
    
    echo "✅ Sucesso!\n";
    
    // Exibir informações específicas baseadas no endpoint
    if (isset($data['data']['stats'])) {
        $stats = $data['data']['stats'];
        echo "📊 Estatísticas:\n";
        echo "   - Total de posts: {$stats['total_posts']}\n";
        echo "   - Posts retornados: {$stats['returned_posts']}\n";
        echo "   - Total de clientes: {$stats['total_clients']}\n";
        echo "   - Total de sites: {$stats['total_sites']}\n";
        echo "   - Tem mais dados: " . ($stats['has_more'] ? 'Sim' : 'Não') . "\n";
    }
    
    if (isset($data['data']['posts'])) {
        $posts = $data['data']['posts'];
        echo "📝 Posts encontrados: " . count($posts) . "\n";
        
        foreach (array_slice($posts, 0, 3) as $i => $post) {
            echo "   " . ($i + 1) . ". {$post['title']} ({$post['post_type']}) - {$post['status']}\n";
            echo "      Cliente: {$post['user_name']} | Site: {$post['site_name']}\n";
        }
        
        if (count($posts) > 3) {
            echo "   ... e mais " . (count($posts) - 3) . " posts\n";
        }
    }
    
    if (isset($data['data']['general_stats'])) {
        $stats = $data['data']['general_stats'];
        echo "📈 Estatísticas Gerais:\n";
        echo "   - Total de posts: {$stats['total_posts']}\n";
        echo "   - Total de clientes: {$stats['total_clients']}\n";
        echo "   - Posts pendentes: {$stats['pending_posts']}\n";
        echo "   - Posts publicados: {$stats['published_posts']}\n";
        echo "   - Artigos: {$stats['articles']}\n";
        echo "   - Reviews: {$stats['product_reviews']}\n";
    }
    
    if (isset($data['data']['id'])) {
        // Post específico
        $post = $data['data'];
        echo "📄 Post: {$post['title']}\n";
        echo "   - Tipo: {$post['post_type']}\n";
        echo "   - Status: {$post['status']}\n";
        echo "   - Cliente: {$post['user_name']}\n";
        echo "   - Site: {$post['site_name']}\n";
        echo "   - Criado em: {$post['created_at']}\n";
        
        if (!empty($post['product_name'])) {
            echo "   - Produto: {$post['product_name']}\n";
        }
    }
    
    echo "\n";
}

// Executar testes
foreach ($endpoints as $name => $endpoint) {
    $url = $baseUrl . $endpoint;
    echo "🔗 Testando: {$url}\n";
    
    $result = makeRequest($url);
    displayResult(strtoupper(str_replace('_', ' ', $name)), $result);
    
    // Pequena pausa entre requisições
    usleep(500000); // 0.5 segundos
}

echo "=== EXEMPLOS DE USO ===\n\n";

echo "1. Posts pendentes:\n";
echo "   curl -X GET \"{$baseUrl}/api/v1/client-posts?status=pending\"\n\n";

echo "2. Reviews de produtos:\n";
echo "   curl -X GET \"{$baseUrl}/api/v1/client-posts?post_type=product_review\"\n\n";

echo "3. Posts de um cliente específico:\n";
echo "   curl -X GET \"{$baseUrl}/api/v1/client-posts?user_id=4\"\n\n";

echo "4. Posts sem conteúdo (mais rápido):\n";
echo "   curl -X GET \"{$baseUrl}/api/v1/client-posts?include_content=false\"\n\n";

echo "5. Paginação:\n";
echo "   curl -X GET \"{$baseUrl}/api/v1/client-posts?limit=20&offset=40\"\n\n";

echo "6. Estatísticas:\n";
echo "   curl -X GET \"{$baseUrl}/api/v1/client-posts/stats\"\n\n";

echo "7. Post específico:\n";
echo "   curl -X GET \"{$baseUrl}/api/v1/client-posts/123\"\n\n";

echo "=== FILTROS DISPONÍVEIS ===\n\n";
echo "- status: draft, pending, processing, completed, failed, published\n";
echo "- post_type: article, product_review\n";
echo "- user_id: ID do usuário\n";
echo "- site_id: ID do site\n";
echo "- limit: 1-100 (padrão: 50)\n";
echo "- offset: número (padrão: 0)\n";
echo "- include_content: true/false (padrão: true)\n\n";

echo "=== TESTE CONCLUÍDO ===\n";
?>
