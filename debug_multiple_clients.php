<?php
/**
 * Debug para verificar múltiplos clientes no sistema
 */

require_once "bootstrap.php";

echo "=== DEBUG MÚLTIPLOS CLIENTES ===\n";

try {
    $db = Database::getInstance();
    
    // 1. Verificar todos os usuários do sistema
    echo "1. USUÁRIOS NO SISTEMA:\n";
    $users = $db->fetchAll("
        SELECT id, name, email, role, status, created_at
        FROM users 
        ORDER BY created_at DESC
    ");
    
    echo "   Total de usuários: " . count($users) . "\n";
    foreach ($users as $user) {
        echo "   - ID: {$user['id']} | Nome: {$user['name']} | Email: {$user['email']} | Role: {$user['role']} | Status: {$user['status']}\n";
    }
    
    // 2. Verificar sites por usuário
    echo "\n2. SITES POR USUÁRIO:\n";
    $sites = $db->fetchAll("
        SELECT cs.id, cs.user_id, cs.site_name, cs.site_url, cs.status, u.name as user_name, u.email
        FROM client_sites cs
        LEFT JOIN users u ON cs.user_id = u.id
        ORDER BY cs.user_id, cs.created_at
    ");
    
    echo "   Total de sites: " . count($sites) . "\n";
    $sitesByUser = [];
    foreach ($sites as $site) {
        $userId = $site['user_id'];
        if (!isset($sitesByUser[$userId])) {
            $sitesByUser[$userId] = [];
        }
        $sitesByUser[$userId][] = $site;
        echo "   - Site ID: {$site['id']} | User: {$site['user_name']} ({$site['email']}) | Site: {$site['site_name']} | Status: {$site['status']}\n";
    }
    
    // 3. Verificar posts por usuário
    echo "\n3. POSTS POR USUÁRIO:\n";
    $posts = $db->fetchAll("
        SELECT 
            cp.id, 
            cp.user_id, 
            cp.site_id, 
            cp.title, 
            cp.status, 
            cp.created_at,
            u.name as user_name, 
            u.email,
            cs.site_name
        FROM client_posts cp
        LEFT JOIN users u ON cp.user_id = u.id
        LEFT JOIN client_sites cs ON cp.site_id = cs.id
        ORDER BY cp.user_id, cp.created_at DESC
    ");
    
    echo "   Total de posts: " . count($posts) . "\n";
    $postsByUser = [];
    foreach ($posts as $post) {
        $userId = $post['user_id'];
        if (!isset($postsByUser[$userId])) {
            $postsByUser[$userId] = [];
        }
        $postsByUser[$userId][] = $post;
        echo "   - Post ID: {$post['id']} | User: {$post['user_name']} ({$post['email']}) | Site: {$post['site_name']} | Status: {$post['status']} | Título: " . substr($post['title'], 0, 50) . "...\n";
    }
    
    // 4. Verificar posts pendentes especificamente
    echo "\n4. POSTS PENDENTES POR USUÁRIO:\n";
    $pendingPosts = $db->fetchAll("
        SELECT 
            cp.id, 
            cp.user_id, 
            cp.site_id, 
            cp.title, 
            cp.status, 
            cp.created_at,
            u.name as user_name, 
            u.email,
            cs.site_name
        FROM client_posts cp
        LEFT JOIN users u ON cp.user_id = u.id
        LEFT JOIN client_sites cs ON cp.site_id = cs.id
        WHERE cp.status = 'pending'
        ORDER BY cp.user_id, cp.created_at DESC
    ");
    
    echo "   Total de posts pendentes: " . count($pendingPosts) . "\n";
    $pendingByUser = [];
    foreach ($pendingPosts as $post) {
        $userId = $post['user_id'];
        if (!isset($pendingByUser[$userId])) {
            $pendingByUser[$userId] = [];
        }
        $pendingByUser[$userId][] = $post;
        echo "   - Post ID: {$post['id']} | User: {$post['user_name']} ({$post['email']}) | Site: {$post['site_name']} | Título: " . substr($post['title'], 0, 50) . "...\n";
    }
    
    // 5. Resumo estatístico
    echo "\n5. RESUMO ESTATÍSTICO:\n";
    echo "   Usuários únicos: " . count($users) . "\n";
    echo "   Usuários com sites: " . count($sitesByUser) . "\n";
    echo "   Usuários com posts: " . count($postsByUser) . "\n";
    echo "   Usuários com posts pendentes: " . count($pendingByUser) . "\n";
    
    foreach ($pendingByUser as $userId => $userPosts) {
        $userName = $userPosts[0]['user_name'];
        $userEmail = $userPosts[0]['email'];
        echo "     - $userName ($userEmail): " . count($userPosts) . " posts pendentes\n";
    }
    
    // 6. Verificar se há problema na query atual
    echo "\n6. TESTANDO QUERY ATUAL DA API:\n";
    $currentQuery = "
        SELECT DISTINCT
            cp.id,
            cp.site_id,
            cp.user_id,
            cp.title,
            cp.status,
            COALESCE(cs.site_name, 'Unknown Site') as site_name,
            COALESCE(cs.site_url, 'unknown') as site_url,
            COALESCE(u.name, 'Unknown User') as user_name,
            COALESCE(u.email, 'unknown') as user_email
        FROM client_posts cp
        LEFT JOIN client_sites cs ON cp.site_id = cs.id
        LEFT JOIN users u ON cs.user_id = u.id
        WHERE cp.status = 'pending'
        ORDER BY cp.created_at ASC
    ";
    
    $apiResult = $db->fetchAll($currentQuery);
    echo "   Resultado da query atual: " . count($apiResult) . " posts\n";
    
    $uniqueUsers = [];
    foreach ($apiResult as $post) {
        if (!in_array($post['user_email'], $uniqueUsers)) {
            $uniqueUsers[] = $post['user_email'];
        }
        echo "     - Post {$post['id']}: {$post['user_name']} ({$post['user_email']}) - {$post['site_name']}\n";
    }
    echo "   Usuários únicos na resposta da API: " . count($uniqueUsers) . "\n";
    
    // 7. Verificar se o problema é no JOIN
    echo "\n7. TESTANDO DIFERENTES TIPOS DE JOIN:\n";
    
    // Teste com INNER JOIN
    $innerJoinQuery = "
        SELECT DISTINCT
            cp.id,
            cp.user_id,
            u.name as user_name,
            u.email as user_email,
            cs.site_name
        FROM client_posts cp
        INNER JOIN client_sites cs ON cp.site_id = cs.id
        INNER JOIN users u ON cs.user_id = u.id
        WHERE cp.status = 'pending'
    ";
    
    $innerResult = $db->fetchAll($innerJoinQuery);
    echo "   INNER JOIN resultado: " . count($innerResult) . " posts\n";
    
    $innerUsers = [];
    foreach ($innerResult as $post) {
        if (!in_array($post['user_email'], $innerUsers)) {
            $innerUsers[] = $post['user_email'];
        }
    }
    echo "   Usuários únicos com INNER JOIN: " . count($innerUsers) . "\n";
    
    // Teste direto na tabela client_posts
    $directQuery = "
        SELECT DISTINCT user_id, COUNT(*) as posts_count
        FROM client_posts 
        WHERE status = 'pending'
        GROUP BY user_id
    ";
    
    $directResult = $db->fetchAll($directQuery);
    echo "   Query direta na client_posts: " . count($directResult) . " usuários únicos\n";
    foreach ($directResult as $row) {
        echo "     - User ID {$row['user_id']}: {$row['posts_count']} posts pendentes\n";
    }
    
} catch (Exception $e) {
    echo "ERRO: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}

echo "\n=== FIM DEBUG ===\n";
