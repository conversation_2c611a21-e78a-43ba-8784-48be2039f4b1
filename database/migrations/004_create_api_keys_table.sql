-- <PERSON><PERSON><PERSON> de chaves de API dos clientes
CREATE TABLE IF NOT EXISTS api_keys (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    subscription_id INT NOT NULL,
    api_key VARCHAR(255) NOT NULL UNIQUE,
    name VA<PERSON>HAR(255) NOT NULL DEFAULT 'Chave Principal',
    status ENUM('active', 'suspended', 'revoked') NOT NULL DEFAULT 'active',
    last_used_at TIMESTAMP NULL,
    usage_count INT DEFAULT 0,
    monthly_usage INT DEFAULT 0,
    monthly_limit INT DEFAULT NULL,
    allowed_domains TEXT, -- JSON array de domínios permitidos
    expires_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREI<PERSON><PERSON> KEY (subscription_id) REFERENCES subscriptions(id) ON DELETE CASCADE,
    
    INDEX idx_api_key (api_key),
    INDEX idx_user_id (user_id),
    INDEX idx_subscription_id (subscription_id),
    INDEX idx_status (status),
    INDEX idx_expires_at (expires_at),
    INDEX idx_last_used_at (last_used_at)
);
