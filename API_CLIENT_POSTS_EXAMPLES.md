# API Client Posts - Exemplos Práticos

## 🚀 Casos de Uso Reais

### 1. Dashboard de Monitoramento

**Objetivo:** Criar um dashboard que mostra posts pendentes em tempo real

```javascript
// Buscar posts pendentes para dashboard
async function getDashboardData() {
    try {
        // Posts pendentes sem conteúdo (mais <PERSON>)
        const pendingResponse = await fetch('/api/v1/client-posts?status=pending&include_content=false');
        const pendingData = await pendingResponse.json();
        
        // Estatísticas gerais
        const statsResponse = await fetch('/api/v1/client-posts/stats');
        const statsData = await statsResponse.json();
        
        return {
            pendingPosts: pendingData.data.posts,
            totalPending: pendingData.data.stats.total_posts,
            generalStats: statsData.data.general_stats
        };
    } catch (error) {
        console.error('Erro ao buscar dados:', error);
    }
}

// Atualizar dashboard a cada 30 segundos
setInterval(getDashboardData, 30000);
```

### 2. Integração com N8N

**Objetivo:** Processar posts pendentes automaticamente

```json
{
  "nodes": [
    {
      "name": "Buscar Posts Pendentes",
      "type": "HTTP Request",
      "parameters": {
        "method": "GET",
        "url": "https://app.melhorcupom.shop/api/v1/client-posts",
        "qs": {
          "status": "pending",
          "post_type": "product_review",
          "limit": "10"
        }
      }
    },
    {
      "name": "Processar Cada Post",
      "type": "Split In Batches",
      "parameters": {
        "batchSize": 1
      }
    }
  ]
}
```

### 3. Relatório Semanal

**Objetivo:** Gerar relatório de produtividade dos clientes

```python
import requests
import pandas as pd
from datetime import datetime, timedelta

def generate_weekly_report():
    # Buscar estatísticas
    stats_response = requests.get('https://app.melhorcupom.shop/api/v1/client-posts/stats')
    stats_data = stats_response.json()
    
    # Buscar todos os posts da semana
    posts_response = requests.get('https://app.melhorcupom.shop/api/v1/client-posts', params={
        'include_content': 'false',
        'limit': '100'
    })
    posts_data = posts_response.json()
    
    # Processar dados
    posts_by_client = posts_data['data']['posts_by_client']
    
    report = []
    for client in posts_by_client:
        client_info = client['client_info']
        report.append({
            'Cliente': client_info['user_name'],
            'Email': client_info['user_email'],
            'Total Posts': client['total_posts'],
            'Posts Pendentes': len([p for p in client['posts'] if p['status'] == 'pending']),
            'Posts Publicados': len([p for p in client['posts'] if p['status'] == 'published'])
        })
    
    # Criar DataFrame e salvar
    df = pd.DataFrame(report)
    df.to_excel(f'relatorio_semanal_{datetime.now().strftime("%Y%m%d")}.xlsx', index=False)
    
    return df

# Executar relatório
report = generate_weekly_report()
print(report)
```

### 4. Webhook para Slack

**Objetivo:** Notificar no Slack quando há posts pendentes

```php
<?php
function notifySlackPendingPosts() {
    // Buscar posts pendentes
    $response = file_get_contents('https://app.melhorcupom.shop/api/v1/client-posts?status=pending&limit=5');
    $data = json_decode($response, true);
    
    if (!$data['success'] || empty($data['data']['posts'])) {
        return;
    }
    
    $posts = $data['data']['posts'];
    $totalPending = $data['data']['stats']['total_posts'];
    
    // Preparar mensagem para Slack
    $message = [
        'text' => "🔔 *{$totalPending} posts pendentes para processamento*",
        'attachments' => []
    ];
    
    foreach (array_slice($posts, 0, 3) as $post) {
        $message['attachments'][] = [
            'color' => 'warning',
            'fields' => [
                [
                    'title' => 'Título',
                    'value' => $post['title'],
                    'short' => false
                ],
                [
                    'title' => 'Cliente',
                    'value' => $post['user_name'],
                    'short' => true
                ],
                [
                    'title' => 'Tipo',
                    'value' => $post['post_type'] === 'article' ? 'Artigo' : 'Review',
                    'short' => true
                ]
            ]
        ];
    }
    
    // Enviar para Slack
    $slackWebhook = 'https://hooks.slack.com/services/YOUR/WEBHOOK/URL';
    
    $ch = curl_init($slackWebhook);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($message));
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    
    $result = curl_exec($ch);
    curl_close($ch);
    
    return $result;
}

// Executar a cada hora via cron
// 0 * * * * php /path/to/slack_notification.php
notifySlackPendingPosts();
?>
```

### 5. App Mobile - Lista de Posts

**Objetivo:** Exibir posts em um app mobile

```dart
// Flutter/Dart example
import 'dart:convert';
import 'package:http/http.dart' as http;

class PostsService {
  static const String baseUrl = 'https://app.melhorcupom.shop/api/v1';
  
  Future<List<Post>> getPosts({
    String? status,
    String? postType,
    int limit = 20,
    int offset = 0,
    bool includeContent = false
  }) async {
    final queryParams = <String, String>{
      'limit': limit.toString(),
      'offset': offset.toString(),
      'include_content': includeContent.toString(),
    };
    
    if (status != null) queryParams['status'] = status;
    if (postType != null) queryParams['post_type'] = postType;
    
    final uri = Uri.parse('$baseUrl/client-posts').replace(queryParameters: queryParams);
    
    try {
      final response = await http.get(uri);
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success']) {
          return (data['data']['posts'] as List)
              .map((json) => Post.fromJson(json))
              .toList();
        }
      }
      
      throw Exception('Falha ao carregar posts');
    } catch (e) {
      throw Exception('Erro de conexão: $e');
    }
  }
  
  Future<PostStats> getStats() async {
    final response = await http.get(Uri.parse('$baseUrl/client-posts/stats'));
    
    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      if (data['success']) {
        return PostStats.fromJson(data['data']['general_stats']);
      }
    }
    
    throw Exception('Falha ao carregar estatísticas');
  }
}

class Post {
  final int id;
  final String title;
  final String status;
  final String postType;
  final String userName;
  final String siteName;
  final DateTime createdAt;
  
  Post({
    required this.id,
    required this.title,
    required this.status,
    required this.postType,
    required this.userName,
    required this.siteName,
    required this.createdAt,
  });
  
  factory Post.fromJson(Map<String, dynamic> json) {
    return Post(
      id: json['id'],
      title: json['title'],
      status: json['status'],
      postType: json['post_type'],
      userName: json['user_name'],
      siteName: json['site_name'],
      createdAt: DateTime.parse(json['created_at']),
    );
  }
}
```

### 6. Excel/Google Sheets Integration

**Objetivo:** Importar dados para planilhas

```javascript
// Google Apps Script
function importPostsData() {
  const sheet = SpreadsheetApp.getActiveSheet();
  
  // Limpar dados existentes
  sheet.clear();
  
  // Buscar dados da API
  const response = UrlFetchApp.fetch('https://app.melhorcupom.shop/api/v1/client-posts?include_content=false&limit=100');
  const data = JSON.parse(response.getContentText());
  
  if (!data.success) {
    throw new Error('Erro ao buscar dados: ' + data.error);
  }
  
  // Cabeçalhos
  const headers = ['ID', 'Título', 'Tipo', 'Status', 'Cliente', 'Site', 'Criado em'];
  sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
  
  // Dados dos posts
  const posts = data.data.posts;
  const rows = posts.map(post => [
    post.id,
    post.title,
    post.post_type === 'article' ? 'Artigo' : 'Review',
    post.status,
    post.user_name,
    post.site_name,
    new Date(post.created_at)
  ]);
  
  if (rows.length > 0) {
    sheet.getRange(2, 1, rows.length, headers.length).setValues(rows);
  }
  
  // Formatação
  sheet.getRange(1, 1, 1, headers.length).setFontWeight('bold');
  sheet.autoResizeColumns(1, headers.length);
}

// Executar automaticamente a cada hora
function createTrigger() {
  ScriptApp.newTrigger('importPostsData')
    .timeBased()
    .everyHours(1)
    .create();
}
```

## 🔧 Dicas de Performance

### 1. Paginação Eficiente
```bash
# Primeira página
curl "https://app.melhorcupom.shop/api/v1/client-posts?limit=20&offset=0"

# Verificar se há mais dados
# Se stats.has_more = true, buscar próxima página
curl "https://app.melhorcupom.shop/api/v1/client-posts?limit=20&offset=20"
```

### 2. Filtros Combinados
```bash
# Buscar reviews pendentes de um cliente específico
curl "https://app.melhorcupom.shop/api/v1/client-posts?status=pending&post_type=product_review&user_id=4"
```

### 3. Consultas Rápidas
```bash
# Para listas (sem conteúdo)
curl "https://app.melhorcupom.shop/api/v1/client-posts?include_content=false&limit=50"

# Para detalhes (com conteúdo)
curl "https://app.melhorcupom.shop/api/v1/client-posts/123"
```

## 📊 Monitoramento

### Script de Health Check
```bash
#!/bin/bash
# health_check.sh

API_URL="https://app.melhorcupom.shop/api/v1/client-posts/stats"

response=$(curl -s -w "%{http_code}" "$API_URL")
http_code="${response: -3}"

if [ "$http_code" -eq 200 ]; then
    echo "✅ API está funcionando"
    exit 0
else
    echo "❌ API com problemas - HTTP $http_code"
    exit 1
fi
```

Estes exemplos mostram como integrar a API em diferentes cenários reais de uso!
