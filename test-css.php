<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste CSS - E1Copy AI</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" onerror="this.onerror=null;this.href='<?= url('/assets/css/login.css') ?>';">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet" onerror="this.remove();">
    
    <style>
        body {
            padding: 2rem;
            background: #f8f9fa;
        }
        .test-card {
            background: white;
            border-radius: 0.5rem;
            padding: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="test-card">
                    <h1 class="text-center mb-4">
                        <i class="fas fa-check-circle text-success me-2"></i>
                        Teste de CSS - E1Copy AI
                    </h1>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Teste de Carregamento:</strong> Se você está vendo esta página com estilos, o CSS está funcionando corretamente.
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h5><i class="fas fa-cog me-2"></i>Bootstrap</h5>
                            <p>Se esta página está bem formatada, o Bootstrap carregou corretamente.</p>
                            
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-primary">
                                    <i class="fas fa-thumbs-up me-1"></i>
                                    Primário
                                </button>
                                <button type="button" class="btn btn-success">
                                    <i class="fas fa-check me-1"></i>
                                    Sucesso
                                </button>
                                <button type="button" class="btn btn-warning">
                                    <i class="fas fa-exclamation-triangle me-1"></i>
                                    Aviso
                                </button>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <h5><i class="fas fa-icons me-2"></i>Font Awesome</h5>
                            <p>Se você vê ícones ao lado dos textos, o Font Awesome carregou.</p>
                            
                            <div class="d-flex flex-wrap gap-2">
                                <span class="badge bg-primary">
                                    <i class="fas fa-user me-1"></i>
                                    Usuário
                                </span>
                                <span class="badge bg-success">
                                    <i class="fas fa-shield-alt me-1"></i>
                                    Seguro
                                </span>
                                <span class="badge bg-info">
                                    <i class="fas fa-robot me-1"></i>
                                    IA
                                </span>
                                <span class="badge bg-warning">
                                    <i class="fas fa-key me-1"></i>
                                    API
                                </span>
                            </div>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Teste de Formulário</h6>
                            <div class="mb-3">
                                <label class="form-label">Email</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-envelope"></i>
                                    </span>
                                    <input type="email" class="form-control" placeholder="<EMAIL>">
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Senha</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-lock"></i>
                                    </span>
                                    <input type="password" class="form-control" placeholder="Sua senha">
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <h6>Status do Sistema</h6>
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Servidor funcionando
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    PHP <?= PHP_VERSION ?>
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Aplicação carregada
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-clock text-info me-2"></i>
                                    <?= date('d/m/Y H:i:s') ?>
                                </li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="text-center mt-4">
                        <a href="<?= url('/login') ?>" class="btn btn-outline-primary me-2">
                            <i class="fas fa-sign-in-alt me-1"></i>
                            Login Cliente
                        </a>
                        <a href="<?= url('/admin/login') ?>" class="btn btn-outline-danger">
                            <i class="fas fa-shield-alt me-1"></i>
                            Login Admin
                        </a>
                    </div>
                </div>
                
                <div class="test-card">
                    <h5><i class="fas fa-code me-2"></i>Informações Técnicas</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <strong>Headers de Segurança:</strong>
                            <ul class="small">
                                <li>X-Content-Type-Options: nosniff</li>
                                <li>X-Frame-Options: SAMEORIGIN</li>
                                <li>X-XSS-Protection: 1; mode=block</li>
                                <li>Content-Security-Policy: Configurado</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <strong>Recursos Carregados:</strong>
                            <ul class="small">
                                <li>Bootstrap 5.3.0</li>
                                <li>Font Awesome 6.4.0</li>
                                <li>CSS Local (fallback)</li>
                                <li>JavaScript (fallback)</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js" onerror="console.warn('Bootstrap JS não carregou')"></script>
    <!-- Script de fallback -->
    <script src="<?= url('/assets/js/fallback.js') ?>"></script>
    
    <script>
        // Teste adicional
        document.addEventListener('DOMContentLoaded', function() {
            console.log('✓ DOM carregado');
            console.log('✓ Teste de CSS concluído');
            
            // Verificar se Bootstrap está disponível
            if (typeof bootstrap !== 'undefined') {
                console.log('✓ Bootstrap JS carregado');
            } else {
                console.warn('⚠ Bootstrap JS não disponível');
            }
        });
    </script>
</body>
</html>
