# Endpoint /api/v1/client-posts Ajustado

## 🔍 Endpoint Ajustado

**URL:** `/api/v1/client-posts?status=pending`
**Método:** GET
**Função:** Listar posts de TODOS os clientes ativos com filtros opcionais

## ✅ Ajustes Aplicados

### 1. **Filtro de Usuários Ativos Obrigatório**

**ANTES:**
```php
$conditions = ['1=1'];  // ❌ Sem filtro de usuários
```

**DEPOIS:**
```php
$conditions = ['u.status = \'active\''];  // ✅ Apenas usuários ativos
```

### 2. **Query Melhorada com DISTINCT**

**ANTES:**
```sql
SELECT cp.*, cs.site_name, cs.site_url, u.name, u.email
FROM client_posts cp
LEFT JOIN client_sites cs ON cp.site_id = cs.id
LEFT JOIN users u ON cp.user_id = u.id
```

**DEPOIS:**
```sql
SELECT DISTINCT
    cp.id, cp.site_id, cp.user_id, cp.post_type, cp.title,
    cp.content, cp.excerpt, cp.product_name, cp.product_description,
    cp.keyword, cp.category, cp.existing_tags, cp.youtube_video,
    cp.product_images, cp.post_cover, cp.slug, cp.status,
    cp.created_at, cp.updated_at,
    COALESCE(cs.site_name, 'Unknown Site') as site_name,
    COALESCE(cs.site_url, 'unknown') as site_url,
    COALESCE(u.name, 'Unknown User') as user_name,
    COALESCE(u.email, 'unknown') as user_email
FROM client_posts cp
LEFT JOIN client_sites cs ON cp.site_id = cs.id
LEFT JOIN users u ON cp.user_id = u.id
WHERE u.status = 'active' AND [filtros...]
```

### 3. **API Keys Incluídas**

- ✅ Busca separada de API keys para evitar duplicações
- ✅ Mapeamento `site_id → api_key`
- ✅ Incluída em cada post e na organização por site

### 4. **Organização Melhorada**

**ANTES:**
```php
// Organizava por site_id e user_id (números)
$siteKey = $post['site_id'];
$clientKey = $post['user_id'];
```

**DEPOIS:**
```php
// Organiza por email e URL (únicos e legíveis)
$clientEmail = $post['user_email'];  // ✅ Chave única por cliente
$siteUrl = $post['site_url'];        // ✅ Chave única por site
```

### 5. **Logs de Debug Adicionados**

- ✅ Log de filtros aplicados
- ✅ Contagem de clientes únicos
- ✅ Contagem de sites únicos
- ✅ Posts por cliente

## 📊 Parâmetros Suportados

| Parâmetro | Tipo | Descrição | Exemplo |
|-----------|------|-----------|---------|
| `status` | string | Filtrar por status | `?status=pending` |
| `post_type` | string | Filtrar por tipo | `?post_type=product_review` |
| `user_id` | int | Filtrar por usuário específico | `?user_id=4` |
| `site_id` | int | Filtrar por site específico | `?site_id=6` |
| `limit` | int | Limite de resultados (max 100) | `?limit=50` |
| `offset` | int | Offset para paginação | `?offset=0` |

## 🧪 Exemplos de Uso

### 1. **Posts Pendentes de TODOS os Clientes**
```bash
curl -X GET "https://app.melhorcupom.shop/api/v1/client-posts?status=pending"
```

### 2. **Product Reviews Pendentes**
```bash
curl -X GET "https://app.melhorcupom.shop/api/v1/client-posts?status=pending&post_type=product_review"
```

### 3. **Posts de um Cliente Específico**
```bash
curl -X GET "https://app.melhorcupom.shop/api/v1/client-posts?user_id=4"
```

### 4. **Posts com Paginação**
```bash
curl -X GET "https://app.melhorcupom.shop/api/v1/client-posts?status=pending&limit=20&offset=0"
```

## 📋 Resposta Esperada

```json
{
  "success": true,
  "message": "Sucesso",
  "data": {
    "posts": [
      {
        "id": 13,
        "user_id": 4,
        "site_id": 6,
        "title": "Post Title",
        "status": "pending",
        "post_type": "product_review",
        "user_name": "E1Cursos",
        "user_email": "<EMAIL>",
        "site_name": "e1cursos.com",
        "site_url": "https://e1cursos.com",
        "api_key": "e1copy_abc123...",
        "product_images": "[...]",
        "created_at": "2025-06-23 01:13:07"
      }
    ],
    "posts_by_client": [
      {
        "client_info": {
          "user_id": 4,
          "user_name": "E1Cursos",
          "user_email": "<EMAIL>"
        },
        "posts": [...],
        "total_posts": 2
      }
    ],
    "posts_by_site": [
      {
        "site_info": {
          "site_id": 6,
          "site_name": "e1cursos.com",
          "site_url": "https://e1cursos.com",
          "api_key": "e1copy_abc123..."
        },
        "posts": [...],
        "total_posts": 2
      }
    ],
    "pagination": {
      "total": 5,
      "limit": 50,
      "offset": 0,
      "has_more": false
    },
    "stats": {
      "total": 5,
      "drafts": 0,
      "pending": 5,
      "processing": 0,
      "completed": 0,
      "published": 0,
      "failed": 0,
      "articles": 2,
      "reviews": 3,
      "unique_clients": 3,
      "unique_sites": 4,
      "posts_returned": 5
    },
    "filters": {
      "status": "pending",
      "post_type": "",
      "user_id": "",
      "site_id": ""
    },
    "timestamp": "2025-06-23 18:30:00"
  }
}
```

## 🔍 Logs de Debug

O endpoint agora inclui logs detalhados:

```
=== getClientPosts AJUSTADO ===
Filtro status: pending
WHERE clause: u.status = 'active' AND cp.status = :status
Posts encontrados: 5
Total posts: 5
Organização getClientPosts:
- Clientes únicos: 3
- Sites únicos: 4
- Cliente <EMAIL>: 2 posts
- Cliente <EMAIL>: 2 posts
- Cliente <EMAIL>: 1 posts
=== getClientPosts RESPOSTA ===
Posts retornados: 5
Clientes únicos: 3
Sites únicos: 4
Status filtrado: pending
```

## ✅ Benefícios dos Ajustes

### 1. **Multi-Tenant Correto**
- ✅ Retorna posts de TODOS os clientes ativos
- ✅ Filtro obrigatório `u.status = 'active'`
- ✅ Organização por cliente e site

### 2. **Dados Completos**
- ✅ API keys incluídas para cada site
- ✅ Informações completas de usuário e site
- ✅ URLs de imagens processadas

### 3. **Flexibilidade**
- ✅ Filtros opcionais por status, tipo, usuário, site
- ✅ Paginação configurável
- ✅ Estatísticas detalhadas

### 4. **Compatibilidade N8N**
- ✅ Estrutura organizada por cliente e site
- ✅ Múltiplos clientes identificados
- ✅ Dados únicos e confiáveis

## 🎯 **ENDPOINT AJUSTADO COM SUCESSO!**

### **Principais Melhorias:**
- ✅ **Filtro obrigatório** de usuários ativos
- ✅ **DISTINCT** para evitar duplicações
- ✅ **API keys incluídas** em cada site
- ✅ **Organização melhorada** por email e URL
- ✅ **Logs detalhados** para debug
- ✅ **Estatísticas expandidas** com contadores únicos

### **Resultado:**
- ✅ `/api/v1/client-posts?status=pending` retorna posts de TODOS os clientes
- ✅ Dados organizados e completos
- ✅ Compatível com N8N
- ✅ Sistema multi-tenant funcionando corretamente

**🎉 O endpoint `/api/v1/client-posts` agora funciona corretamente para um sistema multi-tenant!**
