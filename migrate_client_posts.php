<?php
/**
 * <PERSON>ript para adicionar campos de imagem à tabela client_posts
 */

require_once __DIR__ . '/bootstrap.php';

try {
    $db = Database::getInstance();
    $pdo = $db->getConnection();
    
    echo "=== Migração da tabela client_posts ===\n";
    
    // Verificar se a tabela existe
    $tableExists = $pdo->query("SHOW TABLES LIKE 'client_posts'")->fetch();
    if (!$tableExists) {
        echo "❌ Tabela client_posts não existe. Criando tabela...\n";
        
        // Criar tabela completa
        $createTableSQL = "
        CREATE TABLE client_posts (
            id INT AUTO_INCREMENT PRIMARY KEY,
            site_id INT NOT NULL,
            user_id INT NOT NULL,
            post_type ENUM('article', 'product_review') NOT NULL,
            title VARCHAR(500) NOT NULL,
            content LONGTEXT,
            product_description LONGTEXT NULL,
            post_cover TEXT NULL,
            keyword VARCHAR(255) NULL,
            excerpt TEXT,
            keywords TEXT,
            category VARCHAR(255),
            tags TEXT,
            existing_tags TEXT NULL,
            youtube_video TEXT NULL,
            product_images TEXT NULL,
            slug VARCHAR(255),
            
            -- Campos específicos para produto review
            product_name VARCHAR(255) NULL,
            product_url TEXT NULL,
            product_price DECIMAL(10,2) NULL,
            product_rating DECIMAL(2,1) NULL,
            product_pros TEXT NULL,
            product_cons TEXT NULL,
            affiliate_link TEXT NULL,
            
            -- Status e controle
            status ENUM('draft', 'pending', 'processing', 'completed', 'failed', 'published') DEFAULT 'draft',
            wordpress_post_id INT NULL,
            error_message TEXT NULL,
            
            -- Timestamps
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            published_at TIMESTAMP NULL,
            
            -- Indexes
            INDEX idx_site_status (site_id, status),
            INDEX idx_user_status (user_id, status),
            INDEX idx_post_type (post_type),
            INDEX idx_created_at (created_at)
        )";
        
        $pdo->exec($createTableSQL);
        echo "✓ Tabela client_posts criada com sucesso!\n";
    } else {
        echo "✓ Tabela client_posts já existe. Verificando campos...\n";
        
        // Verificar quais campos existem
        $columns = $pdo->query("DESCRIBE client_posts")->fetchAll(PDO::FETCH_ASSOC);
        $existingFields = array_column($columns, 'Field');
        
        $fieldsToAdd = [
            'product_description' => 'LONGTEXT NULL AFTER content',
            'post_cover' => 'TEXT NULL AFTER product_description',
            'keyword' => 'VARCHAR(255) NULL AFTER post_cover',
            'existing_tags' => 'TEXT NULL AFTER tags',
            'youtube_video' => 'TEXT NULL AFTER existing_tags',
            'product_images' => 'TEXT NULL AFTER youtube_video'
        ];
        
        foreach ($fieldsToAdd as $field => $definition) {
            if (!in_array($field, $existingFields)) {
                echo "   Adicionando campo '$field'...\n";
                $sql = "ALTER TABLE client_posts ADD COLUMN $field $definition";
                $pdo->exec($sql);
                echo "   ✓ Campo '$field' adicionado!\n";
            } else {
                echo "   ✓ Campo '$field' já existe\n";
            }
        }
    }
    
    // Verificar estrutura final
    echo "\n=== Estrutura final da tabela ===\n";
    $columns = $pdo->query("DESCRIBE client_posts")->fetchAll(PDO::FETCH_ASSOC);
    
    $imageFields = ['product_images', 'post_cover'];
    foreach ($columns as $column) {
        if (in_array($column['Field'], $imageFields)) {
            echo "✓ {$column['Field']}: {$column['Type']}\n";
        }
    }
    
    echo "\n🎉 Migração concluída com sucesso!\n";
    
} catch (Exception $e) {
    echo "❌ Erro durante a migração: " . $e->getMessage() . "\n";
    exit(1);
}
?>
