<?php
/**
 * Script para testar e verificar imagens dos posts
 */

// Configuração direta do banco para teste
$host = 'srv1845.hstgr.io';
$database = 'u880879026_appDash';
$username = 'u880879026_userDash';
$password = ':sX=zys@2';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    echo "=== TESTE DE IMAGENS DOS POSTS ===\n\n";
    echo "✅ Conectado ao banco de dados em $host\n\n";
    
    // 1. Verificar estrutura da tabela
    echo "1. Verificando estrutura da tabela...\n";
    $stmt = $pdo->query("DESCRIBE client_posts");
    $columns = $stmt->fetchAll();
    $fields = array_column($columns, 'Field');
    
    $imageFields = ['product_images', 'post_cover'];
    foreach ($imageFields as $field) {
        if (in_array($field, $fields)) {
            echo "✅ Campo '$field' existe\n";
        } else {
            echo "❌ Campo '$field' NÃO existe - ADICIONANDO...\n";
            try {
                $pdo->exec("ALTER TABLE client_posts ADD COLUMN $field TEXT NULL");
                echo "✅ Campo '$field' adicionado com sucesso\n";
            } catch (Exception $e) {
                echo "❌ Erro ao adicionar campo '$field': " . $e->getMessage() . "\n";
            }
        }
    }
    
    // 2. Verificar posts existentes
    echo "\n2. Verificando posts existentes...\n";
    $stmt = $pdo->query("SELECT id, title, product_images, post_cover, status FROM client_posts ORDER BY id DESC LIMIT 5");
    $posts = $stmt->fetchAll();
    
    if (empty($posts)) {
        echo "❌ Nenhum post encontrado\n";
    } else {
        foreach ($posts as $post) {
            echo "Post ID {$post['id']}: " . substr($post['title'], 0, 50) . "...\n";
            echo "  - Status: {$post['status']}\n";
            echo "  - product_images: " . ($post['product_images'] ?? 'NULL') . "\n";
            echo "  - post_cover: " . ($post['post_cover'] ?? 'NULL') . "\n";
            echo "  ---\n";
        }
    }
    
    // 3. Testar inserção de dados de teste
    echo "\n3. Testando inserção de dados de teste...\n";
    
    $testImages = [
        '/app/uploads/posts/test/image1.jpg',
        '/app/uploads/posts/test/image2.jpg',
        '/app/uploads/posts/test/image3.jpg'
    ];
    
    $testCover = '/app/uploads/posts/test/cover.jpg';
    
    // Verificar se existe um post para atualizar
    $stmt = $pdo->query("SELECT id FROM client_posts LIMIT 1");
    $testPost = $stmt->fetch();
    
    if ($testPost) {
        echo "Atualizando post ID {$testPost['id']} com dados de teste...\n";
        
        $stmt = $pdo->prepare("UPDATE client_posts SET product_images = ?, post_cover = ? WHERE id = ?");
        $updateResult = $stmt->execute([
            json_encode($testImages),
            $testCover,
            $testPost['id']
        ]);
        
        if ($updateResult) {
            echo "✅ Post atualizado com sucesso\n";
            
            // Verificar se foi salvo corretamente
            $stmt = $pdo->prepare("SELECT product_images, post_cover FROM client_posts WHERE id = ?");
            $stmt->execute([$testPost['id']]);
            $updatedPost = $stmt->fetch();
            echo "Dados salvos:\n";
            echo "  - product_images: " . $updatedPost['product_images'] . "\n";
            echo "  - post_cover: " . $updatedPost['post_cover'] . "\n";
        } else {
            echo "❌ Erro ao atualizar post\n";
        }
    } else {
        echo "ℹ️ Nenhum post encontrado para teste\n";
    }
    
    echo "\n=== TESTE CONCLUÍDO ===\n";
    
} catch (Exception $e) {
    echo "❌ Erro: " . $e->getMessage() . "\n";
}
?>
