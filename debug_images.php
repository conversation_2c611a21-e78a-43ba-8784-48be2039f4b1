<?php
/**
 * Script para debugar o problema das imagens
 */

require_once __DIR__ . '/bootstrap.php';

try {
    $db = Database::getInstance();
    
    echo "=== DEBUG DE IMAGENS ===\n\n";
    
    // 1. Verificar estrutura da tabela
    echo "1. Verificando estrutura da tabela...\n";
    $columns = $db->fetchAll("DESCRIBE client_posts");
    
    $imageFields = ['product_images', 'post_cover'];
    foreach ($imageFields as $field) {
        $found = false;
        foreach ($columns as $column) {
            if ($column['Field'] === $field) {
                echo "✅ Campo '$field' existe: {$column['Type']}\n";
                $found = true;
                break;
            }
        }
        if (!$found) {
            echo "❌ Campo '$field' NÃO existe\n";
        }
    }
    
    // 2. Verificar posts existentes
    echo "\n2. Verificando posts existentes...\n";
    $posts = $db->fetchAll("SELECT id, title, product_images, post_cover, status FROM client_posts ORDER BY id DESC LIMIT 5");
    
    foreach ($posts as $post) {
        echo "Post ID {$post['id']}: " . substr($post['title'], 0, 50) . "...\n";
        echo "  - Status: {$post['status']}\n";
        echo "  - product_images: " . ($post['product_images'] ?? 'NULL') . "\n";
        echo "  - post_cover: " . ($post['post_cover'] ?? 'NULL') . "\n";
        echo "  ---\n";
    }
    
    // 3. Verificar arquivos físicos
    echo "\n3. Verificando arquivos físicos...\n";
    $uploadDir = __DIR__ . '/uploads/posts';
    if (is_dir($uploadDir)) {
        $postDirs = glob($uploadDir . '/*', GLOB_ONLYDIR);
        foreach ($postDirs as $postDir) {
            $postId = basename($postDir);
            $files = glob($postDir . '/*');
            echo "Post ID $postId: " . count($files) . " arquivos\n";
            foreach ($files as $file) {
                $fileName = basename($file);
                $size = filesize($file);
                echo "  - $fileName (" . round($size/1024, 2) . " KB)\n";
            }
        }
    } else {
        echo "❌ Diretório de uploads não existe\n";
    }
    
    // 4. Testar inserção manual
    echo "\n4. Testando inserção manual...\n";
    
    // Verificar se existe post ID 1 com arquivos
    $post1 = $db->fetch("SELECT id, product_images, post_cover FROM client_posts WHERE id = 1");
    if ($post1) {
        echo "Post ID 1 encontrado:\n";
        echo "  - product_images atual: " . ($post1['product_images'] ?? 'NULL') . "\n";
        echo "  - post_cover atual: " . ($post1['post_cover'] ?? 'NULL') . "\n";
        
        // Verificar se há arquivos físicos para este post
        $postDir = __DIR__ . '/uploads/posts/1';
        if (is_dir($postDir)) {
            $imageFiles = glob($postDir . '/image_*.{jpg,jpeg,png,webp}', GLOB_BRACE);
            $coverFiles = glob($postDir . '/cover_*.{jpg,jpeg,png,webp}', GLOB_BRACE);
            
            if (!empty($imageFiles)) {
                echo "  - Arquivos de imagem encontrados: " . count($imageFiles) . "\n";
                
                // Gerar URLs para as imagens
                $imageUrls = [];
                foreach ($imageFiles as $file) {
                    $fileName = basename($file);
                    $imageUrls[] = '/app/uploads/posts/1/' . $fileName;
                }
                
                echo "  - URLs geradas: " . json_encode($imageUrls) . "\n";
                
                // Atualizar no banco
                $updateData = ['product_images' => json_encode($imageUrls)];
                
                if (!empty($coverFiles)) {
                    $coverFileName = basename($coverFiles[0]);
                    $coverUrl = '/app/uploads/posts/1/' . $coverFileName;
                    $updateData['post_cover'] = $coverUrl;
                    echo "  - URL da capa: $coverUrl\n";
                }
                
                $result = $db->update('client_posts', $updateData, 'id = :id', ['id' => 1]);
                if ($result) {
                    echo "✅ Post atualizado com sucesso!\n";
                    
                    // Verificar se foi salvo
                    $updatedPost = $db->fetch("SELECT product_images, post_cover FROM client_posts WHERE id = 1");
                    echo "  - Dados salvos:\n";
                    echo "    - product_images: " . $updatedPost['product_images'] . "\n";
                    echo "    - post_cover: " . $updatedPost['post_cover'] . "\n";
                } else {
                    echo "❌ Erro ao atualizar post\n";
                }
            } else {
                echo "  - Nenhum arquivo de imagem encontrado\n";
            }
        } else {
            echo "  - Diretório do post não existe\n";
        }
    } else {
        echo "Post ID 1 não encontrado\n";
    }
    
    echo "\n=== FIM DEBUG ===\n";
    
} catch (Exception $e) {
    echo "❌ Erro: " . $e->getMessage() . "\n";
}
?>
