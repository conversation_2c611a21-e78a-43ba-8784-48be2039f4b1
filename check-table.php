<?php
// Este script verifica a estrutura da tabela e1copy_my_posts

// Definir constantes para evitar erros
define('ABSPATH', dirname(__FILE__) . '/');
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);

// Definir manualmente as configurações do banco de dados
define('DB_NAME', 'melhorcupom_shop');
define('DB_USER', 'melhorcupom_shop');
define('DB_PASSWORD', 'Esmael@2023');
define('DB_HOST', 'localhost');
define('DB_CHARSET', 'utf8mb4');
define('DB_COLLATE', '');

// Definir o prefixo da tabela
$table_prefix = 'wp_';

// Conectar ao banco de dados manualmente
$mysqli = new mysqli(DB_HOST, DB_USER, DB_PASSWORD, DB_NAME);

// Verificar conexão
if ($mysqli->connect_error) {
    die("Falha na conexão: " . $mysqli->connect_error);
}

// Nome da tabela
$table_name = $table_prefix . 'e1copy_my_posts';

// Verificar se a tabela existe
$result = $mysqli->query("SHOW TABLES LIKE '$table_name'");
if ($result->num_rows == 0) {
    die("A tabela $table_name não existe.");
}

// Obter a estrutura da tabela
$result = $mysqli->query("DESCRIBE $table_name");
if (!$result) {
    die("Erro ao obter a estrutura da tabela: " . $mysqli->error);
}

// Exibir a estrutura da tabela
echo "Estrutura da tabela $table_name:\n\n";
echo "| Campo | Tipo | Nulo | Chave | Padrão | Extra |\n";
echo "|-------|------|------|-------|--------|-------|\n";

$columns = [];
while ($row = $result->fetch_assoc()) {
    echo "| {$row['Field']} | {$row['Type']} | {$row['Null']} | {$row['Key']} | {$row['Default']} | {$row['Extra']} |\n";
    $columns[] = $row;
}

// Verificar especificamente a coluna 'post_type'
$post_type_exists = false;
foreach ($columns as $column) {
    if ($column['Field'] === 'post_type') {
        $post_type_exists = true;
        echo "\nA coluna 'post_type' existe na tabela.\n";
        break;
    }
}

if (!$post_type_exists) {
    echo "\nA coluna 'post_type' NÃO existe na tabela.\n";

    // Adicionar a coluna se não existir
    $result = $mysqli->query("ALTER TABLE $table_name ADD COLUMN post_type varchar(50) DEFAULT 'blog' AFTER id");
    if ($result) {
        echo "Coluna 'post_type' adicionada à tabela.\n";
    } else {
        echo "Erro ao adicionar a coluna 'post_type': " . $mysqli->error . "\n";
    }
}

// Verificar especificamente a coluna 'affiliation_link'
$affiliation_link_exists = false;
foreach ($columns as $column) {
    if ($column['Field'] === 'affiliation_link') {
        $affiliation_link_exists = true;
        echo "\nA coluna 'affiliation_link' existe na tabela.\n";
        break;
    }
}

if (!$affiliation_link_exists) {
    echo "\nA coluna 'affiliation_link' NÃO existe na tabela.\n";

    // Adicionar a coluna se não existir
    $result = $mysqli->query("ALTER TABLE $table_name ADD COLUMN affiliation_link text DEFAULT ''");
    if ($result) {
        echo "Coluna 'affiliation_link' adicionada à tabela.\n";
    } else {
        echo "Erro ao adicionar a coluna 'affiliation_link': " . $mysqli->error . "\n";
    }
}

// Verificar especificamente a coluna 'product_name'
$product_name_exists = false;
foreach ($columns as $column) {
    if ($column['Field'] === 'product_name') {
        $product_name_exists = true;
        echo "\nA coluna 'product_name' existe na tabela.\n";
        break;
    }
}

if (!$product_name_exists) {
    echo "\nA coluna 'product_name' NÃO existe na tabela.\n";

    // Adicionar a coluna se não existir
    $result = $mysqli->query("ALTER TABLE $table_name ADD COLUMN product_name varchar(255) DEFAULT ''");
    if ($result) {
        echo "Coluna 'product_name' adicionada à tabela.\n";
    } else {
        echo "Erro ao adicionar a coluna 'product_name': " . $mysqli->error . "\n";
    }
}

// Verificar especificamente a coluna 'product_link'
$product_link_exists = false;
foreach ($columns as $column) {
    if ($column['Field'] === 'product_link') {
        $product_link_exists = true;
        echo "\nA coluna 'product_link' existe na tabela.\n";
        break;
    }
}

if (!$product_link_exists) {
    echo "\nA coluna 'product_link' NÃO existe na tabela.\n";

    // Adicionar a coluna se não existir
    $result = $mysqli->query("ALTER TABLE $table_name ADD COLUMN product_link text DEFAULT ''");
    if ($result) {
        echo "Coluna 'product_link' adicionada à tabela.\n";
    } else {
        echo "Erro ao adicionar a coluna 'product_link': " . $mysqli->error . "\n";
    }
}

// Verificar especificamente a coluna 'cta_image'
$cta_image_exists = false;
foreach ($columns as $column) {
    if ($column['Field'] === 'cta_image') {
        $cta_image_exists = true;
        echo "\nA coluna 'cta_image' existe na tabela.\n";
        break;
    }
}

if (!$cta_image_exists) {
    echo "\nA coluna 'cta_image' NÃO existe na tabela.\n";

    // Adicionar a coluna se não existir
    $result = $mysqli->query("ALTER TABLE $table_name ADD COLUMN cta_image text DEFAULT ''");
    if ($result) {
        echo "Coluna 'cta_image' adicionada à tabela.\n";
    } else {
        echo "Erro ao adicionar a coluna 'cta_image': " . $mysqli->error . "\n";
    }
}

// Verificar os dados na tabela
$result = $mysqli->query("SELECT * FROM $table_name ORDER BY id DESC LIMIT 5");
if (!$result) {
    die("Erro ao obter os dados da tabela: " . $mysqli->error);
}

echo "\n\nDados recentes da tabela (últimos 5 registros):\n\n";
while ($row = $result->fetch_assoc()) {
    echo "ID: {$row['id']}\n";
    echo "Título: {$row['title']}\n";
    echo "Tipo: " . (isset($row['post_type']) ? $row['post_type'] : 'não definido') . "\n";
    echo "Processado: " . ($row['processed'] ? 'Sim' : 'Não') . "\n";

    if (isset($row['affiliation_link'])) {
        echo "Link de Afiliação: {$row['affiliation_link']}\n";
    } else {
        echo "Link de Afiliação: não definido\n";
    }

    echo "-------------------\n";
}

// Fechar conexão
$mysqli->close();
