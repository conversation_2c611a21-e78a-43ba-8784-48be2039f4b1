<?php
/**
 * Controller da Área do Cliente
 * Sistema de Dashboard E1Copy AI
 */

class ClientController extends Controller {
    
    public function dashboard() {
        $user = Auth::user();
        
        // Buscar dados da assinatura ativa
        $subscription = $this->db->fetch(
            "SELECT s.*, p.name as plan_name, p.limits_per_month, p.max_sites
             FROM subscriptions s
             JOIN plans p ON s.plan_id = p.id
             WHERE s.user_id = :user_id AND s.status = 'active'
             ORDER BY s.created_at DESC
             LIMIT 1",
            ['user_id' => $user['id']]
        );
        
        // Buscar sites do cliente com suas licenças
        $sites = $this->db->fetchAll(
            "SELECT cs.*, ak.name as api_key_name, ak.status as api_key_status, ak.api_key
             FROM client_sites cs
             LEFT JOIN api_keys ak ON cs.api_key_id = ak.id
             WHERE cs.user_id = :user_id
             ORDER BY cs.created_at DESC",
            ['user_id' => $user['id']]
        );

        // Buscar chaves de API (manter para compatibilidade)
        $apiKeys = $this->db->fetchAll(
            "SELECT * FROM api_keys
             WHERE user_id = :user_id
             ORDER BY created_at DESC",
            ['user_id' => $user['id']]
        );
        
        // Buscar estatísticas de uso
        $stats = [];
        if (!empty($apiKeys)) {
            $keyIds = array_column($apiKeys, 'id');
            $placeholders = str_repeat('?,', count($keyIds) - 1) . '?';
            
            $stats = $this->db->fetch(
                "SELECT 
                    COUNT(*) as total_requests,
                    COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as today_requests,
                    COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as month_requests
                 FROM api_usage 
                 WHERE api_key_id IN ($placeholders)",
                $keyIds
            );
        }
        
        $this->view('client.dashboard', [
            'user' => $user,
            'subscription' => $subscription,
            'sites' => $sites,
            'apiKeys' => $apiKeys,
            'stats' => $stats
        ]);
    }
    
    public function account() {
        $user = Auth::user();
        
        $this->view('client.account', [
            'user' => $user
        ]);
    }
    
    public function updateAccount() {
        // Verificar CSRF token
        if (!$this->verifyCsrfToken($this->input('_token'))) {
            $this->flash('error', 'Token de segurança inválido');
            return redirect(url('/client/account'));
        }
        
        // Validar dados
        $validation = $this->validate([
            'name' => 'required|min:2|max:255',
            'email' => 'required|email|max:255'
        ]);
        
        if ($validation !== true) {
            return $this->withErrors($validation)->withInput()->redirect(url('/client/account'));
        }
        
        $user = Auth::user();
        $name = $this->input('name');
        $email = $this->input('email');
        $currentPassword = $this->input('current_password');
        $newPassword = $this->input('new_password');
        $confirmPassword = $this->input('confirm_password');
        
        // Verificar se o email já existe para outro usuário
        if ($email !== $user['email']) {
            $existingUser = $this->db->fetch(
                "SELECT id FROM users WHERE email = :email AND id != :id",
                ['email' => $email, 'id' => $user['id']]
            );
            
            if ($existingUser) {
                $this->flash('error', 'Este email já está sendo usado por outro usuário');
                return $this->withInput()->redirect(url('/client/account'));
            }
        }
        
        $updateData = [
            'name' => $name,
            'email' => $email
        ];
        
        // Se foi fornecida uma nova senha
        if (!empty($newPassword)) {
            // Validar senha atual
            if (empty($currentPassword) || !password_verify($currentPassword, $user['password'])) {
                $this->flash('error', 'Senha atual incorreta');
                return $this->withInput()->redirect(url('/client/account'));
            }
            
            // Validar nova senha
            if (strlen($newPassword) < 6) {
                $this->flash('error', 'A nova senha deve ter pelo menos 6 caracteres');
                return $this->withInput()->redirect(url('/client/account'));
            }
            
            if ($newPassword !== $confirmPassword) {
                $this->flash('error', 'A confirmação da senha não confere');
                return $this->withInput()->redirect(url('/client/account'));
            }
            
            $updateData['password'] = password_hash($newPassword, PASSWORD_ARGON2ID);
        }
        
        // Atualizar dados
        if ($this->db->update('users', $updateData, 'id = :id', ['id' => $user['id']])) {
            $this->flash('success', 'Dados atualizados com sucesso!');
        } else {
            $this->flash('error', 'Erro ao atualizar dados');
        }
        
        redirect(url('/client/account'));
    }
    
    public function generateApiKey() {
        // Verificar CSRF token
        if (!$this->verifyCsrfToken($this->input('_token'))) {
            return $this->json(['success' => false, 'message' => 'Token de segurança inválido'], 400);
        }
        
        $user = Auth::user();
        
        // Buscar assinatura ativa
        $subscription = $this->db->fetch(
            "SELECT * FROM subscriptions 
             WHERE user_id = :user_id AND status = 'active'
             ORDER BY created_at DESC
             LIMIT 1",
            ['user_id' => $user['id']]
        );
        
        if (!$subscription) {
            return $this->json(['success' => false, 'message' => 'Nenhuma assinatura ativa encontrada'], 400);
        }
        
        $keyName = $this->input('name', 'Chave Principal');
        $apiKey = Auth::generateApiKey($user['id'], $subscription['id'], $keyName);
        
        if ($apiKey) {
            return $this->json([
                'success' => true,
                'message' => 'Licença API gerada com sucesso!',
                'api_key' => $apiKey
            ]);
        } else {
            return $this->json(['success' => false, 'message' => 'Erro ao gerar licença API'], 500);
        }
    }
    
    public function revokeApiKey($keyId) {
        // Verificar CSRF token
        if (!$this->verifyCsrfToken($this->input('_token'))) {
            return $this->json(['success' => false, 'message' => 'Token de segurança inválido'], 400);
        }
        
        $user = Auth::user();
        
        // Verificar se a chave pertence ao usuário
        $apiKey = $this->db->fetch(
            "SELECT * FROM api_keys WHERE id = :id AND user_id = :user_id",
            ['id' => $keyId, 'user_id' => $user['id']]
        );
        
        if (!$apiKey) {
            return $this->json(['success' => false, 'message' => 'Chave não encontrada'], 404);
        }
        
        // Revogar chave
        if ($this->db->update('api_keys', ['status' => 'revoked'], 'id = :id', ['id' => $keyId])) {
            return $this->json(['success' => true, 'message' => 'Chave revogada com sucesso!']);
        } else {
            return $this->json(['success' => false, 'message' => 'Erro ao revogar chave'], 500);
        }
    }

    public function sites() {
        $user = Auth::user();

        // Buscar assinatura ativa para verificar limites
        $subscription = $this->db->fetch(
            "SELECT s.*, p.name as plan_name, p.max_sites
             FROM subscriptions s
             JOIN plans p ON s.plan_id = p.id
             WHERE s.user_id = :user_id AND s.status = 'active'
             ORDER BY s.created_at DESC
             LIMIT 1",
            ['user_id' => $user['id']]
        );

        // Buscar sites do cliente
        $sites = $this->db->fetchAll(
            "SELECT cs.*, ak.name as api_key_name
             FROM client_sites cs
             LEFT JOIN api_keys ak ON cs.api_key_id = ak.id
             WHERE cs.user_id = :user_id
             ORDER BY cs.created_at DESC",
            ['user_id' => $user['id']]
        );

        $this->view('client.sites', [
            'user' => $user,
            'subscription' => $subscription,
            'sites' => $sites
        ]);
    }

    public function addSite() {
        // Verificar CSRF token
        if (!$this->verifyCsrfToken($this->input('_token'))) {
            return $this->json(['success' => false, 'message' => 'Token de segurança inválido'], 400);
        }

        $user = Auth::user();

        // Buscar assinatura ativa
        $subscription = $this->db->fetch(
            "SELECT s.*, p.max_sites
             FROM subscriptions s
             JOIN plans p ON s.plan_id = p.id
             WHERE s.user_id = :user_id AND s.status = 'active'
             ORDER BY s.created_at DESC
             LIMIT 1",
            ['user_id' => $user['id']]
        );

        if (!$subscription) {
            return $this->json(['success' => false, 'message' => 'Nenhuma assinatura ativa encontrada'], 400);
        }

        // Verificar limite de sites
        if ($subscription['max_sites']) {
            $currentSites = $this->db->fetch(
                "SELECT COUNT(*) as count FROM client_sites WHERE user_id = :user_id",
                ['user_id' => $user['id']]
            );

            if ($currentSites['count'] >= $subscription['max_sites']) {
                return $this->json([
                    'success' => false,
                    'message' => "Limite de {$subscription['max_sites']} sites atingido para seu plano"
                ], 400);
            }
        }

        $siteName = $this->input('site_name');
        $siteUrl = $this->input('site_url');

        // Validar dados
        if (empty($siteName) || empty($siteUrl)) {
            return $this->json(['success' => false, 'message' => 'Nome e URL do site são obrigatórios'], 400);
        }

        // Validar URL
        if (!filter_var($siteUrl, FILTER_VALIDATE_URL)) {
            return $this->json(['success' => false, 'message' => 'URL inválida'], 400);
        }

        // Verificar se a URL já existe para este usuário
        $existingSite = $this->db->fetch(
            "SELECT id FROM client_sites WHERE user_id = :user_id AND site_url = :url",
            ['user_id' => $user['id'], 'url' => $siteUrl]
        );

        if ($existingSite) {
            return $this->json(['success' => false, 'message' => 'Este site já foi adicionado'], 400);
        }

        // Gerar chave API automaticamente para o site
        $keyName = "Licença - " . $siteName;
        $apiKey = Auth::generateApiKey($user['id'], $subscription['id'], $keyName);

        if (!$apiKey) {
            return $this->json(['success' => false, 'message' => 'Erro ao gerar licença API para o site'], 500);
        }

        // Buscar ID da chave gerada
        $keyData = $this->db->fetch(
            "SELECT id FROM api_keys WHERE api_key = :key",
            ['key' => $apiKey]
        );

        // Adicionar site com a chave associada
        $siteId = $this->db->insert('client_sites', [
            'user_id' => $user['id'],
            'site_name' => $siteName,
            'site_url' => $siteUrl,
            'status' => 'pending',
            'api_key_id' => $keyData['id']
        ]);

        if ($siteId) {
            return $this->json([
                'success' => true,
                'message' => 'Site adicionado com sucesso! Licença API gerada automaticamente.',
                'site_id' => $this->db->lastInsertId(),
                'api_key' => $apiKey
            ]);
        } else {
            return $this->json(['success' => false, 'message' => 'Erro ao adicionar site'], 500);
        }
    }

    public function removeSite($siteId) {
        // Verificar CSRF token
        if (!$this->verifyCsrfToken($this->input('_token'))) {
            return $this->json(['success' => false, 'message' => 'Token de segurança inválido'], 400);
        }

        $user = Auth::user();

        // Verificar se o site pertence ao usuário
        $site = $this->db->fetch(
            "SELECT * FROM client_sites WHERE id = :id AND user_id = :user_id",
            ['id' => $siteId, 'user_id' => $user['id']]
        );

        if (!$site) {
            return $this->json(['success' => false, 'message' => 'Site não encontrado'], 404);
        }

        // Remover site
        if ($this->db->query("DELETE FROM client_sites WHERE id = :id", ['id' => $siteId])) {
            return $this->json(['success' => true, 'message' => 'Site removido com sucesso!']);
        } else {
            return $this->json(['success' => false, 'message' => 'Erro ao remover site'], 500);
        }
    }

    public function checkSiteConnection($siteId) {
        $user = Auth::user();

        // Verificar se o site pertence ao usuário
        $site = $this->db->fetch(
            "SELECT * FROM client_sites WHERE id = :id AND user_id = :user_id",
            ['id' => $siteId, 'user_id' => $user['id']]
        );

        if (!$site) {
            return $this->json(['success' => false, 'message' => 'Site não encontrado'], 404);
        }

        // Tentar verificar conexão com o plugin
        $checkUrl = rtrim($site['site_url'], '/') . '/wp-json/e1copy/v1/status';

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $checkUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        $status = 'disconnected';
        $pluginVersion = null;

        if ($httpCode === 200 && $response) {
            $data = json_decode($response, true);
            if ($data && isset($data['status']) && $data['status'] === 'active') {
                $status = 'connected';
                $pluginVersion = $data['version'] ?? null;
            }
        }

        // Atualizar status no banco
        $this->db->update('client_sites', [
            'status' => $status,
            'plugin_version' => $pluginVersion,
            'last_connection' => $status === 'connected' ? date('Y-m-d H:i:s') : null
        ], 'id = :id', ['id' => $siteId]);

        return $this->json([
            'success' => true,
            'status' => $status,
            'plugin_version' => $pluginVersion,
            'message' => $status === 'connected' ? 'Site conectado!' : 'Plugin não detectado'
        ]);
    }

    public function getSiteApiKey($siteId) {
        $user = Auth::user();

        // Verificar se o site pertence ao usuário
        $site = $this->db->fetch(
            "SELECT cs.*, ak.api_key
             FROM client_sites cs
             LEFT JOIN api_keys ak ON cs.api_key_id = ak.id
             WHERE cs.id = :id AND cs.user_id = :user_id",
            ['id' => $siteId, 'user_id' => $user['id']]
        );

        if (!$site) {
            return $this->json(['success' => false, 'message' => 'Site não encontrado'], 404);
        }

        if (!$site['api_key']) {
            return $this->json(['success' => false, 'message' => 'Nenhuma chave API associada a este site'], 404);
        }

        return $this->json([
            'success' => true,
            'api_key' => $site['api_key']
        ]);
    }

    public function regenerateSiteApiKey($siteId) {
        // Verificar CSRF token
        if (!$this->verifyCsrfToken($this->input('_token'))) {
            return $this->json(['success' => false, 'message' => 'Token de segurança inválido'], 400);
        }

        $user = Auth::user();

        // Verificar se o site pertence ao usuário
        $site = $this->db->fetch(
            "SELECT cs.*, ak.api_key, ak.id as key_id
             FROM client_sites cs
             LEFT JOIN api_keys ak ON cs.api_key_id = ak.id
             WHERE cs.id = :id AND cs.user_id = :user_id",
            ['id' => $siteId, 'user_id' => $user['id']]
        );

        if (!$site) {
            return $this->json(['success' => false, 'message' => 'Site não encontrado'], 404);
        }

        // Buscar assinatura ativa
        $subscription = $this->db->fetch(
            "SELECT * FROM subscriptions
             WHERE user_id = :user_id AND status = 'active'
             ORDER BY created_at DESC
             LIMIT 1",
            ['user_id' => $user['id']]
        );

        if (!$subscription) {
            return $this->json(['success' => false, 'message' => 'Nenhuma assinatura ativa encontrada'], 400);
        }

        // Revogar chave antiga se existir
        if ($site['key_id']) {
            $this->db->update('api_keys', ['status' => 'revoked'], 'id = :id', ['id' => $site['key_id']]);
        }

        // Gerar nova chave
        $keyName = "Licença - " . $site['site_name'];
        $newApiKey = Auth::generateApiKey($user['id'], $subscription['id'], $keyName);

        if (!$newApiKey) {
            return $this->json(['success' => false, 'message' => 'Erro ao gerar nova licença API'], 500);
        }

        // Buscar ID da nova chave
        $newKeyData = $this->db->fetch(
            "SELECT id FROM api_keys WHERE api_key = :key",
            ['key' => $newApiKey]
        );

        // Atualizar site com nova chave
        $this->db->update('client_sites', [
            'api_key_id' => $newKeyData['id']
        ], 'id = :id', ['id' => $siteId]);

        return $this->json([
            'success' => true,
            'message' => 'Nova licença API gerada com sucesso!',
            'api_key' => $newApiKey
        ]);
    }
}
