<?php include __DIR__ . '/../../layouts/client-header.php'; ?>

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <?php if ($post['post_type'] === 'article'): ?>
                        <i class="fas fa-file-alt text-primary"></i> Editar Artigo Simples
                    <?php else: ?>
                        <i class="fas fa-star text-warning"></i> Editar Produto Review
                    <?php endif; ?>
                </h1>
                <div>
                    <a href="/client/posts/<?= $post['id'] ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Voltar
                    </a>
                </div>
            </div>

            <?php if (isset($_GET['error'])): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= htmlspecialchars($_GET['error']) ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <form method="POST" action="/client/posts/<?= $post['id'] ?>" id="postForm">
                <div class="row">
                    <div class="col-md-8">
                        <!-- Informações do Site -->
                        <div class="card mb-4">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-globe"></i> Site de Destino
                                </h6>
                                <div class="d-flex align-items-center">
                                    <div>
                                        <strong><?= htmlspecialchars($post['site_name']) ?></strong>
                                        <br>
                                        <small class="text-muted"><?= htmlspecialchars($post['site_url']) ?></small>
                                    </div>
                                    <div class="ms-auto">
                                        <span class="badge bg-success">Conectado</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <?php if ($post['post_type'] === 'article'): ?>
                        <!-- Conteúdo Principal -->
                        <div class="card mb-4">
                            <div class="card-body">
                                <h6 class="card-title">Conteúdo Principal</h6>

                                <!-- Título -->
                                <div class="mb-3">
                                    <label for="title" class="form-label">Título *</label>
                                    <input type="text" class="form-control" id="title" name="title" required
                                           value="<?= htmlspecialchars($post['title']) ?>"
                                           placeholder="Digite o título do post...">
                                </div>

                                <!-- Resumo/Excerpt -->
                                <div class="mb-3">
                                    <label for="excerpt" class="form-label">Resumo/Excerpt</label>
                                    <textarea class="form-control" id="excerpt" name="excerpt" rows="3"
                                              placeholder="Breve descrição do post (opcional)..."><?= htmlspecialchars($post['excerpt']) ?></textarea>
                                    <div class="form-text">Usado para SEO e listagens de posts.</div>
                                </div>

                                <!-- Conteúdo -->
                                <div class="mb-3">
                                    <label for="content" class="form-label">Conteúdo *</label>
                                    <textarea class="form-control" id="content" name="content" rows="15" required
                                              placeholder="Digite o conteúdo do post..."><?= htmlspecialchars($post['content']) ?></textarea>
                                    <div class="form-text">Você pode usar HTML básico para formatação.</div>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>

                        <?php if ($post['post_type'] === 'product_review'): ?>
                        <!-- Conteúdo Principal -->
                        <div class="card mb-4">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-edit"></i> Conteúdo Principal
                                </h6>

                                <div class="mb-3">
                                    <label for="product_name" class="form-label">Nome do Produto *</label>
                                    <input type="text" class="form-control" id="product_name" name="product_name" required
                                           value="<?= htmlspecialchars($post['product_name']) ?>"
                                           placeholder="Digite o nome do produto...">
                                </div>

                                <div class="mb-3">
                                    <label for="product_description" class="form-label">Descrição do Produto *</label>
                                    <textarea class="form-control" id="product_description" name="product_description" rows="6" required
                                              placeholder="Descreva detalhadamente o produto, suas características, benefícios e funcionalidades..."><?= htmlspecialchars($post['product_description']) ?></textarea>
                                    <div class="form-text">Esta será a descrição principal do produto no review.</div>
                                </div>

                                <div class="mb-3">
                                    <label for="post_cover" class="form-label">Capa do Post</label>
                                    <input type="file" class="form-control" id="post_cover" name="post_cover" accept="image/*">
                                    <div class="form-text">Imagem que será usada como capa do post (JPG, PNG, WebP).</div>
                                    <?php if ($post['post_cover']): ?>
                                        <div class="mt-2">
                                            <div class="d-flex align-items-center gap-2">
                                                <img src="<?= htmlspecialchars($post['post_cover']) ?>"
                                                     alt="Capa atual"
                                                     class="img-thumbnail"
                                                     style="max-width: 100px; max-height: 100px;">
                                                <div>
                                                    <small class="text-muted d-block">Capa atual</small>
                                                    <small class="text-muted">Selecione uma nova imagem para substituir</small>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <div class="mb-3">
                                    <label for="keyword" class="form-label">Palavra-Chave Principal *</label>
                                    <input type="text" class="form-control" id="keyword" name="keyword" required
                                           value="<?= htmlspecialchars($post['keyword']) ?>"
                                           placeholder="palavra-chave principal">
                                    <div class="form-text">Palavra-chave principal para SEO.</div>
                                </div>

                                <div class="mb-3">
                                    <label for="youtube_video" class="form-label">Vídeo YouTube</label>
                                    <input type="url" class="form-control" id="youtube_video" name="youtube_video"
                                           value="<?= htmlspecialchars($post['youtube_video']) ?>"
                                           placeholder="https://www.youtube.com/watch?v=...">
                                    <div class="form-text">URL do vídeo do YouTube relacionado ao produto.</div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="category" class="form-label">Categoria *</label>
                                            <select class="form-select" id="category" name="category" required>
                                                <option value="">Carregando categorias...</option>
                                            </select>
                                            <div class="form-text">
                                                <span id="category-loading" class="text-muted">
                                                    <i class="fas fa-spinner fa-spin"></i> Buscando categorias do WordPress...
                                                </span>
                                                <span id="category-error" class="text-danger d-none">
                                                    <i class="fas fa-exclamation-triangle"></i> Erro ao carregar categorias
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="existing_tags" class="form-label">Tags Existentes</label>
                                            <select class="form-select" id="existing_tags" name="existing_tags" multiple>
                                                <option value="">Carregando tags...</option>
                                            </select>
                                            <div class="form-text">
                                                <span id="tags-loading" class="text-muted">
                                                    <i class="fas fa-spinner fa-spin"></i> Buscando tags do WordPress...
                                                </span>
                                                <span id="tags-error" class="text-danger d-none">
                                                    <i class="fas fa-exclamation-triangle"></i> Erro ao carregar tags
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <?php if (!empty($post['product_images'])): ?>
                                    <?php $productImages = json_decode($post['product_images'], true); ?>
                                    <?php if (is_array($productImages) && !empty($productImages)): ?>
                                        <div class="mb-3">
                                            <label class="form-label">Imagens Atuais do Produto</label>
                                            <div class="row">
                                                <?php foreach ($productImages as $index => $imageUrl): ?>
                                                    <div class="col-md-4 mb-2">
                                                        <div class="card">
                                                            <img src="<?= htmlspecialchars($imageUrl) ?>"
                                                                 alt="Imagem <?= $index + 1 ?>"
                                                                 class="card-img-top"
                                                                 style="height: 150px; object-fit: cover;">
                                                            <div class="card-body p-2">
                                                                <small class="text-muted">Imagem <?= $index + 1 ?></small>
                                                            </div>
                                                        </div>
                                                    </div>
                                                <?php endforeach; ?>
                                            </div>
                                            <div class="form-text">
                                                <i class="fas fa-info-circle"></i>
                                                Para alterar as imagens, substitua-as abaixo. As imagens atuais serão mantidas se você não selecionar novas.
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                <?php endif; ?>

                                <!-- Campos de imagens individuais como no formulário de criação -->
                                <div class="mb-3">
                                    <label class="form-label">
                                        <?php if (!empty($post['product_images'])): ?>
                                            Substituir Imagens do Produto
                                        <?php else: ?>
                                            Imagens do Produto *
                                        <?php endif; ?>
                                    </label>
                                    <div id="product-images-container">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="product_image_1" class="form-label">Imagem 1 <?= empty($post['product_images']) ? '*' : '' ?></label>
                                                    <input type="file" class="form-control product-image" id="product_image_1" name="product_images[]"
                                                           <?= empty($post['product_images']) ? 'required' : '' ?> accept="image/*">
                                                    <div class="image-preview mt-2" id="preview_1"></div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="product_image_2" class="form-label">Imagem 2 <?= empty($post['product_images']) ? '*' : '' ?></label>
                                                    <input type="file" class="form-control product-image" id="product_image_2" name="product_images[]"
                                                           <?= empty($post['product_images']) ? 'required' : '' ?> accept="image/*">
                                                    <div class="image-preview mt-2" id="preview_2"></div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="product_image_3" class="form-label">Imagem 3 <?= empty($post['product_images']) ? '*' : '' ?></label>
                                                    <input type="file" class="form-control product-image" id="product_image_3" name="product_images[]"
                                                           <?= empty($post['product_images']) ? 'required' : '' ?> accept="image/*">
                                                    <div class="image-preview mt-2" id="preview_3"></div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="product_image_4" class="form-label">Imagem 4 <?= empty($post['product_images']) ? '*' : '' ?></label>
                                                    <input type="file" class="form-control product-image" id="product_image_4" name="product_images[]"
                                                           <?= empty($post['product_images']) ? 'required' : '' ?> accept="image/*">
                                                    <div class="image-preview mt-2" id="preview_4"></div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="product_image_5" class="form-label">Imagem 5 <?= empty($post['product_images']) ? '*' : '' ?></label>
                                                    <input type="file" class="form-control product-image" id="product_image_5" name="product_images[]"
                                                           <?= empty($post['product_images']) ? 'required' : '' ?> accept="image/*">
                                                    <div class="image-preview mt-2" id="preview_5"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <button type="button" class="btn btn-outline-primary btn-sm" id="add-image-btn">
                                        <i class="fas fa-plus"></i> Adicionar Mais Imagens
                                    </button>

                                    <div class="form-text mt-2">
                                        <?php if (!empty($post['product_images'])): ?>
                                            <strong>Opcional:</strong> Selecione novas imagens apenas se quiser substituir as atuais.
                                        <?php else: ?>
                                            <strong>Importante:</strong> São necessárias pelo menos 5 imagens do produto.
                                        <?php endif; ?>
                                        Formatos aceitos: JPG, PNG, WebP (máximo 5MB por imagem).
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>

                    <div class="col-md-4">
                        <!-- Status Atual -->
                        <div class="card mb-4">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-info-circle"></i> Status Atual
                                </h6>
                                
                                <?php
                                $statusClasses = [
                                    'draft' => 'bg-secondary',
                                    'pending' => 'bg-warning',
                                    'processing' => 'bg-info',
                                    'completed' => 'bg-success',
                                    'published' => 'bg-success',
                                    'failed' => 'bg-danger'
                                ];
                                $statusLabels = [
                                    'draft' => 'Rascunho',
                                    'pending' => 'Pendente',
                                    'processing' => 'Processando',
                                    'completed' => 'Concluído',
                                    'published' => 'Publicado',
                                    'failed' => 'Falhou'
                                ];
                                ?>
                                <span class="badge <?= $statusClasses[$post['status']] ?> fs-6">
                                    <?= $statusLabels[$post['status']] ?>
                                </span>
                                
                                <div class="mt-3">
                                    <small class="text-muted">
                                        Criado em: <?= date('d/m/Y H:i', strtotime($post['created_at'])) ?>
                                    </small>
                                </div>
                            </div>
                        </div>



                        <!-- Ações -->
                        <div class="card">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-cog"></i> Ações
                                </h6>
                                
                                <div class="d-grid gap-2">
                                    <button type="submit" name="action" value="save_draft" class="btn btn-outline-secondary">
                                        <i class="fas fa-save"></i> Salvar Rascunho
                                    </button>
                                    <?php if (in_array($post['status'], ['draft', 'failed'])): ?>
                                        <button type="submit" name="action" value="publish" class="btn btn-primary">
                                            <i class="fas fa-paper-plane"></i> Publicar Agora
                                        </button>
                                    <?php endif; ?>
                                </div>
                                
                                <hr>
                                
                                <div class="text-muted small">
                                    <p><strong>Rascunho:</strong> Salva as alterações sem publicar.</p>
                                    <?php if (in_array($post['status'], ['draft', 'failed'])): ?>
                                        <p><strong>Publicar:</strong> Envia o post para a fila de publicação.</p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
<?php if ($post['post_type'] === 'article'): ?>
// Auto-resize textarea
document.getElementById('content')?.addEventListener('input', function() {
    this.style.height = 'auto';
    this.style.height = this.scrollHeight + 'px';
});
<?php endif; ?>

<?php if ($post['post_type'] === 'product_review'): ?>
// Auto-resize textarea da descrição do produto
document.getElementById('product_description')?.addEventListener('input', function() {
    this.style.height = 'auto';
    this.style.height = this.scrollHeight + 'px';
});
<?php endif; ?>

// Validação do formulário
document.getElementById('postForm').addEventListener('submit', function(e) {
    <?php if ($post['post_type'] === 'article'): ?>
    const title = document.getElementById('title').value.trim();
    const content = document.getElementById('content').value.trim();

    if (!title) {
        e.preventDefault();
        alert('Por favor, digite um título para o post.');
        document.getElementById('title').focus();
        return;
    }

    if (!content) {
        e.preventDefault();
        alert('Por favor, digite o conteúdo do post.');
        document.getElementById('content').focus();
        return;
    }
    <?php endif; ?>

    <?php if ($post['post_type'] === 'product_review'): ?>
    const productName = document.getElementById('product_name').value.trim();
    const productDescription = document.getElementById('product_description').value.trim();
    const keyword = document.getElementById('keyword').value.trim();
    const category = document.getElementById('category').value;

    if (!productName) {
        e.preventDefault();
        alert('Por favor, digite o nome do produto.');
        document.getElementById('product_name').focus();
        return;
    }

    if (!productDescription) {
        e.preventDefault();
        alert('Por favor, digite a descrição do produto.');
        document.getElementById('product_description').focus();
        return;
    }

    if (!keyword) {
        e.preventDefault();
        alert('Por favor, digite a palavra-chave principal.');
        document.getElementById('keyword').focus();
        return;
    }

    if (!category) {
        e.preventDefault();
        alert('Por favor, selecione uma categoria.');
        document.getElementById('category').focus();
        return;
    }

    // Validar imagens apenas se não há imagens existentes
    <?php if (empty($post['product_images'])): ?>
    // Validar imagens obrigatórias (apenas as 5 primeiras)
    const requiredImages = [
        document.getElementById('product_image_1'),
        document.getElementById('product_image_2'),
        document.getElementById('product_image_3'),
        document.getElementById('product_image_4'),
        document.getElementById('product_image_5')
    ];

    let missingImages = 0;
    requiredImages.forEach(function(input, index) {
        if (!input.files || input.files.length === 0) {
            missingImages++;
        }
    });

    if (missingImages > 0) {
        e.preventDefault();
        alert(`Por favor, adicione todas as 5 imagens obrigatórias do produto. Faltam ${missingImages} imagem(ns).`);
        return;
    }
    <?php endif; ?>
    <?php endif; ?>

    // Confirmar publicação
    if (e.submitter && e.submitter.value === 'publish') {
        if (!confirm('Tem certeza que deseja publicar este post agora? Ele será enviado para processamento.')) {
            e.preventDefault();
            return;
        }
    }
});

<?php if ($post['post_type'] === 'article'): ?>
// Contador de caracteres para o título
document.getElementById('title')?.addEventListener('input', function() {
    const maxLength = 500;
    const currentLength = this.value.length;

    // Criar ou atualizar contador
    let counter = document.getElementById('title-counter');
    if (!counter) {
        counter = document.createElement('div');
        counter.id = 'title-counter';
        counter.className = 'form-text';
        this.parentNode.appendChild(counter);
    }

    counter.textContent = `${currentLength}/${maxLength} caracteres`;
    counter.className = currentLength > maxLength ? 'form-text text-danger' : 'form-text text-muted';
});

// Trigger inicial do contador
document.getElementById('title')?.dispatchEvent(new Event('input'));
<?php endif; ?>

// Buscar categorias e tags do WordPress para product review
<?php if ($post['post_type'] === 'product_review'): ?>
document.addEventListener('DOMContentLoaded', function() {
    const siteUrl = <?= json_encode($post['site_url']) ?>;
    const currentCategory = <?= json_encode($post['category']) ?>;
    const currentTags = <?= json_encode($post['existing_tags']) ?>;

    // Buscar categorias
    console.log('Buscando categorias para:', siteUrl);
    fetchWordPressData('categories', siteUrl)
        .then(categories => {
            console.log('Categorias encontradas:', categories);
            const categorySelect = document.getElementById('category');
            const loadingSpan = document.getElementById('category-loading');
            const errorSpan = document.getElementById('category-error');

            categorySelect.innerHTML = '<option value="">Selecione uma categoria...</option>';

            categories.forEach(category => {
                const option = document.createElement('option');
                option.value = category.name;
                option.textContent = category.name;
                if (category.name === currentCategory) {
                    option.selected = true;
                }
                categorySelect.appendChild(option);
            });

            loadingSpan.classList.add('d-none');
        })
        .catch(error => {
            console.error('Erro ao buscar categorias:', error);
            const loadingSpan = document.getElementById('category-loading');
            const errorSpan = document.getElementById('category-error');

            loadingSpan.classList.add('d-none');
            errorSpan.classList.remove('d-none');
            errorSpan.innerHTML = `<i class="fas fa-exclamation-triangle"></i> ${error.message}`;

            // Fallback para input text
            const categorySelect = document.getElementById('category');
            const newInput = document.createElement('input');
            newInput.type = 'text';
            newInput.className = 'form-control';
            newInput.id = 'category';
            newInput.name = 'category';
            newInput.required = true;
            newInput.value = currentCategory;
            newInput.placeholder = 'Digite o nome da categoria';
            categorySelect.parentNode.replaceChild(newInput, categorySelect);
        });

    // Buscar tags
    console.log('Buscando tags para:', siteUrl);
    fetchWordPressData('tags', siteUrl)
        .then(tags => {
            console.log('Tags encontradas:', tags);
            const tagsSelect = document.getElementById('existing_tags');
            const loadingSpan = document.getElementById('tags-loading');
            const errorSpan = document.getElementById('tags-error');

            tagsSelect.innerHTML = '<option value="">Nenhum</option>';

            // Separar tags atuais
            const currentTagsArray = currentTags ? currentTags.split(',').map(tag => tag.trim()) : [];

            tags.forEach(tag => {
                const option = document.createElement('option');
                option.value = tag.name;
                option.textContent = tag.name;
                if (currentTagsArray.includes(tag.name)) {
                    option.selected = true;
                }
                tagsSelect.appendChild(option);
            });

            loadingSpan.classList.add('d-none');
        })
        .catch(error => {
            console.error('Erro ao buscar tags:', error);
            const loadingSpan = document.getElementById('tags-loading');
            const errorSpan = document.getElementById('tags-error');

            loadingSpan.classList.add('d-none');
            errorSpan.classList.remove('d-none');
            errorSpan.innerHTML = `<i class="fas fa-exclamation-triangle"></i> ${error.message}`;

            // Fallback para input text
            const tagsSelect = document.getElementById('existing_tags');
            const newInput = document.createElement('input');
            newInput.type = 'text';
            newInput.className = 'form-control';
            newInput.id = 'existing_tags';
            newInput.name = 'existing_tags';
            newInput.value = currentTags;
            newInput.placeholder = 'Digite as tags existentes separadas por vírgula';
            tagsSelect.parentNode.replaceChild(newInput, tagsSelect);
        });
});

async function fetchWordPressData(type, siteUrl) {
    const endpoint = type === 'categories' ? 'categories' : 'tags';
    const apiUrl = `${siteUrl.replace(/\/$/, '')}/wp-json/wp/v2/${endpoint}?per_page=100`;

    console.log(`Fazendo requisição para: ${apiUrl}`);

    try {
        // Adicionar timeout de 10 segundos
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 10000);

        const response = await fetch(apiUrl, {
            signal: controller.signal,
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();

        // Verificar se a resposta é um array válido
        if (!Array.isArray(data)) {
            throw new Error('Resposta inválida da API do WordPress');
        }

        return data;
    } catch (error) {
        if (error.name === 'AbortError') {
            throw new Error('Timeout: A requisição demorou muito para responder');
        }
        throw error;
    }
}

// Contador de imagens
let imageCount = 5;

// Adicionar mais campos de imagem
document.getElementById('add-image-btn')?.addEventListener('click', function() {
    imageCount++;
    const container = document.getElementById('product-images-container');

    const newRow = document.createElement('div');
    newRow.className = 'row';
    newRow.innerHTML = `
        <div class="col-md-6">
            <div class="mb-3">
                <label for="product_image_${imageCount}" class="form-label">Imagem ${imageCount}</label>
                <div class="input-group">
                    <input type="file" class="form-control product-image" id="product_image_${imageCount}" name="product_images[]" accept="image/*">
                    <button type="button" class="btn btn-outline-danger remove-image-btn">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
                <div class="image-preview mt-2" id="preview_${imageCount}"></div>
            </div>
        </div>
    `;

    container.appendChild(newRow);

    // Adicionar evento de remoção
    newRow.querySelector('.remove-image-btn').addEventListener('click', function() {
        newRow.remove();
    });

    // Adicionar preview para nova imagem
    setupImagePreview(newRow.querySelector('input[type="file"]'));
});

// Configurar preview de imagens
function setupImagePreview(input) {
    console.log('Configurando preview para:', input.id);
    input.addEventListener('change', function(e) {
        const file = e.target.files[0];
        const previewId = 'preview_' + this.id.split('_').pop();
        const previewContainer = document.getElementById(previewId);

        console.log('Arquivo selecionado:', file ? file.name : 'nenhum');
        console.log('Preview container:', previewContainer);

        if (file && file.type.startsWith('image/')) {
            const reader = new FileReader();
            reader.onload = function(e) {
                previewContainer.innerHTML = `
                    <img src="${e.target.result}" class="img-thumbnail" style="max-width: 150px; max-height: 150px;">
                    <div class="small text-muted mt-1">${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)</div>
                `;
                console.log('Preview criado para:', file.name);
            };
            reader.readAsDataURL(file);
        } else {
            previewContainer.innerHTML = '';
        }
    });
}

// Configurar preview para imagens existentes
document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('input[type="file"].product-image').forEach(setupImagePreview);
});
<?php endif; ?>
</script>

<?php include __DIR__ . '/../../layouts/client-footer.php'; ?>
