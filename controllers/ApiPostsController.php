<?php

class ApiPostsController extends Controller {
    
    /**
     * <PERSON><PERSON><PERSON> posts pendentes para processamento via N8N
     */
    public function pendingPosts() {
        try {
            // Verificar autenticação via API key
            $apiKey = $this->getApiKey();
            if (!$apiKey) {
                $this->jsonResponse(['error' => 'API key required'], 401);
                return;
            }
            
            // Verificar se a API key é válida
            $client = $this->db->fetch("
                SELECT u.id, u.name, u.email 
                FROM users u
                INNER JOIN client_sites cs ON u.id = cs.user_id
                WHERE cs.api_key = :api_key AND cs.status = 'connected'
                LIMIT 1
            ", ['api_key' => $apiKey]);
            
            if (!$client) {
                $this->jsonResponse(['error' => 'Invalid API key'], 401);
                return;
            }
            
            // Buscar posts pendentes do cliente
            $posts = $this->db->fetchAll("
                SELECT 
                    cp.id,
                    cp.title,
                    cp.content,
                    cp.excerpt,
                    cp.post_type,
                    cp.product_name,
                    cp.product_description,
                    cp.keyword,
                    cp.category,
                    cp.existing_tags,
                    cp.youtube_video,
                    cp.product_images,
                    cp.post_cover,
                    cp.slug,
                    cp.created_at,
                    cs.site_name,
                    cs.site_url,
                    cs.api_key
                FROM client_posts cp
                INNER JOIN client_sites cs ON cp.site_id = cs.id
                WHERE cs.api_key = :api_key 
                AND cp.status = 'pending'
                ORDER BY cp.created_at ASC
            ", ['api_key' => $apiKey]);
            
            // Processar URLs das imagens
            foreach ($posts as &$post) {
                if (!empty($post['product_images'])) {
                    $images = json_decode($post['product_images'], true);
                    if (is_array($images)) {
                        // Converter URLs relativas em URLs absolutas
                        $post['product_images'] = array_map(function($url) {
                            return $this->getBaseUrl() . $url;
                        }, $images);
                    } else {
                        $post['product_images'] = [];
                    }
                } else {
                    $post['product_images'] = [];
                }
                
                // URL da capa do post
                if (!empty($post['post_cover'])) {
                    $post['post_cover'] = $this->getBaseUrl() . $post['post_cover'];
                }
            }
            
            $this->jsonResponse([
                'success' => true,
                'posts' => $posts,
                'total' => count($posts)
            ]);
            
        } catch (Exception $e) {
            $this->jsonResponse(['error' => $e->getMessage()], 500);
        }
    }
    
    /**
     * Marca um post como processado
     */
    public function markAsProcessed($postId) {
        try {
            // Verificar autenticação via API key
            $apiKey = $this->getApiKey();
            if (!$apiKey) {
                $this->jsonResponse(['error' => 'API key required'], 401);
                return;
            }
            
            // Verificar se o post existe e pertence ao cliente da API key
            $post = $this->db->fetch("
                SELECT cp.id, cp.status
                FROM client_posts cp
                INNER JOIN client_sites cs ON cp.site_id = cs.id
                WHERE cp.id = :post_id AND cs.api_key = :api_key
            ", ['post_id' => $postId, 'api_key' => $apiKey]);
            
            if (!$post) {
                $this->jsonResponse(['error' => 'Post not found'], 404);
                return;
            }
            
            // Atualizar status para completed
            $this->db->update('client_posts', 
                ['status' => 'completed', 'updated_at' => date('Y-m-d H:i:s')], 
                'id = :id', 
                ['id' => $postId]
            );
            
            $this->jsonResponse([
                'success' => true,
                'message' => 'Post marked as completed'
            ]);
            
        } catch (Exception $e) {
            $this->jsonResponse(['error' => $e->getMessage()], 500);
        }
    }
    
    /**
     * Marca um post como falhou no processamento
     */
    public function markAsFailed($postId) {
        try {
            // Verificar autenticação via API key
            $apiKey = $this->getApiKey();
            if (!$apiKey) {
                $this->jsonResponse(['error' => 'API key required'], 401);
                return;
            }
            
            $errorMessage = $this->input('error_message', 'Processing failed');
            
            // Verificar se o post existe e pertence ao cliente da API key
            $post = $this->db->fetch("
                SELECT cp.id, cp.status
                FROM client_posts cp
                INNER JOIN client_sites cs ON cp.site_id = cs.id
                WHERE cp.id = :post_id AND cs.api_key = :api_key
            ", ['post_id' => $postId, 'api_key' => $apiKey]);
            
            if (!$post) {
                $this->jsonResponse(['error' => 'Post not found'], 404);
                return;
            }
            
            // Atualizar status para failed
            $this->db->update('client_posts', 
                [
                    'status' => 'failed', 
                    'error_message' => $errorMessage,
                    'updated_at' => date('Y-m-d H:i:s')
                ], 
                'id = :id', 
                ['id' => $postId]
            );
            
            $this->jsonResponse([
                'success' => true,
                'message' => 'Post marked as failed'
            ]);
            
        } catch (Exception $e) {
            $this->jsonResponse(['error' => $e->getMessage()], 500);
        }
    }
    
    /**
     * Obtém a API key do header ou parâmetro
     */
    private function getApiKey() {
        // Tentar obter do header Authorization
        $headers = getallheaders();
        if (isset($headers['Authorization'])) {
            if (preg_match('/Bearer\s+(.*)$/i', $headers['Authorization'], $matches)) {
                return $matches[1];
            }
        }
        
        // Tentar obter do parâmetro api_key
        return $this->input('api_key');
    }
    
    /**
     * Obtém a URL base do sistema
     */
    private function getBaseUrl() {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'];
        return $protocol . '://' . $host;
    }
    
    /**
     * Retorna resposta JSON
     */
    private function jsonResponse($data, $statusCode = 200) {
        http_response_code($statusCode);
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }
}
