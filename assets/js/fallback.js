/**
 * <PERSON>rip<PERSON> de Fallback para CDNs
 * E1Copy AI Dashboard
 */

(function() {
    'use strict';
    
    // Verificar se Bootstrap carregou
    function checkBootstrap() {
        // Tentar criar um elemento com classe Bootstrap
        var testElement = document.createElement('div');
        testElement.className = 'container';
        document.body.appendChild(testElement);
        
        var styles = window.getComputedStyle(testElement);
        var hasBootstrap = styles.width !== 'auto' && styles.paddingLeft !== '0px';
        
        document.body.removeChild(testElement);
        
        if (!hasBootstrap) {
            loadLocalCSS();
        }
    }
    
    // Carregar CSS local
    function loadLocalCSS() {
        var link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = getBaseUrl() + '/assets/css/login.css';
        link.onerror = function() {
            console.warn('Não foi possível carregar o CSS local');
        };
        document.head.appendChild(link);
    }
    
    // Obter URL base
    function getBaseUrl() {
        var scripts = document.getElementsByTagName('script');
        var currentScript = scripts[scripts.length - 1];
        var src = currentScript.src;
        return src.substring(0, src.lastIndexOf('/assets/'));
    }
    
    // Verificar quando o DOM estiver pronto
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', checkBootstrap);
    } else {
        checkBootstrap();
    }
    
    // Fallback para Font Awesome
    setTimeout(function() {
        var icons = document.querySelectorAll('.fas');
        if (icons.length > 0) {
            var testIcon = icons[0];
            var styles = window.getComputedStyle(testIcon, ':before');
            
            // Se Font Awesome não carregou, adicionar texto alternativo
            if (!styles.content || styles.content === 'none') {
                icons.forEach(function(icon) {
                    var className = icon.className;
                    var text = '';
                    
                    if (className.includes('fa-robot')) text = '🤖';
                    else if (className.includes('fa-shield')) text = '🛡️';
                    else if (className.includes('fa-envelope')) text = '✉️';
                    else if (className.includes('fa-lock')) text = '🔒';
                    else if (className.includes('fa-key')) text = '🔑';
                    else if (className.includes('fa-user')) text = '👤';
                    else if (className.includes('fa-crown')) text = '👑';
                    else if (className.includes('fa-check')) text = '✓';
                    else if (className.includes('fa-exclamation')) text = '⚠️';
                    else if (className.includes('fa-arrow')) text = '←';
                    else text = '•';
                    
                    if (text && !icon.textContent.trim()) {
                        icon.textContent = text;
                    }
                });
            }
        }
    }, 1000);
})();
