<?php
/**
 * Classe Base para Models
 * Sistema de Dashboard E1Copy AI
 */

class Model {
    protected $db;
    protected $table;
    protected $primaryKey = 'id';
    protected $fillable = [];
    protected $hidden = [];
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    public function find($id) {
        $sql = "SELECT * FROM {$this->table} WHERE {$this->primaryKey} = :id";
        $result = $this->db->fetch($sql, ['id' => $id]);
        
        if ($result) {
            return $this->hideFields($result);
        }
        
        return null;
    }
    
    public function findBy($field, $value) {
        $sql = "SELECT * FROM {$this->table} WHERE {$field} = :value";
        $result = $this->db->fetch($sql, ['value' => $value]);
        
        if ($result) {
            return $this->hideFields($result);
        }
        
        return null;
    }
    
    public function all() {
        $sql = "SELECT * FROM {$this->table}";
        $results = $this->db->fetchAll($sql);
        
        return array_map([$this, 'hideFields'], $results);
    }
    
    public function where($conditions, $params = []) {
        $sql = "SELECT * FROM {$this->table} WHERE {$conditions}";
        $results = $this->db->fetchAll($sql, $params);
        
        return array_map([$this, 'hideFields'], $results);
    }
    
    public function create($data) {
        $filteredData = $this->filterFillable($data);
        
        if ($this->db->insert($this->table, $filteredData)) {
            return $this->find($this->db->lastInsertId());
        }
        
        return false;
    }
    
    public function update($id, $data) {
        $filteredData = $this->filterFillable($data);
        
        return $this->db->update(
            $this->table, 
            $filteredData, 
            "{$this->primaryKey} = :id", 
            ['id' => $id]
        );
    }
    
    public function delete($id) {
        return $this->db->delete($this->table, "{$this->primaryKey} = :id", ['id' => $id]);
    }
    
    public function count($conditions = '1=1', $params = []) {
        $sql = "SELECT COUNT(*) as total FROM {$this->table} WHERE {$conditions}";
        $result = $this->db->fetch($sql, $params);
        
        return (int) $result['total'];
    }
    
    public function paginate($page = 1, $perPage = 10, $conditions = '1=1', $params = []) {
        $offset = ($page - 1) * $perPage;
        
        $sql = "SELECT * FROM {$this->table} WHERE {$conditions} LIMIT {$perPage} OFFSET {$offset}";
        $results = $this->db->fetchAll($sql, $params);
        
        $total = $this->count($conditions, $params);
        $totalPages = ceil($total / $perPage);
        
        return [
            'data' => array_map([$this, 'hideFields'], $results),
            'current_page' => $page,
            'per_page' => $perPage,
            'total' => $total,
            'total_pages' => $totalPages,
            'has_next' => $page < $totalPages,
            'has_prev' => $page > 1
        ];
    }
    
    protected function filterFillable($data) {
        if (empty($this->fillable)) {
            return $data;
        }
        
        return array_intersect_key($data, array_flip($this->fillable));
    }
    
    protected function hideFields($data) {
        if (empty($this->hidden)) {
            return $data;
        }
        
        foreach ($this->hidden as $field) {
            unset($data[$field]);
        }
        
        return $data;
    }
}
