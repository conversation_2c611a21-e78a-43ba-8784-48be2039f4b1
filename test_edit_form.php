<?php
/**
 * Script para testar o formulário de edição
 */

require_once "bootstrap.php";

echo "=== TESTE FORMULÁRIO DE EDIÇÃO ===\n";

// Simular dados de sessão
$_SESSION['user_id'] = 1; // Assumindo usuário ID 1

// Simular dados do formulário
$_POST = [
    'post_type' => 'product_review',
    'product_name' => 'Produto Teste',
    'product_description' => 'Descrição do produto teste',
    'keyword' => 'palavra-chave-teste',
    'category' => 'tecnologia',
    'action' => 'save_draft'
];

// Simular upload de arquivos
$_FILES = [
    'product_images' => [
        'name' => ['test1.jpg', 'test2.png', '', '', ''],
        'type' => ['image/jpeg', 'image/png', '', '', ''],
        'tmp_name' => ['/tmp/test1', '/tmp/test2', '', '', ''],
        'error' => [UPLOAD_ERR_OK, UPLOAD_ERR_OK, UPLOAD_ERR_NO_FILE, UPLOAD_ERR_NO_FILE, UPLOAD_ERR_NO_FILE],
        'size' => [1024, 2048, 0, 0, 0]
    ]
];

echo "1. Dados simulados:\n";
echo "   POST: " . print_r($_POST, true) . "\n";
echo "   FILES: " . print_r($_FILES, true) . "\n";

// Testar processamento como no controller
echo "2. Testando processamento de imagens:\n";

if (isset($_FILES['product_images']) && !empty($_FILES['product_images']['name'][0])) {
    echo "   ✓ Condição de upload atendida\n";
    
    $tempImages = [];
    for ($i = 0; $i < count($_FILES['product_images']['name']); $i++) {
        echo "   Processando imagem $i: " . $_FILES['product_images']['name'][$i] . "\n";
        echo "     Erro: " . $_FILES['product_images']['error'][$i] . "\n";
        
        if ($_FILES['product_images']['error'][$i] === UPLOAD_ERR_OK) {
            $tempImages[] = [
                'name' => $_FILES['product_images']['name'][$i],
                'tmp_name' => $_FILES['product_images']['tmp_name'][$i],
                'type' => $_FILES['product_images']['type'][$i],
                'size' => $_FILES['product_images']['size'][$i]
            ];
            echo "     ✓ Imagem válida adicionada\n";
        } else {
            echo "     ✗ Imagem inválida (erro: " . $_FILES['product_images']['error'][$i] . ")\n";
        }
    }
    
    echo "   Total de imagens válidas: " . count($tempImages) . "\n";
    
    if (!empty($tempImages)) {
        echo "   ✓ Há imagens para processar\n";
        
        // Simular criação de diretório
        $postId = 999; // ID fictício
        $uploadDir = __DIR__ . '/uploads/posts/' . $postId;
        
        echo "   Diretório de upload: $uploadDir\n";
        
        if (!is_dir($uploadDir)) {
            if (mkdir($uploadDir, 0755, true)) {
                echo "   ✓ Diretório criado\n";
            } else {
                echo "   ✗ Erro ao criar diretório\n";
            }
        } else {
            echo "   ✓ Diretório já existe\n";
        }
        
        // Simular processamento de cada imagem
        $uploadedImages = [];
        foreach ($tempImages as $index => $image) {
            echo "   Processando imagem $index: {$image['name']}\n";
            
            // Validar tipo
            $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
            if (!in_array($image['type'], $allowedTypes)) {
                echo "     ✗ Tipo não permitido: {$image['type']}\n";
                continue;
            }
            
            // Validar tamanho
            if ($image['size'] > 5 * 1024 * 1024) {
                echo "     ✗ Arquivo muito grande: {$image['size']}\n";
                continue;
            }
            
            // Gerar nome do arquivo
            $extension = pathinfo($image['name'], PATHINFO_EXTENSION);
            $fileName = 'image_' . ($index + 1) . '_' . time() . '.' . $extension;
            $filePath = $uploadDir . '/' . $fileName;
            
            echo "     Nome do arquivo: $fileName\n";
            echo "     Caminho completo: $filePath\n";
            
            // Simular criação do arquivo (não podemos usar move_uploaded_file com arquivos fictícios)
            if (file_put_contents($filePath, 'conteúdo simulado da imagem') !== false) {
                $publicUrl = '/app/uploads/posts/' . $postId . '/' . $fileName;
                $uploadedImages[] = $publicUrl;
                echo "     ✓ Arquivo simulado criado: $publicUrl\n";
            } else {
                echo "     ✗ Erro ao criar arquivo simulado\n";
            }
        }
        
        echo "   URLs finais: " . json_encode($uploadedImages) . "\n";
        
        // Limpar arquivos de teste
        foreach (glob($uploadDir . '/*') as $file) {
            unlink($file);
        }
        rmdir($uploadDir);
        echo "   ✓ Arquivos de teste removidos\n";
        
    } else {
        echo "   ✗ Nenhuma imagem válida para processar\n";
    }
} else {
    echo "   ✗ Condição de upload NÃO atendida\n";
    echo "   isset(\$_FILES['product_images']): " . (isset($_FILES['product_images']) ? 'SIM' : 'NÃO') . "\n";
    if (isset($_FILES['product_images'])) {
        echo "   empty(\$_FILES['product_images']['name'][0]): " . (empty($_FILES['product_images']['name'][0]) ? 'SIM' : 'NÃO') . "\n";
        echo "   Valor de name[0]: '" . $_FILES['product_images']['name'][0] . "'\n";
    }
}

echo "\n=== FIM TESTE ===\n";
