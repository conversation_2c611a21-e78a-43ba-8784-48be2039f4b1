<?php
function e1copy_ai_generate_page() {
    $generated_content = '';
    $post_title = '';
    $prompt = '';
    $image_url = ''; // Defina manualmente se quiser

    if (isset($_POST['generate_post'])) {
        $post_title = sanitize_text_field($_POST['post_title']);
        $prompt = sanitize_textarea_field($_POST['prompt']);
        $generated_content = e1copy_ai_generate_post($prompt);

        // Limpar o conteúdo para remover tags HTML e caracteres especiais
        $generated_content = strip_tags($generated_content);
        $generated_content = preg_replace('/\*+([^*]+)\*+/', '$1', $generated_content);
        $generated_content = preg_replace('/\#+([^#]+)\#+/', '$1', $generated_content);

        // Quebrar o conteúdo em linhas
        $lines = explode("\n", $generated_content);
        $formatted_content = '';

        // Adicionar H1 na primeira linha
        if (!empty($lines[0])) {
            $formatted_content .= '<h1>' . esc_html($lines[0]) . '</h1>';
        }

        // Adicionar H2 nas linhas subsequentes curtas, parágrafo no resto
        for ($i = 1; $i < count($lines); $i++) {
            $line = trim($lines[$i]);
            if ($line === '') continue;

            if (strlen($line) < 80) {
                $formatted_content .= '<h2>' . esc_html($line) . '</h2>';
            } else {
                $formatted_content .= '<p>' . esc_html($line) . '</p>';
            }
        }

        if ($image_url) {
            $formatted_content = '<img src="' . $image_url . '" alt="Imagem de destaque" class="post-featured-image" />' . $formatted_content;
        }

        $generated_content = $formatted_content;
    }

    if (isset($_POST['save_post']) && isset($_POST['generated_content'])) {
        $post_title = sanitize_text_field($_POST['post_title']);
        $generated_content = wp_kses_post($_POST['generated_content']);
        $post_category = isset($_POST['post_category']) ? [(int) $_POST['post_category']] : [];

        $schedule_date = !empty($_POST['schedule_date']) ? sanitize_text_field($_POST['schedule_date']) : '';
        $post_status = 'draft';
        $post_date = current_time('mysql');

        if ($_POST['save_post'] === 'publish') {
            $post_status = 'publish';
        } elseif ($_POST['save_post'] === 'schedule' && !empty($schedule_date)) {
            $timestamp = strtotime($schedule_date);
            if ($timestamp > time()) {
                $post_status = 'future';
                $post_date = date('Y-m-d H:i:s', $timestamp);
            }
        }

        $post_id = wp_insert_post([
            'post_title'    => $post_title,
            'post_content'  => $generated_content,
            'post_status'   => $post_status,
            'post_author'   => get_current_user_id(),
            'post_category' => $post_category,
            'post_date'     => $post_date
        ]);

        if ($post_id && $image_url) {
            groq_set_featured_image($image_url, $post_id);
        }

        if ($post_id) {
            $status_message = 'salvo como rascunho';
            if ($post_status === 'publish') {
                $status_message = 'publicado';
            } elseif ($post_status === 'future') {
                $status_message = 'agendado para ' . date_i18n('d/m/Y H:i', strtotime($post_date));
            }

            echo "<div class='notice notice-success'><p>Post {$status_message}! <a href='" . get_permalink($post_id) . "' target='_blank'>Ver post</a></p></div>";
        } else {
            echo "<div class='notice notice-error'><p>Falha ao salvar o post.</p></div>";
        }
    }
?>

<div class="wrap">
    <h1>E1Copy AI - Gerar Novo Artigo</h1>
    <form method="post">
        <label>Título do Post:</label><br>
        <input type="text" name="post_title" value="<?php echo esc_attr($post_title); ?>" style="width: 400px;" required><br><br>

        <label>Prompt para IA:</label><br>
        <textarea name="prompt" rows="7" style="width: 600px;" required><?php echo esc_textarea($prompt); ?></textarea><br><br>

        <button type="submit" name="generate_post" class="button button-primary">Gerar Prévia</button>
    </form>

    <?php if ($generated_content): ?>
        <hr>
        <h2>Prévia do Conteúdo Gerado</h2>
        <form method="post">
            <input type="hidden" name="post_title" value="<?php echo esc_attr($post_title); ?>">

            <?php
            wp_editor($generated_content, 'generated_content', [
                'textarea_name' => 'generated_content',
                'textarea_rows' => 20,
                'media_buttons' => false,
                'tinymce'       => true,
                'quicktags'     => true,
            ]);
            ?>
            <br>
            <label for="post_category"><h1>Categoria:</h1></label><br>
            <select name="post_category" id="post_category" required>
                <option value="">Selecione uma categoria</option>
                <?php
                $categories = get_categories(['hide_empty' => false]);
                foreach ($categories as $category) {
                    echo '<option value="' . esc_attr($category->term_id) . '">' . esc_html($category->name) . '</option>';
                }
                ?>
            </select><br><br>

            <label for="schedule_date"><h1>Agendar para:</h1></label><br>
            <input type="datetime-local" name="schedule_date" id="schedule_date"><br><br>

            <button type="submit" name="save_post" value="draft" class="button">Salvar como Rascunho</button>
            <button type="submit" name="save_post" value="publish" class="button button-primary">Publicar Agora</button>
            <button type="submit" name="save_post" value="schedule" class="button button-secondary">Agendar Post</button>
        </form>
    <?php endif; ?>
</div>

<?php
}

function groq_set_featured_image($image_url, $post_id) {
    $image_data = file_get_contents($image_url);
    $upload_dir = wp_upload_dir();
    $filename = basename($image_url);
    $file_path = $upload_dir['path'] . '/' . $filename;
    file_put_contents($file_path, $image_data);

    $attachment = array(
        'guid'           => $upload_dir['url'] . '/' . $filename,
        'post_mime_type' => 'image/jpeg',
        'post_title'     => sanitize_file_name($filename),
        'post_content'   => '',
        'post_status'    => 'inherit',
    );

    $attach_id = wp_insert_attachment($attachment, $file_path, $post_id);
    require_once(ABSPATH . 'wp-admin/includes/image.php');
    $attach_data = wp_generate_attachment_metadata($attach_id, $file_path);
    wp_update_attachment_metadata($attach_id, $attach_data);
    set_post_thumbnail($post_id, $attach_id);

    return $attach_id;
}
