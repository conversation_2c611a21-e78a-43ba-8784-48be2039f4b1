<?php
/**
 * Teste dos endpoints de posts pendentes
 */

require_once "bootstrap.php";

echo "=== TESTE DOS ENDPOINTS DE POSTS PENDENTES ===\n";

try {
    $db = Database::getInstance();
    
    // 1. Verificar posts pendentes no banco
    echo "1. Verificando posts pendentes no banco de dados:\n";
    
    $pendingPosts = $db->fetchAll("
        SELECT 
            cp.id,
            cp.title,
            cp.post_type,
            cp.status,
            cp.created_at,
            cs.site_name,
            cs.site_url,
            u.name as user_name,
            u.email as user_email
        FROM client_posts cp
        LEFT JOIN client_sites cs ON cp.site_id = cs.id
        LEFT JOIN users u ON cs.user_id = u.id
        WHERE cp.status = 'pending'
        ORDER BY cp.created_at ASC
    ");
    
    echo "   Total de posts pendentes: " . count($pendingPosts) . "\n";
    
    if (count($pendingPosts) > 0) {
        $clientsWithPending = [];
        $sitesWithPending = [];
        
        foreach ($pendingPosts as $post) {
            echo "   - Post ID: {$post['id']} | Título: {$post['title']} | Cliente: {$post['user_name']} | Site: {$post['site_name']}\n";
            
            if (!in_array($post['user_email'], $clientsWithPending)) {
                $clientsWithPending[] = $post['user_email'];
            }
            
            if (!in_array($post['site_url'], $sitesWithPending)) {
                $sitesWithPending[] = $post['site_url'];
            }
        }
        
        echo "   Clientes únicos com posts pendentes: " . count($clientsWithPending) . "\n";
        echo "   Sites únicos com posts pendentes: " . count($sitesWithPending) . "\n";
        
        foreach ($clientsWithPending as $client) {
            echo "     - Cliente: $client\n";
        }
    } else {
        echo "   ⚠️  Nenhum post pendente encontrado no banco\n";
    }
    
    // 2. Verificar usuários e sites ativos
    echo "\n2. Verificando usuários e sites ativos:\n";
    
    $activeUsers = $db->fetchAll("
        SELECT id, name, email, status 
        FROM users 
        WHERE status = 'active'
        ORDER BY name
    ");
    echo "   Usuários ativos: " . count($activeUsers) . "\n";
    
    $connectedSites = $db->fetchAll("
        SELECT cs.id, cs.site_name, cs.site_url, cs.status, u.name as user_name
        FROM client_sites cs
        LEFT JOIN users u ON cs.user_id = u.id
        WHERE cs.status = 'connected'
        ORDER BY cs.site_name
    ");
    echo "   Sites conectados: " . count($connectedSites) . "\n";
    
    foreach ($connectedSites as $site) {
        echo "     - Site: {$site['site_name']} | URL: {$site['site_url']} | Cliente: {$site['user_name']}\n";
    }
    
} catch (Exception $e) {
    echo "   Erro ao consultar banco: " . $e->getMessage() . "\n";
}

echo "\n=== ENDPOINTS DISPONÍVEIS ===\n";
echo "1. /api/v1/client-posts/pending (ApiController@getPendingClientPosts)\n";
echo "   - Retorna posts pendentes de TODOS os clientes\n";
echo "   - Não requer API key\n";
echo "   - Organizado por site\n";

echo "\n2. /api/v1/posts/pending (ApiPostsController@pendingPosts)\n";
echo "   - CORRIGIDO: Agora aceita API key opcional\n";
echo "   - Sem API key: retorna posts de TODOS os clientes\n";
echo "   - Com API key: retorna posts apenas do cliente específico\n";
echo "   - Organizado por cliente e por site\n";

echo "\n3. /api/v1/product-reviews/pending (ApiController@getProductReviewsPending)\n";
echo "   - Retorna apenas product reviews pendentes de TODOS os clientes\n";
echo "   - Não requer API key\n";

echo "\n=== SIMULAÇÃO DE RESPOSTA DA API ===\n";

// Simular resposta do endpoint corrigido
if (isset($pendingPosts) && count($pendingPosts) > 0) {
    echo "Exemplo de resposta do endpoint /api/v1/posts/pending (sem API key):\n";
    
    $postsByClient = [];
    $postsBySite = [];
    
    foreach ($pendingPosts as $post) {
        $userKey = $post['user_email'];
        $siteKey = $post['site_url'];
        
        // Organizar por cliente
        if (!isset($postsByClient[$userKey])) {
            $postsByClient[$userKey] = [
                'user_info' => [
                    'name' => $post['user_name'],
                    'email' => $post['user_email']
                ],
                'posts' => []
            ];
        }
        $postsByClient[$userKey]['posts'][] = $post;
        
        // Organizar por site
        if (!isset($postsBySite[$siteKey])) {
            $postsBySite[$siteKey] = [
                'site_info' => [
                    'site_name' => $post['site_name'],
                    'site_url' => $post['site_url']
                ],
                'posts' => []
            ];
        }
        $postsBySite[$siteKey]['posts'][] = $post;
    }
    
    $response = [
        'success' => true,
        'total_pending' => count($pendingPosts),
        'clients_with_pending' => count($postsByClient),
        'sites_with_pending' => count($postsBySite),
        'posts_by_client' => array_values($postsByClient),
        'posts_by_site' => array_values($postsBySite),
        'api_key_filter' => 'all_clients'
    ];
    
    echo json_encode($response, JSON_PRETTY_PRINT) . "\n";
}

echo "\n=== RECOMENDAÇÕES PARA O N8N ===\n";
echo "✅ Use o endpoint: /api/v1/posts/pending (SEM API key)\n";
echo "✅ Ou use: /api/v1/client-posts/pending\n";
echo "✅ Ambos agora retornam posts de TODOS os clientes\n";
echo "✅ Dados organizados por cliente e por site\n";
echo "✅ Inclui informações completas do usuário e site\n";

echo "\n=== CORREÇÃO APLICADA ===\n";
echo "✅ ApiPostsController@pendingPosts agora aceita API key opcional\n";
echo "✅ Sem API key = posts de todos os clientes\n";
echo "✅ Com API key = posts apenas do cliente específico\n";
echo "✅ Resposta organizada para facilitar processamento no N8N\n";
echo "✅ Compatibilidade mantida com uso anterior\n";

echo "\n=== FIM TESTE ===\n";
