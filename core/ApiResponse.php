<?php
/**
 * Classe para Respostas da API
 * Sistema de Dashboard E1Copy AI
 */

class ApiResponse {

    private static function setCorsHeaders() {
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
        header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With, X-E1Copy-API-Key');
        header('Access-Control-Max-Age: 86400');

        // Handle preflight OPTIONS request
        if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
            http_response_code(200);
            exit;
        }
    }

    public static function success($data = null, $message = 'Sucesso', $status = 200) {
        self::setCorsHeaders();
        http_response_code($status);
        header('Content-Type: application/json');
        
        $response = [
            'success' => true,
            'message' => $message,
            'timestamp' => date('c')
        ];
        
        if ($data !== null) {
            $response['data'] = $data;
        }
        
        echo json_encode($response, JSON_PRETTY_PRINT);
        exit;
    }
    
    public static function error($message = 'Erro interno', $status = 500, $errors = null) {
        self::setCorsHeaders();
        http_response_code($status);
        header('Content-Type: application/json');
        
        $response = [
            'success' => false,
            'message' => $message,
            'timestamp' => date('c')
        ];
        
        if ($errors !== null) {
            $response['errors'] = $errors;
        }
        
        echo json_encode($response, JSON_PRETTY_PRINT);
        exit;
    }
    
    public static function unauthorized($message = 'Não autorizado') {
        self::error($message, 401);
    }
    
    public static function forbidden($message = 'Acesso negado') {
        self::error($message, 403);
    }
    
    public static function notFound($message = 'Recurso não encontrado') {
        self::error($message, 404);
    }
    
    public static function validationError($errors, $message = 'Dados inválidos') {
        self::error($message, 422, $errors);
    }
    
    public static function rateLimit($message = 'Limite de requisições excedido') {
        self::error($message, 429);
    }
}
