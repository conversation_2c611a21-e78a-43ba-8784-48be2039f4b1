<?php
/**
 * Rotas da API para Plugin WordPress
 * Sistema de Dashboard E1Copy AI
 * 
 * APENAS APIs de validação de chave para o plugin
 */

// =====================================================
// APIS PARA PLUGIN WORDPRESS - APENAS VALIDAÇÃO
// =====================================================

// Endpoint principal de validação de chave para o plugin
$router->post('/api/v1/validate', 'Api@validateKey');
$router->get('/api/v1/verify-key', 'Api@verifyKey');
$router->post('/api/v1/verify-key', 'Api@verifyKey');

// Endpoint de status da chave (informações básicas)
$router->post('/api/v1/status', 'Api@keyStatus');

// Endpoint de health check (sem autenticação)
$router->get('/api/v1/health', 'Api@healthCheck');
