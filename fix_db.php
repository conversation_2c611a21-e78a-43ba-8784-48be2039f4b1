<?php
$host = "localhost";
$user = "u880879026_userDash";
$pass = ":sX=zys@2";
$db = "u880879026_appDash";

$conn = new mysqli($host, $user, $pass, $db);
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

echo "Connected successfully\n";

// Check if columns exist
$result = $conn->query("DESCRIBE client_posts");
$columns = [];
while ($row = $result->fetch_assoc()) {
    $columns[] = $row["Field"];
}

echo "Existing columns: " . implode(", ", $columns) . "\n";

$needed = ["product_images", "post_cover"];
foreach ($needed as $col) {
    if (!in_array($col, $columns)) {
        echo "Adding column $col...\n";
        $sql = "ALTER TABLE client_posts ADD COLUMN $col TEXT NULL";
        if ($conn->query($sql)) {
            echo "Column $col added successfully\n";
        } else {
            echo "Error adding $col: " . $conn->error . "\n";
        }
    } else {
        echo "Column $col already exists\n";
    }
}

$conn->close();
?>
