<?php
$title = 'Meus Sites - E1Copy AI';
$pageTitle = 'Gerenciar Sites';
ob_start();
?>

<!-- Informações do Plano -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row align-items-center">
            <div class="col-md-8">
                <?php if ($subscription): ?>
                    <h6 class="mb-1">
                        <i class="fas fa-credit-card me-2"></i>
                        Plano: <span class="badge bg-primary"><?= e($subscription['plan_name']) ?></span>
                    </h6>
                    <p class="text-muted mb-0">
                        <i class="fas fa-globe me-1"></i>
                        Sites permitidos: 
                        <?php if ($subscription['max_sites']): ?>
                            <?= count($sites) ?> / <?= $subscription['max_sites'] ?>
                        <?php else: ?>
                            <?= count($sites) ?> / Ilimitado
                        <?php endif; ?>
                    </p>
                <?php else: ?>
                    <div class="alert alert-warning mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Nenhuma assinatura ativa encontrada. Entre em contato com o suporte.
                    </div>
                <?php endif; ?>
            </div>
            <div class="col-md-4 text-end">
                <?php if ($subscription && (!$subscription['max_sites'] || count($sites) < $subscription['max_sites'])): ?>
                    <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addSiteModal">
                        <i class="fas fa-plus me-1"></i>
                        Adicionar Site
                    </button>
                <?php else: ?>
                    <button type="button" class="btn btn-secondary" disabled>
                        <i class="fas fa-ban me-1"></i>
                        Limite Atingido
                    </button>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Lista de Sites -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-globe me-2"></i>
            Meus Sites (<?= count($sites) ?>)
        </h5>
    </div>
    <div class="card-body">
        <?php if (!empty($sites)): ?>
            <div class="row">
                <?php foreach ($sites as $site): ?>
                    <div class="col-md-6 col-lg-4 mb-4">
                        <div class="card h-100 border-<?= $site['status'] === 'connected' ? 'success' : ($site['status'] === 'pending' ? 'warning' : 'danger') ?>">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h6 class="mb-0"><?= e($site['site_name']) ?></h6>
                                <span class="badge bg-<?= $site['status'] === 'connected' ? 'success' : ($site['status'] === 'pending' ? 'warning' : 'danger') ?>">
                                    <?php
                                    switch($site['status']) {
                                        case 'connected': echo 'Conectado'; break;
                                        case 'pending': echo 'Pendente'; break;
                                        case 'disconnected': echo 'Desconectado'; break;
                                        case 'suspended': echo 'Suspenso'; break;
                                        default: echo ucfirst($site['status']);
                                    }
                                    ?>
                                </span>
                            </div>
                            <div class="card-body">
                                <p class="card-text">
                                    <i class="fas fa-link me-1"></i>
                                    <a href="<?= e($site['site_url']) ?>" target="_blank" class="text-decoration-none">
                                        <?= e($site['site_url']) ?>
                                    </a>
                                </p>
                                
                                <?php if ($site['plugin_version']): ?>
                                    <p class="text-muted small mb-2">
                                        <i class="fas fa-plug me-1"></i>
                                        Plugin v<?= e($site['plugin_version']) ?>
                                    </p>
                                <?php endif; ?>
                                
                                <?php if ($site['last_connection']): ?>
                                    <p class="text-muted small mb-2">
                                        <i class="fas fa-clock me-1"></i>
                                        Última conexão: <?= date('d/m/Y H:i', strtotime($site['last_connection'])) ?>
                                    </p>
                                <?php endif; ?>
                                
                                <?php if ($site['api_key_name']): ?>
                                    <p class="text-muted small mb-2">
                                        <i class="fas fa-key me-1"></i>
                                        Licença: <?= e($site['api_key_name']) ?>
                                    </p>
                                <?php endif; ?>
                                
                                <small class="text-muted">
                                    <i class="fas fa-calendar me-1"></i>
                                    Adicionado em <?= date('d/m/Y', strtotime($site['created_at'])) ?>
                                </small>
                            </div>
                            <div class="card-footer">
                                <div class="btn-group w-100" role="group">
                                    <button type="button" class="btn btn-outline-success btn-sm"
                                            onclick="showApiKey(<?= $site['id'] ?>)" title="Exibir Chave">
                                        <i class="fas fa-key"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-primary btn-sm"
                                            onclick="checkConnection(<?= $site['id'] ?>)" title="Verificar Conexão">
                                        <i class="fas fa-sync-alt"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-info btn-sm"
                                            onclick="showSiteInfo(<?= $site['id'] ?>)" title="Informações">
                                        <i class="fas fa-info-circle"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-danger btn-sm"
                                            onclick="removeSite(<?= $site['id'] ?>, '<?= e($site['site_name']) ?>')" title="Remover Site">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php else: ?>
            <div class="text-center py-5">
                <i class="fas fa-globe fa-3x text-muted mb-3"></i>
                <h5>Nenhum site adicionado</h5>
                <p class="text-muted">Adicione seu primeiro site para começar a usar o E1Copy AI.</p>
                <?php if ($subscription && (!$subscription['max_sites'] || count($sites) < $subscription['max_sites'])): ?>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addSiteModal">
                        <i class="fas fa-plus me-1"></i>
                        Adicionar Primeiro Site
                    </button>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Modal para adicionar site -->
<div class="modal fade" id="addSiteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Adicionar Novo Site</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addSiteForm">
                <div class="modal-body">
                    <input type="hidden" name="_token" value="<?= $csrf_token ?>">
                    
                    <div class="mb-3">
                        <label for="siteName" class="form-label">Nome do Site</label>
                        <input type="text" class="form-control" id="siteName" name="site_name" 
                               placeholder="Ex: Meu Blog" required>
                        <div class="form-text">Nome para identificar o site no painel.</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="siteUrl" class="form-label">URL do Site</label>
                        <input type="url" class="form-control" id="siteUrl" name="site_url" 
                               placeholder="https://meusite.com" required>
                        <div class="form-text">URL completa do seu site WordPress.</div>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Próximos passos:</strong>
                        <ol class="mb-0 mt-2">
                            <li>Instale o plugin E1Copy AI no seu WordPress</li>
                            <li>Configure a chave de ativação nas configurações do plugin</li>
                            <li>O status mudará para "Conectado" automaticamente</li>
                        </ol>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" class="btn btn-success">Adicionar Site</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal para exibir chave API -->
<div class="modal fade" id="apiKeyModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Licença API do Site</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Importante:</strong> Mantenha sua licença API segura e não a compartilhe.
                </div>

                <div class="mb-3">
                    <label class="form-label">Site:</label>
                    <div class="form-control-plaintext" id="apiKeySiteName"></div>
                </div>

                <div class="mb-3">
                    <label class="form-label">Sua Licença API:</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="apiKeyValue" readonly>
                        <button class="btn btn-outline-secondary" type="button" onclick="copyApiKey()">
                            <i class="fas fa-copy"></i> Copiar
                        </button>
                    </div>
                </div>

                <div class="alert alert-danger" id="regenerateWarning" style="display: none;">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Atenção:</strong> Ao gerar uma nova chave, a chave atual será invalidada.
                    Você precisará atualizar a chave no plugin WordPress, caso contrário as funcionalidades ficarão bloqueadas.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-warning" onclick="showRegenerateWarning()">
                    <i class="fas fa-sync-alt me-1"></i>
                    Gerar Nova Chave
                </button>
                <button type="button" class="btn btn-danger" id="confirmRegenerate" onclick="regenerateApiKey()" style="display: none;">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    Confirmar Nova Chave
                </button>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal de informações do site -->
<div class="modal fade" id="siteInfoModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Informações do Site</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="siteInfoContent">
                <!-- Conteúdo será carregado via JavaScript -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
            </div>
        </div>
    </div>
</div>

<?php
$content = ob_get_clean();
include __DIR__ . '/../layouts/app.php';
?>

<script>
// Adicionar site
document.getElementById('addSiteForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Adicionando...';
    
    fetch('<?= url('/client/sites/add') ?>', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            bootstrap.Modal.getInstance(document.getElementById('addSiteModal')).hide();
            location.reload();
        } else {
            alert('Erro: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Erro:', error);
        alert('Erro ao adicionar site');
    })
    .finally(() => {
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    });
});

// Verificar conexão
function checkConnection(siteId) {
    const formData = new FormData();
    formData.append('_token', '<?= $csrf_token ?>');
    
    fetch(`<?= url('/client/sites/check/') ?>${siteId}`, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            location.reload();
        } else {
            alert('Erro: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Erro:', error);
        alert('Erro ao verificar conexão');
    });
}

// Remover site
function removeSite(siteId, siteName) {
    if (confirm(`Tem certeza que deseja remover o site "${siteName}"?`)) {
        const formData = new FormData();
        formData.append('_token', '<?= $csrf_token ?>');
        
        fetch(`<?= url('/client/sites/remove/') ?>${siteId}`, {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Erro: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Erro:', error);
            alert('Erro ao remover site');
        });
    }
}

// Mostrar chave API do site
function showApiKey(siteId) {
    const siteData = <?= json_encode($sites) ?>.find(s => s.id == siteId);

    if (siteData) {
        // Buscar chave API do site
        fetch(`<?= url('/client/sites/api-key/') ?>${siteId}`, {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('apiKeySiteName').textContent = siteData.site_name;
                document.getElementById('apiKeyValue').value = data.api_key;

                // Armazenar ID do site para regeneração
                document.getElementById('apiKeyModal').setAttribute('data-site-id', siteId);

                // Resetar estado do modal
                document.getElementById('regenerateWarning').style.display = 'none';
                document.getElementById('confirmRegenerate').style.display = 'none';

                new bootstrap.Modal(document.getElementById('apiKeyModal')).show();
            } else {
                alert('Erro: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Erro:', error);
            alert('Erro ao buscar chave API');
        });
    }
}

// Copiar chave para clipboard
function copyApiKey() {
    const input = document.getElementById('apiKeyValue');
    input.select();
    document.execCommand('copy');

    // Feedback visual
    const button = event.target.closest('button');
    const originalHTML = button.innerHTML;
    button.innerHTML = '<i class="fas fa-check"></i> Copiado!';
    setTimeout(() => {
        button.innerHTML = originalHTML;
    }, 2000);
}

// Mostrar aviso de regeneração
function showRegenerateWarning() {
    document.getElementById('regenerateWarning').style.display = 'block';
    document.getElementById('confirmRegenerate').style.display = 'inline-block';
}

// Regenerar chave API
function regenerateApiKey() {
    const siteId = document.getElementById('apiKeyModal').getAttribute('data-site-id');

    if (confirm('Tem certeza? A chave atual será invalidada e você precisará atualizar o plugin WordPress.')) {
        const formData = new FormData();
        formData.append('_token', '<?= $csrf_token ?>');

        fetch(`<?= url('/client/sites/regenerate-api-key/') ?>${siteId}`, {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('apiKeyValue').value = data.api_key;
                document.getElementById('regenerateWarning').style.display = 'none';
                document.getElementById('confirmRegenerate').style.display = 'none';

                alert('Nova chave gerada com sucesso! Não esqueça de atualizar no plugin WordPress.');
            } else {
                alert('Erro: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Erro:', error);
            alert('Erro ao regenerar chave API');
        });
    }
}

// Mostrar informações do site
function showSiteInfo(siteId) {
    const siteData = <?= json_encode($sites) ?>.find(s => s.id == siteId);

    if (siteData) {
        const content = `
            <div class="row">
                <div class="col-12">
                    <h6>${siteData.site_name}</h6>
                    <p><strong>URL:</strong> <a href="${siteData.site_url}" target="_blank">${siteData.site_url}</a></p>
                    <p><strong>Status:</strong> <span class="badge bg-${siteData.status === 'connected' ? 'success' : 'warning'}">${siteData.status}</span></p>
                    ${siteData.plugin_version ? `<p><strong>Versão do Plugin:</strong> ${siteData.plugin_version}</p>` : ''}
                    ${siteData.last_connection ? `<p><strong>Última Conexão:</strong> ${new Date(siteData.last_connection).toLocaleString('pt-BR')}</p>` : ''}
                    <p><strong>Adicionado em:</strong> ${new Date(siteData.created_at).toLocaleDateString('pt-BR')}</p>
                </div>
            </div>
        `;

        document.getElementById('siteInfoContent').innerHTML = content;
        new bootstrap.Modal(document.getElementById('siteInfoModal')).show();
    }
}
</script>
