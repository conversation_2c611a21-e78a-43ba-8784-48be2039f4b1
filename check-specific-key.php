<?php
/**
 * Verificação específica da chave que está dando problema
 */

require_once __DIR__ . '/bootstrap.php';

$apiKey = 'e1copy_9f61f8b3ad6a29b10c0b76a6e401bd1a95ae685b82e539bad6592415c7b86715';

echo "🔍 Verificando chave específica: " . substr($apiKey, 0, 20) . "...\n\n";

$db = Database::getInstance();

try {
    // 1. Verificar se a chave existe
    echo "1️⃣ Verificando se a chave existe...\n";
    $keyData = $db->fetch("SELECT * FROM api_keys WHERE api_key = :key", ['key' => $apiKey]);
    
    if (!$keyData) {
        echo "❌ CHAVE NÃO ENCONTRADA!\n";
        exit(1);
    }
    
    echo "✅ Chave encontrada!\n";
    echo "   ID: {$keyData['id']}\n";
    echo "   Nome: {$keyData['name']}\n";
    echo "   Status: {$keyData['status']}\n";
    echo "   User ID: {$keyData['user_id']}\n";
    echo "   Subscription ID: {$keyData['subscription_id']}\n";
    echo "   Uso mensal: {$keyData['monthly_usage']}/{$keyData['monthly_limit']}\n";
    echo "   Criada em: {$keyData['created_at']}\n\n";
    
    // 2. Verificar usuário
    echo "2️⃣ Verificando usuário...\n";
    $user = $db->fetch("SELECT * FROM users WHERE id = :id", ['id' => $keyData['user_id']]);
    
    if (!$user) {
        echo "❌ USUÁRIO NÃO ENCONTRADO!\n";
        exit(1);
    }
    
    echo "✅ Usuário encontrado: {$user['name']} ({$user['email']})\n";
    echo "   Status do usuário: {$user['status']}\n";
    echo "   Telefone: " . ($user['phone'] ?: 'Não informado') . "\n\n";
    
    // 3. Verificar assinatura
    echo "3️⃣ Verificando assinatura...\n";
    $subscription = $db->fetch("SELECT * FROM subscriptions WHERE id = :id", ['id' => $keyData['subscription_id']]);
    
    if (!$subscription) {
        echo "❌ ASSINATURA NÃO ENCONTRADA!\n";
        exit(1);
    }
    
    echo "✅ Assinatura encontrada:\n";
    echo "   Status: {$subscription['status']}\n";
    echo "   Plano ID: {$subscription['plan_id']}\n";
    echo "   Início: {$subscription['starts_at']}\n";
    echo "   Fim: " . ($subscription['ends_at'] ?: 'Vitalício') . "\n";
    echo "   Status pagamento: {$subscription['payment_status']}\n\n";
    
    // 4. Verificar plano
    echo "4️⃣ Verificando plano...\n";
    $plan = $db->fetch("SELECT * FROM plans WHERE id = :id", ['id' => $subscription['plan_id']]);
    
    if ($plan) {
        echo "✅ Plano encontrado: {$plan['name']}\n";
        echo "   Preço: R$ " . number_format($plan['price'], 2, ',', '.') . "\n";
        echo "   Ciclo: {$plan['billing_cycle']}\n";
        echo "   Status: {$plan['status']}\n\n";
    } else {
        echo "❌ PLANO NÃO ENCONTRADO!\n\n";
    }
    
    // 5. Testar verificação completa
    echo "5️⃣ Testando verificação completa...\n";
    $verification = Auth::verifyApiKey($apiKey);
    
    echo "📊 Resultado da verificação:\n";
    foreach ($verification as $field => $value) {
        echo "   $field: " . (is_bool($value) ? ($value ? 'true' : 'false') : $value) . "\n";
    }
    
    if ($verification['valid']) {
        echo "\n✅ CHAVE VÁLIDA! ✅\n";
    } else {
        echo "\n❌ CHAVE INVÁLIDA: {$verification['reason']} ❌\n";
        
        // 6. Diagnóstico detalhado
        echo "\n6️⃣ Diagnóstico detalhado:\n";
        
        if ($user['status'] !== 'active') {
            echo "❌ Problema: Usuário com status '{$user['status']}' (deveria ser 'active')\n";
        }
        
        if ($keyData['status'] !== 'active') {
            echo "❌ Problema: Chave com status '{$keyData['status']}' (deveria ser 'active')\n";
        }
        
        if ($subscription['status'] !== 'active') {
            echo "❌ Problema: Assinatura com status '{$subscription['status']}' (deveria ser 'active')\n";
        }
        
        if ($subscription['ends_at'] && strtotime($subscription['ends_at']) < time()) {
            echo "❌ Problema: Assinatura expirada em {$subscription['ends_at']}\n";
        }
        
        if ($keyData['monthly_limit'] && $keyData['monthly_usage'] >= $keyData['monthly_limit']) {
            echo "❌ Problema: Limite mensal excedido ({$keyData['monthly_usage']}/{$keyData['monthly_limit']})\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ ERRO: " . $e->getMessage() . "\n";
    exit(1);
}
