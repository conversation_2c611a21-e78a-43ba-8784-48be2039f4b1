<?php
/**
 * Sistema de Views/Templates
 * Sistema de Dashboard E1Copy AI
 */

class View {
    private $templatePath;
    private $data = [];
    
    public function __construct($templatePath = null) {
        $this->templatePath = $templatePath ?: APP_ROOT . '/views';
    }
    
    public function render($template, $data = []) {
        $this->data = array_merge($this->data, $data);
        
        // Extrair variáveis para o escopo do template
        extract($this->data);
        
        // Adicionar funções auxiliares
        $csrf_token = $this->generateCsrfToken();
        $errors = $_SESSION['errors'] ?? [];
        $flash = $_SESSION['flash'] ?? [];
        $old = $_SESSION['old'] ?? [];
        
        // Limpar flash messages após uso
        unset($_SESSION['errors'], $_SESSION['flash'], $_SESSION['old']);
        
        $templateFile = $this->templatePath . '/' . str_replace('.', '/', $template) . '.php';
        
        if (!file_exists($templateFile)) {
            throw new Exception("Template não encontrado: {$templateFile}");
        }
        
        ob_start();
        include $templateFile;
        $content = ob_get_clean();
        
        echo $content;
    }
    
    public function with($key, $value) {
        $this->data[$key] = $value;
        return $this;
    }
    
    private function generateCsrfToken() {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        return $_SESSION['csrf_token'];
    }
    
    // Funções auxiliares para templates
    public static function asset($path) {
        return url('assets/' . ltrim($path, '/'));
    }
    
    public static function route($name, $params = []) {
        // Implementar sistema de rotas nomeadas se necessário
        return url($name);
    }
    
    public static function old($key, $default = '') {
        return $_SESSION['old'][$key] ?? $default;
    }
    
    public static function error($key) {
        return $_SESSION['errors'][$key][0] ?? null;
    }
    
    public static function hasError($key) {
        return isset($_SESSION['errors'][$key]);
    }
    
    public static function flash($key) {
        return $_SESSION['flash'][$key] ?? null;
    }
    
    public static function auth() {
        return Auth::user();
    }
    
    public static function isAuth() {
        return Auth::check();
    }
    
    public static function isAdmin() {
        return Auth::check() && Auth::user()['role'] === 'admin';
    }
}
