<?php
/**
 * Teste das novas configurações de upload
 */

require_once "bootstrap.php";

echo "=== TESTE DAS CONFIGURAÇÕES DE UPLOAD ===\n";

// 1. Verificar variáveis de ambiente
echo "1. Verificando variáveis de ambiente:\n";
echo "   UPLOAD_PATH: " . ($_ENV['UPLOAD_PATH'] ?? 'NÃO DEFINIDO') . "\n";
echo "   UPLOAD_URL_PREFIX: " . ($_ENV['UPLOAD_URL_PREFIX'] ?? 'NÃO DEFINIDO') . "\n";
echo "   APP_URL: " . ($_ENV['APP_URL'] ?? 'NÃO DEFINIDO') . "\n";

// 2. Testar configurações de upload
echo "\n2. Testando configurações de upload:\n";
if (function_exists('config')) {
    $uploadConfig = config('upload');
    if ($uploadConfig) {
        echo "   ✓ Configurações de upload carregadas\n";
        echo "   base_path: " . $uploadConfig['base_path'] . "\n";
        echo "   public_url_base: " . $uploadConfig['public_url_base'] . "\n";
        
        // Testar função de caminho físico
        $physicalPathFunction = $uploadConfig['physical_base_path'];
        if (is_callable($physicalPathFunction)) {
            $physicalPath = $physicalPathFunction();
            echo "   physical_base_path: $physicalPath\n";
        }
    } else {
        echo "   ✗ Erro ao carregar configurações de upload\n";
    }
} else {
    echo "   ✗ Função config() não disponível\n";
}

// 3. Simular geração de URLs
echo "\n3. Simulando geração de URLs:\n";
$postId = 123;
$fileName = 'test_image.jpg';

// URL antiga (problema)
$oldUrl = '/app/uploads/posts/' . $postId . '/' . $fileName;
echo "   URL antiga (problemática): $oldUrl\n";

// URL nova (corrigida)
$uploadUrlPrefix = $_ENV['UPLOAD_URL_PREFIX'] ?? '/uploads';
$newUrl = $uploadUrlPrefix . '/posts/' . $postId . '/' . $fileName;
echo "   URL nova (corrigida): $newUrl\n";

// URL completa no subdomínio
$appUrl = $_ENV['APP_URL'] ?? 'https://app.melhorcupom.shop';
$fullUrl = $appUrl . $newUrl;
echo "   URL completa: $fullUrl\n";

// 4. Testar caminhos físicos
echo "\n4. Testando caminhos físicos:\n";
$uploadPath = $_ENV['UPLOAD_PATH'] ?? '/uploads';

// Caminho com DOCUMENT_ROOT (produção)
if (isset($_SERVER['DOCUMENT_ROOT']) && !empty($_SERVER['DOCUMENT_ROOT'])) {
    $physicalPath = $_SERVER['DOCUMENT_ROOT'] . $uploadPath . '/posts/' . $postId;
    echo "   Caminho físico (produção): $physicalPath\n";
    echo "   DOCUMENT_ROOT existe: SIM (" . $_SERVER['DOCUMENT_ROOT'] . ")\n";
} else {
    echo "   DOCUMENT_ROOT: NÃO DEFINIDO\n";
}

// Caminho fallback (desenvolvimento)
$fallbackPath = __DIR__ . '/uploads/posts/' . $postId;
echo "   Caminho fallback (desenvolvimento): $fallbackPath\n";

// 5. Verificar se diretório de uploads existe
echo "\n5. Verificando estrutura de diretórios:\n";
$uploadsDir = __DIR__ . '/uploads';
echo "   Diretório uploads: $uploadsDir\n";
echo "   Existe: " . (is_dir($uploadsDir) ? 'SIM' : 'NÃO') . "\n";
echo "   Gravável: " . (is_writable($uploadsDir) ? 'SIM' : 'NÃO') . "\n";

$postsDir = $uploadsDir . '/posts';
echo "   Diretório posts: $postsDir\n";
echo "   Existe: " . (is_dir($postsDir) ? 'SIM' : 'NÃO') . "\n";
echo "   Gravável: " . (is_writable($postsDir) ? 'SIM' : 'NÃO') . "\n";

// 6. Testar criação de diretório com nova configuração
echo "\n6. Testando criação de diretório:\n";
$testPostId = 'test_' . time();

// Usar configuração do .env
if (isset($_SERVER['DOCUMENT_ROOT']) && !empty($_SERVER['DOCUMENT_ROOT'])) {
    $testDir = $_SERVER['DOCUMENT_ROOT'] . $uploadPath . '/posts/' . $testPostId;
    echo "   Tentando criar (produção): $testDir\n";
} else {
    $testDir = __DIR__ . '/uploads/posts/' . $testPostId;
    echo "   Tentando criar (desenvolvimento): $testDir\n";
}

if (mkdir($testDir, 0755, true)) {
    echo "   ✓ Diretório criado com sucesso\n";
    
    // Testar criação de arquivo
    $testFile = $testDir . '/' . $fileName;
    if (file_put_contents($testFile, 'conteúdo de teste') !== false) {
        echo "   ✓ Arquivo de teste criado\n";
        
        // Gerar URL usando nova configuração
        $testUrl = $uploadUrlPrefix . '/posts/' . $testPostId . '/' . $fileName;
        echo "   ✓ URL gerada: $testUrl\n";
        
        // Limpar
        unlink($testFile);
        rmdir($testDir);
        echo "   ✓ Arquivos de teste removidos\n";
    } else {
        echo "   ✗ Erro ao criar arquivo de teste\n";
    }
} else {
    echo "   ✗ Erro ao criar diretório\n";
}

echo "\n=== COMPARAÇÃO DE URLs ===\n";
echo "ANTES (problemático):\n";
echo "  - URL no banco: /app/uploads/posts/14/cover_1750699822.png\n";
echo "  - URL completa: https://app.melhorcupom.shop/app/uploads/posts/14/cover_1750699822.png\n";
echo "  - Problema: /app/ duplicado\n";

echo "\nDEPOIS (corrigido):\n";
echo "  - URL no banco: /uploads/posts/14/cover_1750699822.png\n";
echo "  - URL completa: https://app.melhorcupom.shop/uploads/posts/14/cover_1750699822.png\n";
echo "  - ✓ Caminho correto!\n";

echo "\n=== RESUMO ===\n";
echo "✓ Configurações adicionadas ao .env\n";
echo "✓ Controller atualizado para usar configurações\n";
echo "✓ URLs agora usam UPLOAD_URL_PREFIX\n";
echo "✓ Caminhos físicos usam UPLOAD_PATH\n";
echo "✓ Sistema compatível com subdomínios\n";

echo "\n🎉 CONFIGURAÇÃO CONCLUÍDA!\n";
echo "\n📝 PRÓXIMOS PASSOS:\n";
echo "1. Teste o upload no formulário de edição\n";
echo "2. Verifique se as URLs geradas estão corretas\n";
echo "3. Confirme se as imagens são acessíveis no navegador\n";

echo "\n=== FIM TESTE ===\n";
