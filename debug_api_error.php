<?php
/**
 * Debug do erro 500 na API
 */

// Ativar exibição de erros
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "=== DEBUG ERRO 500 NA API ===\n";

try {
    require_once "bootstrap.php";
    
    echo "1. Bootstrap carregado com sucesso\n";
    
    // Testar conexão com banco
    echo "2. Testando conexão com banco...\n";
    $db = Database::getInstance();
    echo "   ✓ Conexão estabelecida\n";
    
    // Testar query simples primeiro
    echo "3. Testando query simples...\n";
    $simpleTest = $db->fetchAll("SELECT COUNT(*) as total FROM client_posts WHERE status = 'pending'");
    echo "   ✓ Posts pendentes: " . $simpleTest[0]['total'] . "\n";
    
    // Testar estrutura das tabelas
    echo "4. Verificando estrutura das tabelas...\n";
    
    // Verificar se tabelas existem
    $tables = ['client_posts', 'client_sites', 'users'];
    foreach ($tables as $table) {
        try {
            $result = $db->fetchAll("SELECT COUNT(*) as count FROM $table LIMIT 1");
            echo "   ✓ Tabela $table existe (" . $result[0]['count'] . " registros)\n";
        } catch (Exception $e) {
            echo "   ✗ Erro na tabela $table: " . $e->getMessage() . "\n";
        }
    }
    
    // Testar JOINs separadamente
    echo "5. Testando JOINs...\n";
    
    // Teste 1: client_posts + client_sites
    try {
        $test1 = $db->fetchAll("
            SELECT COUNT(*) as count
            FROM client_posts cp
            INNER JOIN client_sites cs ON cp.site_id = cs.id
            WHERE cp.status = 'pending'
        ");
        echo "   ✓ JOIN client_posts + client_sites: " . $test1[0]['count'] . " registros\n";
    } catch (Exception $e) {
        echo "   ✗ Erro JOIN client_posts + client_sites: " . $e->getMessage() . "\n";
    }
    
    // Teste 2: client_sites + users
    try {
        $test2 = $db->fetchAll("
            SELECT COUNT(*) as count
            FROM client_sites cs
            INNER JOIN users u ON cs.user_id = u.id
        ");
        echo "   ✓ JOIN client_sites + users: " . $test2[0]['count'] . " registros\n";
    } catch (Exception $e) {
        echo "   ✗ Erro JOIN client_sites + users: " . $e->getMessage() . "\n";
    }
    
    // Teste 3: Query completa sem DISTINCT
    echo "6. Testando query completa sem DISTINCT...\n";
    try {
        $test3 = $db->fetchAll("
            SELECT COUNT(*) as count
            FROM client_posts cp
            INNER JOIN client_sites cs ON cp.site_id = cs.id
            INNER JOIN users u ON cs.user_id = u.id
            WHERE cp.status = 'pending'
        ");
        echo "   ✓ Query completa sem DISTINCT: " . $test3[0]['count'] . " registros\n";
    } catch (Exception $e) {
        echo "   ✗ Erro query completa: " . $e->getMessage() . "\n";
    }
    
    // Teste 4: Query com campos específicos
    echo "7. Testando query com campos específicos...\n";
    try {
        $test4 = $db->fetchAll("
            SELECT 
                cp.id,
                cp.title,
                cs.site_name,
                u.name as user_name
            FROM client_posts cp
            INNER JOIN client_sites cs ON cp.site_id = cs.id
            INNER JOIN users u ON cs.user_id = u.id
            WHERE cp.status = 'pending'
            LIMIT 5
        ");
        echo "   ✓ Query com campos específicos: " . count($test4) . " registros\n";
        foreach ($test4 as $row) {
            echo "     - Post: {$row['id']} | {$row['title']} | Site: {$row['site_name']} | User: {$row['user_name']}\n";
        }
    } catch (Exception $e) {
        echo "   ✗ Erro query com campos: " . $e->getMessage() . "\n";
    }
    
    // Teste 5: Query completa com DISTINCT
    echo "8. Testando query completa com DISTINCT...\n";
    try {
        $test5 = $db->fetchAll("
            SELECT DISTINCT
                cp.id,
                cp.site_id,
                cp.user_id,
                cp.post_type,
                cp.title,
                cp.content,
                cp.excerpt,
                cp.product_name,
                cp.product_description,
                cp.keyword,
                cp.category,
                cp.existing_tags,
                cp.youtube_video,
                cp.product_images,
                cp.post_cover,
                cp.slug,
                cp.status,
                cp.created_at,
                cp.updated_at,
                cs.site_name,
                cs.site_url,
                cs.api_key,
                u.name as user_name,
                u.email as user_email
            FROM client_posts cp
            INNER JOIN client_sites cs ON cp.site_id = cs.id
            INNER JOIN users u ON cs.user_id = u.id
            WHERE cp.status = 'pending'
            AND cs.status = 'connected'
            AND u.status = 'active'
            ORDER BY cp.created_at ASC
            LIMIT 5
        ");
        echo "   ✓ Query completa com DISTINCT: " . count($test5) . " registros\n";
        
        if (count($test5) > 0) {
            echo "   Exemplo de dados:\n";
            $first = $test5[0];
            echo "     - ID: {$first['id']}\n";
            echo "     - Título: {$first['title']}\n";
            echo "     - Site: {$first['site_name']}\n";
            echo "     - Usuário: {$first['user_name']}\n";
            echo "     - Email: {$first['user_email']}\n";
        }
    } catch (Exception $e) {
        echo "   ✗ Erro query completa com DISTINCT: " . $e->getMessage() . "\n";
    }
    
    // Teste 6: Simular o método da API
    echo "9. Simulando método da API...\n";
    try {
        // Simular o que o método getPendingClientPosts faz
        $posts = $db->fetchAll("
            SELECT DISTINCT
                cp.id,
                cp.site_id,
                cp.user_id,
                cp.post_type,
                cp.title,
                cp.content,
                cp.excerpt,
                cp.product_name,
                cp.product_description,
                cp.keyword,
                cp.category,
                cp.existing_tags,
                cp.youtube_video,
                cp.product_images,
                cp.post_cover,
                cp.slug,
                cp.status,
                cp.created_at,
                cp.updated_at,
                cs.site_name,
                cs.site_url,
                cs.api_key,
                u.name as user_name,
                u.email as user_email
            FROM client_posts cp
            INNER JOIN client_sites cs ON cp.site_id = cs.id
            INNER JOIN users u ON cs.user_id = u.id
            WHERE cp.status = 'pending'
            AND cs.status = 'connected'
            AND u.status = 'active'
            ORDER BY cp.created_at ASC
        ");
        
        echo "   ✓ Posts encontrados: " . count($posts) . "\n";
        
        // Processar URLs das imagens (simular processPostImages)
        foreach ($posts as &$post) {
            if (!empty($post['product_images'])) {
                $productImages = json_decode($post['product_images'], true);
                if (is_array($productImages)) {
                    echo "     - Post {$post['id']} tem " . count($productImages) . " imagens\n";
                }
            }
        }
        
        // Organizar por site e cliente
        $postsBySite = [];
        $postsByClient = [];
        
        foreach ($posts as $post) {
            // Por site
            $siteKey = $post['site_url'];
            if (!isset($postsBySite[$siteKey])) {
                $postsBySite[$siteKey] = [
                    'site_info' => [
                        'site_id' => $post['site_id'],
                        'site_name' => $post['site_name'],
                        'site_url' => $post['site_url'],
                        'api_key' => $post['api_key']
                    ],
                    'posts' => []
                ];
            }
            $postsBySite[$siteKey]['posts'][] = $post;
            
            // Por cliente
            $clientKey = $post['user_email'];
            if (!isset($postsByClient[$clientKey])) {
                $postsByClient[$clientKey] = [
                    'user_info' => [
                        'name' => $post['user_name'],
                        'email' => $post['user_email']
                    ],
                    'posts' => []
                ];
            }
            $postsByClient[$clientKey]['posts'][] = $post;
        }
        
        echo "   ✓ Organização concluída:\n";
        echo "     - Sites: " . count($postsBySite) . "\n";
        echo "     - Clientes: " . count($postsByClient) . "\n";
        
        // Simular resposta da API
        $response = [
            'total_pending' => count($posts),
            'clients_with_pending' => count($postsByClient),
            'sites_with_pending' => count($postsBySite),
            'posts_by_client' => array_values($postsByClient),
            'posts_by_site' => array_values($postsBySite),
            'all_posts' => $posts,
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        echo "   ✓ Resposta simulada criada com sucesso\n";
        echo "   ✓ Total pending: " . $response['total_pending'] . "\n";
        echo "   ✓ Clientes: " . $response['clients_with_pending'] . "\n";
        echo "   ✓ Sites: " . $response['sites_with_pending'] . "\n";
        
    } catch (Exception $e) {
        echo "   ✗ Erro na simulação da API: " . $e->getMessage() . "\n";
        echo "   Stack trace: " . $e->getTraceAsString() . "\n";
    }
    
} catch (Exception $e) {
    echo "ERRO GERAL: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}

echo "\n=== FIM DEBUG ===\n";
