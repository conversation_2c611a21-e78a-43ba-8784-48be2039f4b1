<?php
$title = 'Dashboard Admin - E1Copy AI';
$pageTitle = 'Dashboard Administrativo';
ob_start();
?>

<div class="row">
    <!-- Estatísticas -->
    <div class="col-md-3 mb-4">
        <div class="card text-white bg-primary">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title"><?= number_format($stats['total_users']) ?></h4>
                        <p class="card-text">Total de Clientes</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer bg-primary border-0">
                <a href="<?= url('/admin/clients') ?>" class="text-white text-decoration-none">
                    <small>Ver todos <i class="fas fa-arrow-right"></i></small>
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-4">
        <div class="card text-white bg-success">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title"><?= number_format($stats['active_subscriptions']) ?></h4>
                        <p class="card-text">Assinaturas Ativas</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-credit-card fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer bg-success border-0">
                <small>Receita recorrente</small>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-4">
        <div class="card text-white bg-info">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title"><?= number_format($stats['total_api_keys']) ?></h4>
                        <p class="card-text">Chaves Ativas</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-key fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer bg-info border-0">
                <small>APIs em uso</small>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-4">
        <div class="card text-white bg-warning">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title"><?= number_format($stats['today_requests']) ?></h4>
                        <p class="card-text">Requisições Hoje</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-chart-line fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer bg-warning border-0">
                <small>Atividade do dia</small>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Usuários Recentes -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user-plus me-2"></i>
                    Clientes Recentes
                </h5>
                <a href="<?= url('/admin/clients') ?>" class="btn btn-outline-primary btn-sm">
                    Ver Todos
                </a>
            </div>
            <div class="card-body">
                <?php if (!empty($recentUsers)): ?>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Nome</th>
                                    <th>Email</th>
                                    <th>Status</th>
                                    <th>Cadastro</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recentUsers as $user): ?>
                                    <tr>
                                        <td><?= e($user['name']) ?></td>
                                        <td><?= e($user['email']) ?></td>
                                        <td>
                                            <span class="badge bg-<?= $user['status'] === 'active' ? 'success' : 'danger' ?> fs-6">
                                                <?= ucfirst($user['status']) ?>
                                            </span>
                                        </td>
                                        <td><?= date('d/m/Y', strtotime($user['created_at'])) ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <h5>Nenhum cliente cadastrado</h5>
                        <p class="text-muted">Os novos clientes aparecerão aqui.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Atividade Recente da API -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-activity me-2"></i>
                    Atividade Recente da API
                </h5>
            </div>
            <div class="card-body">
                <?php if (!empty($recentActivity)): ?>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Cliente</th>
                                    <th>Endpoint</th>
                                    <th>Status</th>
                                    <th>Horário</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recentActivity as $activity): ?>
                                    <tr>
                                        <td>
                                            <small>
                                                <?= e($activity['name']) ?><br>
                                                <span class="text-muted"><?= e($activity['email']) ?></span>
                                            </small>
                                        </td>
                                        <td>
                                            <code><?= e($activity['endpoint']) ?></code>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?= $activity['response_status'] < 400 ? 'success' : 'danger' ?> fs-6">
                                                <?= $activity['response_status'] ?>
                                            </span>
                                        </td>
                                        <td>
                                            <small><?= date('H:i', strtotime($activity['created_at'])) ?></small>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                        <h5>Nenhuma atividade</h5>
                        <p class="text-muted">As requisições da API aparecerão aqui.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Ações Rápidas -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    Ações Rápidas
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <div class="d-grid">
                            <a href="<?= url('/admin/clients') ?>" class="btn btn-outline-primary">
                                <i class="fas fa-users me-2"></i>
                                Gerenciar Clientes
                            </a>
                        </div>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <div class="d-grid">
                            <a href="<?= url('/admin/settings') ?>" class="btn btn-outline-secondary">
                                <i class="fas fa-cog me-2"></i>
                                Configurações
                            </a>
                        </div>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <div class="d-grid">
                            <button type="button" class="btn btn-outline-info" onclick="refreshStats()">
                                <i class="fas fa-sync-alt me-2"></i>
                                Atualizar Dados
                            </button>
                        </div>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <div class="d-grid">
                            <button type="button" class="btn btn-outline-warning" data-bs-toggle="modal" data-bs-target="#systemInfoModal">
                                <i class="fas fa-info-circle me-2"></i>
                                Info do Sistema
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de Informações do Sistema -->
<div class="modal fade" id="systemInfoModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Informações do Sistema</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Servidor</h6>
                        <ul class="list-unstyled">
                            <li><strong>PHP:</strong> <?= PHP_VERSION ?></li>
                            <li><strong>Servidor:</strong> <?= $_SERVER['SERVER_SOFTWARE'] ?? 'N/A' ?></li>
                            <li><strong>Sistema:</strong> <?= PHP_OS ?></li>
                            <li><strong>Memória:</strong> <?= ini_get('memory_limit') ?></li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>Aplicação</h6>
                        <ul class="list-unstyled">
                            <li><strong>Versão:</strong> 1.0.0</li>
                            <li><strong>Ambiente:</strong> <?= config('app.debug') ? 'Desenvolvimento' : 'Produção' ?></li>
                            <li><strong>Timezone:</strong> <?= date_default_timezone_get() ?></li>
                            <li><strong>Uptime:</strong> <?= round((microtime(true) - APP_START_TIME) * 1000, 2) ?>ms</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
            </div>
        </div>
    </div>
</div>

<?php
$content = ob_get_clean();
include __DIR__ . '/../layouts/app.php';
?>

<script>
function refreshStats() {
    // Simular atualização (recarregar página)
    location.reload();
}

// Auto-refresh a cada 5 minutos
setInterval(function() {
    location.reload();
}, 300000);
</script>
