<?php
/**
 * Configurações de Upload
 * Sistema de Dashboard E1Copy AI
 */

return [
    // Diretório base para uploads (configurável via .env)
    'base_path' => $_ENV['UPLOAD_PATH'] ?? '/uploads',
    
    // Diretório físico base (será calculado automaticamente)
    'physical_base_path' => function() {
        $uploadPath = $_ENV['UPLOAD_PATH'] ?? '/uploads';
        
        // Tentar usar DOCUMENT_ROOT primeiro
        if (isset($_SERVER['DOCUMENT_ROOT']) && !empty($_SERVER['DOCUMENT_ROOT'])) {
            return $_SERVER['DOCUMENT_ROOT'] . $uploadPath;
        }
        
        // Fallback para caminho relativo
        return __DIR__ . '/../uploads';
    },
    
    // Configurações específicas para posts
    'posts' => [
        'path' => '/posts',
        'max_file_size' => 5 * 1024 * 1024, // 5MB
        'allowed_types' => [
            'image/jpeg',
            'image/jpg', 
            'image/png',
            'image/webp'
        ],
        'allowed_extensions' => ['jpg', 'jpeg', 'png', 'webp'],
        
        // Configurações de nomes de arquivo
        'cover_prefix' => 'cover_',
        'image_prefix' => 'image_',
        
        // Permissões de diretório
        'directory_permissions' => 0755
    ],
    
    // Configurações de segurança
    'security' => [
        'scan_uploads' => true,
        'check_mime_type' => true,
        'max_filename_length' => 255,
        'forbidden_extensions' => [
            'php', 'php3', 'php4', 'php5', 'phtml',
            'exe', 'bat', 'cmd', 'com', 'scr',
            'js', 'html', 'htm', 'css'
        ]
    ],
    
    // URLs públicas (configurável via .env)
    'public_url_base' => $_ENV['UPLOAD_URL_PREFIX'] ?? '/uploads'
];
