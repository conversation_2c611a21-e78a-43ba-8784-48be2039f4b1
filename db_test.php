<?php
$host = "srv1845.hstgr.io";
$db = "u880879026_appDash";
$user = "u880879026_userDash";
$pass = ":sX=zys@2";

try {
    $pdo = new PDO("mysql:host=$host;dbname=$db;charset=utf8mb4", $user, $pass);
    echo "Connected to database\n";
    
    // Check if columns exist
    $stmt = $pdo->query("DESCRIBE client_posts");
    $columns = $stmt->fetchAll();
    $fields = array_column($columns, "Field");
    
    if (!in_array("product_images", $fields)) {
        echo "Adding product_images column...\n";
        $pdo->exec("ALTER TABLE client_posts ADD COLUMN product_images TEXT NULL");
        echo "product_images column added\n";
    } else {
        echo "product_images column exists\n";
    }
    
    if (!in_array("post_cover", $fields)) {
        echo "Adding post_cover column...\n";
        $pdo->exec("ALTER TABLE client_posts ADD COLUMN post_cover TEXT NULL");
        echo "post_cover column added\n";
    } else {
        echo "post_cover column exists\n";
    }
    
    // Test data
    $stmt = $pdo->query("SELECT id FROM client_posts LIMIT 1");
    $post = $stmt->fetch();
    if ($post) {
        $testImages = json_encode(["/app/uploads/posts/test/img1.jpg", "/app/uploads/posts/test/img2.jpg"]);
        $testCover = "/app/uploads/posts/test/cover.jpg";
        
        $stmt = $pdo->prepare("UPDATE client_posts SET product_images = ?, post_cover = ? WHERE id = ?");
        $stmt->execute([$testImages, $testCover, $post["id"]]);
        echo "Test data inserted for post ID " . $post["id"] . "\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
