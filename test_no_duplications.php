<?php
/**
 * Teste para verificar se as duplicações foram eliminadas
 */

// Simular requisição HTTP
$_SERVER['REQUEST_METHOD'] = 'GET';
$_SERVER['REQUEST_URI'] = '/api/v1/client-posts/pending';

require_once "bootstrap.php";

echo "=== TESTE ANTI-DUPLICAÇÃO ===\n";

try {
    // Instanciar controller
    require_once "controllers/ApiController.php";
    $controller = new ApiController();
    
    // Capturar output
    ob_start();
    $result = $controller->getPendingClientPosts();
    $output = ob_get_clean();
    
    echo "Output capturado:\n$output\n";
    
    // Se o resultado for um objeto ApiResponse, extrair os dados
    if (is_object($result) && method_exists($result, 'getData')) {
        $data = $result->getData();
    } else {
        $data = $result;
    }
    
    if (isset($data['all_posts'])) {
        $posts = $data['all_posts'];
        
        echo "\n=== ANÁLISE DE DUPLICAÇÕES ===\n";
        echo "Total de posts retornados: " . count($posts) . "\n";
        
        // Verificar duplicações por ID
        $postIds = [];
        $duplicatedIds = [];
        
        foreach ($posts as $post) {
            $id = $post['id'];
            if (in_array($id, $postIds)) {
                $duplicatedIds[] = $id;
                echo "❌ DUPLICAÇÃO ENCONTRADA: Post ID $id\n";
            } else {
                $postIds[] = $id;
            }
        }
        
        if (empty($duplicatedIds)) {
            echo "✅ NENHUMA DUPLICAÇÃO POR ID ENCONTRADA!\n";
        } else {
            echo "❌ Total de IDs duplicados: " . count(array_unique($duplicatedIds)) . "\n";
        }
        
        // Verificar clientes únicos
        $userEmails = [];
        foreach ($posts as $post) {
            $email = $post['user_email'];
            if (!in_array($email, $userEmails)) {
                $userEmails[] = $email;
            }
        }
        
        echo "\n=== ANÁLISE DE CLIENTES ===\n";
        echo "Clientes únicos encontrados: " . count($userEmails) . "\n";
        
        foreach ($userEmails as $email) {
            $clientPosts = array_filter($posts, function($p) use ($email) {
                return $p['user_email'] === $email;
            });
            echo "- $email: " . count($clientPosts) . " posts\n";
        }
        
        // Verificar sites únicos
        $siteUrls = [];
        foreach ($posts as $post) {
            $url = $post['site_url'];
            if (!in_array($url, $siteUrls)) {
                $siteUrls[] = $url;
            }
        }
        
        echo "\n=== ANÁLISE DE SITES ===\n";
        echo "Sites únicos encontrados: " . count($siteUrls) . "\n";
        
        foreach ($siteUrls as $url) {
            $sitePosts = array_filter($posts, function($p) use ($url) {
                return $p['site_url'] === $url;
            });
            echo "- $url: " . count($sitePosts) . " posts\n";
        }
        
        // Verificar estrutura da resposta
        echo "\n=== ESTRUTURA DA RESPOSTA ===\n";
        echo "total_pending: " . ($data['total_pending'] ?? 'N/A') . "\n";
        echo "clients_with_pending: " . ($data['clients_with_pending'] ?? 'N/A') . "\n";
        echo "sites_with_pending: " . ($data['sites_with_pending'] ?? 'N/A') . "\n";
        echo "posts_by_client: " . (isset($data['posts_by_client']) ? count($data['posts_by_client']) : 'N/A') . " grupos\n";
        echo "posts_by_site: " . (isset($data['posts_by_site']) ? count($data['posts_by_site']) : 'N/A') . " grupos\n";
        
        // Verificar consistência
        $totalInAllPosts = count($posts);
        $totalInResponse = $data['total_pending'] ?? 0;
        
        if ($totalInAllPosts === $totalInResponse) {
            echo "✅ CONSISTÊNCIA: total_pending bate com all_posts\n";
        } else {
            echo "❌ INCONSISTÊNCIA: total_pending ($totalInResponse) != all_posts ($totalInAllPosts)\n";
        }
        
        // Verificar se há múltiplos clientes
        if (count($userEmails) > 1) {
            echo "✅ MÚLTIPLOS CLIENTES: Sistema multi-tenant funcionando\n";
        } else {
            echo "⚠️  APENAS 1 CLIENTE: Pode haver problema no sistema multi-tenant\n";
        }
        
        // Mostrar exemplo de post
        if (!empty($posts)) {
            echo "\n=== EXEMPLO DE POST ===\n";
            $firstPost = $posts[0];
            echo "ID: " . $firstPost['id'] . "\n";
            echo "Título: " . substr($firstPost['title'], 0, 50) . "...\n";
            echo "Cliente: " . $firstPost['user_name'] . " (" . $firstPost['user_email'] . ")\n";
            echo "Site: " . $firstPost['site_name'] . " (" . $firstPost['site_url'] . ")\n";
            echo "API Key: " . (isset($firstPost['api_key']) ? substr($firstPost['api_key'], 0, 20) . "..." : 'N/A') . "\n";
        }
        
    } else {
        echo "❌ Estrutura de resposta inesperada\n";
        echo "Dados recebidos: " . print_r($data, true) . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ ERRO: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}

echo "\n=== INSTRUÇÕES ===\n";
echo "1. Execute o script SQL fix_duplications.sql no phpMyAdmin\n";
echo "2. Teste a API novamente: /api/v1/client-posts/pending\n";
echo "3. Verifique se não há mais duplicações\n";
echo "4. Se ainda houver problemas, verifique os logs do servidor\n";

echo "\n=== FIM TESTE ===\n";
