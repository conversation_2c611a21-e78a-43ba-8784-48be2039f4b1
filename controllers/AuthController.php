<?php
/**
 * Controller de Autenticação
 * Sistema de Dashboard E1Copy AI
 */

class AuthController extends Controller {
    
    public function showLogin() {
        // Se já estiver logado, redirecionar
        if (Auth::check()) {
            if (Auth::isAdmin()) {
                redirect(url('/admin/dashboard'));
            } else {
                redirect(url('/client/dashboard'));
            }
        }
        
        $this->view('auth.login');
    }
    
    public function login() {
        // Verificar CSRF token
        if (!$this->verifyCsrfToken($this->input('_token'))) {
            $this->flash('error', 'Token de segurança inválido');
            return $this->withInput()->redirect(url('/login'));
        }
        
        // Validar dados
        $validation = $this->validate([
            'email' => 'required|email',
            'password' => 'required|min:6'
        ]);
        
        if ($validation !== true) {
            return $this->withErrors($validation)->withInput()->redirect(url('/login'));
        }
        
        $email = $this->input('email');
        $password = $this->input('password');
        
        if (Auth::attempt($email, $password)) {
            // Login bem-sucedido
            $intendedUrl = $_SESSION['intended_url'] ?? null;
            unset($_SESSION['intended_url']);
            
            if ($intendedUrl) {
                redirect($intendedUrl);
            } elseif (Auth::isAdmin()) {
                redirect(url('/admin/dashboard'));
            } else {
                redirect(url('/client/dashboard'));
            }
        } else {
            $this->flash('error', 'Email ou senha incorretos');
            return $this->withInput()->redirect(url('/login'));
        }
    }
    
    public function logout() {
        Auth::logout();
        redirect(url('/login'));
    }
}
