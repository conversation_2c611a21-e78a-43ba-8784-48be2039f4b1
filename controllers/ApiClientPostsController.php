<?php
/**
 * API Controller para Posts dos Clientes
 * Sistema de Dashboard E1Copy AI
 */

class ApiClientPostsController extends Controller {
    
    /**
     * Lista posts dos clientes com filtros
     * GET /api/v1/client-posts
     */
    public function index() {
        try {
            // Parâmetros de filtro
            $status = $this->input('status', '');
            $postType = $this->input('post_type', '');
            $userId = $this->input('user_id', '');
            $siteId = $this->input('site_id', '');
            $limit = min(100, max(1, (int)$this->input('limit', 50)));
            $offset = max(0, (int)$this->input('offset', 0));
            $includeContent = $this->input('include_content', 'true') === 'true';
            
            // Construir condições da query - simplificada como no debug
            $conditions = ['u.status = \'active\''];
            $params = [];

            // Filtro por status
            if ($status) {
                $conditions[] = "cp.status = :status";
                $params['status'] = $status;
            }

            // Filtro por tipo de post
            if ($postType) {
                $conditions[] = "cp.post_type = :post_type";
                $params['post_type'] = $postType;
            }

            // Filtro por usuário
            if ($userId) {
                $conditions[] = "cp.user_id = :user_id";
                $params['user_id'] = $userId;
            }

            // Filtro por site
            if ($siteId) {
                $conditions[] = "cp.site_id = :site_id";
                $params['site_id'] = $siteId;
            }

            $whereClause = implode(' AND ', $conditions);
            
            // Campos a selecionar (com ou sem conteúdo completo)
            $selectFields = "
                cp.id,
                cp.site_id,
                cp.user_id,
                cp.post_type,
                cp.title,
                cp.excerpt,
                cp.keywords,
                cp.category,
                cp.tags,
                cp.slug,
                cp.product_name,
                cp.product_url,
                cp.product_price,
                cp.product_rating,
                cp.product_pros,
                cp.product_cons,
                cp.affiliate_link,
                cp.status,
                cp.wordpress_post_id,
                cp.error_message,
                cp.created_at,
                cp.updated_at,
                cp.published_at,
                COALESCE(cs.site_name, 'Unknown Site') as site_name,
                COALESCE(cs.site_url, 'unknown') as site_url,
                COALESCE(u.name, 'Unknown User') as user_name,
                COALESCE(u.email, 'unknown') as user_email,
                '' as api_key
            ";
            
            // Incluir conteúdo completo se solicitado
            if ($includeContent) {
                $selectFields .= ", cp.content";
            }
            
            // Query principal - EXATAMENTE igual ao debug que funciona
            if ($status === 'pending') {
                // Para posts pendentes, usar query específica que funciona
                $posts = $this->db->fetchAll("
                    SELECT {$selectFields}
                    FROM client_posts cp
                    INNER JOIN users u ON cp.user_id = u.id
                    LEFT JOIN client_sites cs ON cp.site_id = cs.id
                    WHERE cp.status = 'pending' AND u.status = 'active'
                    ORDER BY cp.created_at DESC
                    LIMIT {$limit} OFFSET {$offset}
                ");

                $totalCount = $this->db->fetch("
                    SELECT COUNT(cp.id) as total
                    FROM client_posts cp
                    INNER JOIN users u ON cp.user_id = u.id
                    WHERE cp.status = 'pending' AND u.status = 'active'
                ")['total'];
            } else {
                // Para outros filtros, usar query com parâmetros
                $posts = $this->db->fetchAll("
                    SELECT {$selectFields}
                    FROM client_posts cp
                    INNER JOIN users u ON cp.user_id = u.id
                    LEFT JOIN client_sites cs ON cp.site_id = cs.id
                    WHERE {$whereClause}
                    ORDER BY cp.created_at DESC
                    LIMIT {$limit} OFFSET {$offset}
                ", $params);

                $totalCount = $this->db->fetch("
                    SELECT COUNT(cp.id) as total
                    FROM client_posts cp
                    INNER JOIN users u ON cp.user_id = u.id
                    WHERE {$whereClause}
                ", $params)['total'];
            }
            
            // Processar dados dos posts
            foreach ($posts as &$post) {
                // Converter campos JSON se necessário
                if (!empty($post['tags'])) {
                    $post['tags'] = json_decode($post['tags'], true) ?: [];
                }
                
                if (!empty($post['product_pros'])) {
                    $post['product_pros'] = json_decode($post['product_pros'], true) ?: [];
                }
                
                if (!empty($post['product_cons'])) {
                    $post['product_cons'] = json_decode($post['product_cons'], true) ?: [];
                }
                
                // Converter tipos numéricos
                $post['id'] = (int)$post['id'];
                $post['site_id'] = (int)$post['site_id'];
                $post['user_id'] = (int)$post['user_id'];
                $post['wordpress_post_id'] = $post['wordpress_post_id'] ? (int)$post['wordpress_post_id'] : null;
                $post['product_price'] = $post['product_price'] ? (float)$post['product_price'] : null;
                $post['product_rating'] = $post['product_rating'] ? (float)$post['product_rating'] : null;
                
                // Adicionar informações de imagens se existirem
                $post['images'] = [];
                for ($i = 1; $i <= 5; $i++) {
                    $imageField = "product_images{$i}";
                    if (!empty($post[$imageField])) {
                        $post['images'][] = $post[$imageField];
                    }
                    unset($post[$imageField]); // Remover campos individuais
                }
            }
            
            // Organizar posts por cliente e por site - simplificado como no debug
            $postsByClient = [];
            $postsBySite = [];

            foreach ($posts as $post) {
                $clientKey = $post['user_id'] . '_' . $post['user_email'];
                $siteKey = $post['site_id'];

                // Agrupar por cliente
                if (!isset($postsByClient[$clientKey])) {
                    $postsByClient[$clientKey] = [
                        'client_info' => [
                            'user_id' => (int)$post['user_id'],
                            'user_name' => $post['user_name'],
                            'user_email' => $post['user_email']
                        ],
                        'posts' => [],
                        'total_posts' => 0
                    ];
                }
                $postsByClient[$clientKey]['posts'][] = $post;
                $postsByClient[$clientKey]['total_posts']++;

                // Agrupar por site
                if (!isset($postsBySite[$siteKey])) {
                    $postsBySite[$siteKey] = [
                        'site_info' => [
                            'site_id' => (int)$post['site_id'],
                            'site_name' => $post['site_name'],
                            'site_url' => $post['site_url'],
                            'user_name' => $post['user_name'],
                            'user_email' => $post['user_email'],
                            'api_key' => $post['api_key']
                        ],
                        'posts' => [],
                        'total_posts' => 0
                    ];
                }
                $postsBySite[$siteKey]['posts'][] = $post;
                $postsBySite[$siteKey]['total_posts']++;
            }

            // Estatísticas
            $stats = [
                'total_posts' => (int)$totalCount,
                'returned_posts' => count($posts),
                'total_clients' => count($postsByClient),
                'total_sites' => count($postsBySite),
                'limit' => $limit,
                'offset' => $offset,
                'has_more' => ($offset + $limit) < $totalCount
            ];
            
            // Resposta da API
            $this->json([
                'success' => true,
                'data' => [
                    'posts' => $posts,
                    'posts_by_client' => array_values($postsByClient),
                    'posts_by_site' => array_values($postsBySite),
                    'stats' => $stats,
                    'filters_applied' => [
                        'status' => $status ?: null,
                        'post_type' => $postType ?: null,
                        'user_id' => $userId ?: null,
                        'site_id' => $siteId ?: null,
                        'include_content' => $includeContent
                    ]
                ],
                'timestamp' => date('Y-m-d H:i:s')
            ]);

        } catch (Exception $e) {
            $this->json([
                'success' => false,
                'error' => $e->getMessage(),
                'timestamp' => date('Y-m-d H:i:s')
            ], 500);
        }
    }

    /**
     * Busca um post específico por ID
     * GET /api/v1/client-posts/{id}
     */
    public function show($id) {
        try {
            $post = $this->db->fetch("
                SELECT
                    cp.*,
                    cs.site_name,
                    cs.site_url,
                    u.name as user_name,
                    u.email as user_email,
                    ak.api_key
                FROM client_posts cp
                LEFT JOIN client_sites cs ON cp.site_id = cs.id
                LEFT JOIN users u ON cp.user_id = u.id
                LEFT JOIN api_keys ak ON cs.api_key_id = ak.id
                WHERE cp.id = :id AND u.status = 'active'
            ", ['id' => $id]);

            if (!$post) {
                $this->json([
                    'success' => false,
                    'error' => 'Post não encontrado',
                    'timestamp' => date('Y-m-d H:i:s')
                ], 404);
                return;
            }

            // Processar dados do post
            $this->processPostData($post);

            $this->json([
                'success' => true,
                'data' => $post,
                'timestamp' => date('Y-m-d H:i:s')
            ]);

        } catch (Exception $e) {
            $this->json([
                'success' => false,
                'error' => $e->getMessage(),
                'timestamp' => date('Y-m-d H:i:s')
            ], 500);
        }
    }

    /**
     * Retorna estatísticas dos posts dos clientes
     * GET /api/v1/client-posts/stats
     */
    public function stats() {
        try {
            $stats = $this->db->fetch("
                SELECT
                    COUNT(*) as total_posts,
                    COUNT(DISTINCT cp.user_id) as total_clients,
                    COUNT(DISTINCT cp.site_id) as total_sites,
                    SUM(CASE WHEN cp.status = 'pending' THEN 1 ELSE 0 END) as pending_posts,
                    SUM(CASE WHEN cp.status = 'processing' THEN 1 ELSE 0 END) as processing_posts,
                    SUM(CASE WHEN cp.status = 'completed' THEN 1 ELSE 0 END) as completed_posts,
                    SUM(CASE WHEN cp.status = 'published' THEN 1 ELSE 0 END) as published_posts,
                    SUM(CASE WHEN cp.status = 'failed' THEN 1 ELSE 0 END) as failed_posts,
                    SUM(CASE WHEN cp.status = 'draft' THEN 1 ELSE 0 END) as draft_posts,
                    SUM(CASE WHEN cp.post_type = 'article' THEN 1 ELSE 0 END) as articles,
                    SUM(CASE WHEN cp.post_type = 'product_review' THEN 1 ELSE 0 END) as product_reviews
                FROM client_posts cp
                INNER JOIN users u ON cp.user_id = u.id
                WHERE u.status = 'active'
            ");

            // Estatísticas por período
            $dailyStats = $this->db->fetchAll("
                SELECT
                    DATE(cp.created_at) as date,
                    COUNT(*) as posts_created,
                    SUM(CASE WHEN cp.status = 'published' THEN 1 ELSE 0 END) as posts_published
                FROM client_posts cp
                INNER JOIN users u ON cp.user_id = u.id
                WHERE u.status = 'active'
                AND cp.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                GROUP BY DATE(cp.created_at)
                ORDER BY date DESC
            ");

            // Top clientes por posts
            $topClients = $this->db->fetchAll("
                SELECT
                    u.name as user_name,
                    u.email as user_email,
                    COUNT(cp.id) as total_posts,
                    SUM(CASE WHEN cp.status = 'published' THEN 1 ELSE 0 END) as published_posts
                FROM client_posts cp
                INNER JOIN users u ON cp.user_id = u.id
                WHERE u.status = 'active'
                GROUP BY u.id, u.name, u.email
                ORDER BY total_posts DESC
                LIMIT 10
            ");

            $this->json([
                'success' => true,
                'data' => [
                    'general_stats' => $stats,
                    'daily_stats' => $dailyStats,
                    'top_clients' => $topClients
                ],
                'timestamp' => date('Y-m-d H:i:s')
            ]);

        } catch (Exception $e) {
            $this->json([
                'success' => false,
                'error' => $e->getMessage(),
                'timestamp' => date('Y-m-d H:i:s')
            ], 500);
        }
    }

    /**
     * Processa dados de um post (conversões de tipo, etc.)
     */
    private function processPostData(&$post) {
        // Converter campos JSON
        if (!empty($post['tags'])) {
            $post['tags'] = json_decode($post['tags'], true) ?: [];
        }

        if (!empty($post['product_pros'])) {
            $post['product_pros'] = json_decode($post['product_pros'], true) ?: [];
        }

        if (!empty($post['product_cons'])) {
            $post['product_cons'] = json_decode($post['product_cons'], true) ?: [];
        }

        // Converter tipos numéricos
        $post['id'] = (int)$post['id'];
        $post['site_id'] = (int)$post['site_id'];
        $post['user_id'] = (int)$post['user_id'];
        $post['wordpress_post_id'] = $post['wordpress_post_id'] ? (int)$post['wordpress_post_id'] : null;
        $post['product_price'] = $post['product_price'] ? (float)$post['product_price'] : null;
        $post['product_rating'] = $post['product_rating'] ? (float)$post['product_rating'] : null;

        // Processar imagens
        $post['images'] = [];
        for ($i = 1; $i <= 5; $i++) {
            $imageField = "product_images{$i}";
            if (!empty($post[$imageField])) {
                $post['images'][] = $post[$imageField];
            }
            unset($post[$imageField]);
        }
    }

    /**
     * Debug endpoint para verificar posts pendentes
     * GET /api/v1/client-posts/debug
     */
    public function debug() {
        try {
            // 1. Verificar posts pendentes por cliente
            $postsByClient = $this->db->fetchAll("
                SELECT
                    u.id as user_id,
                    u.name as user_name,
                    u.email as user_email,
                    u.status as user_status,
                    COUNT(cp.id) as total_posts,
                    COUNT(CASE WHEN cp.status = 'pending' THEN 1 END) as pending_posts
                FROM users u
                LEFT JOIN client_posts cp ON u.id = cp.user_id
                WHERE u.role = 'client'
                GROUP BY u.id, u.name, u.email, u.status
                HAVING pending_posts > 0 OR total_posts > 0
                ORDER BY pending_posts DESC, total_posts DESC
            ");

            // 2. Verificar posts pendentes únicos
            $pendingPosts = $this->db->fetchAll("
                SELECT DISTINCT
                    cp.id,
                    cp.title,
                    cp.status,
                    cp.user_id,
                    u.name as user_name,
                    u.email as user_email,
                    u.status as user_status,
                    cp.created_at
                FROM client_posts cp
                INNER JOIN users u ON cp.user_id = u.id
                WHERE cp.status = 'pending' AND u.status = 'active'
                ORDER BY cp.created_at DESC
            ");

            // 3. Verificar query da API
            $apiQuery = "
                SELECT
                    cp.id,
                    cp.title,
                    cp.user_id,
                    u.name as user_name,
                    u.email as user_email,
                    cs.site_name
                FROM client_posts cp
                LEFT JOIN client_sites cs ON cp.site_id = cs.id
                LEFT JOIN users u ON cp.user_id = u.id
                LEFT JOIN api_keys ak ON cs.api_key_id = ak.id
                WHERE u.status = 'active' AND cp.status = 'pending'
                GROUP BY cp.id
                ORDER BY cp.created_at DESC
            ";

            $apiResults = $this->db->fetchAll($apiQuery);

            // 4. Verificar clientes ativos
            $activeClients = $this->db->fetchAll("
                SELECT id, name, email, status, created_at
                FROM users
                WHERE role = 'client'
                ORDER BY created_at DESC
            ");

            $this->json([
                'success' => true,
                'debug_data' => [
                    'posts_by_client' => $postsByClient,
                    'pending_posts_unique' => $pendingPosts,
                    'api_query_results' => $apiResults,
                    'active_clients' => $activeClients,
                    'summary' => [
                        'total_clients_with_posts' => count($postsByClient),
                        'total_pending_posts' => count($pendingPosts),
                        'api_results_count' => count($apiResults),
                        'total_active_clients' => count($activeClients)
                    ]
                ],
                'timestamp' => date('Y-m-d H:i:s')
            ]);

        } catch (Exception $e) {
            $this->json([
                'success' => false,
                'error' => $e->getMessage(),
                'timestamp' => date('Y-m-d H:i:s')
            ], 500);
        }
    }

    /**
     * Endpoint de teste com dados simulados de múltiplos clientes
     * GET /api/v1/client-posts/test-multiple
     */
    public function testMultiple() {
        try {
            // Buscar posts reais
            $realPosts = $this->db->fetchAll("
                SELECT
                    cp.id,
                    cp.title,
                    cp.post_type,
                    cp.status,
                    cp.user_id,
                    u.name as user_name,
                    u.email as user_email,
                    cs.site_name,
                    cp.created_at
                FROM client_posts cp
                INNER JOIN users u ON cp.user_id = u.id
                LEFT JOIN client_sites cs ON cp.site_id = cs.id
                WHERE cp.status = 'pending' AND u.status = 'active'
                ORDER BY cp.created_at DESC
                LIMIT 5
            ");

            // Simular posts de outros clientes
            $simulatedPosts = [
                [
                    'id' => 999,
                    'title' => 'Review do Samsung Galaxy S24 - Análise Completa',
                    'post_type' => 'product_review',
                    'status' => 'pending',
                    'user_id' => 999,
                    'user_name' => 'Maria Santos',
                    'user_email' => '<EMAIL>',
                    'site_name' => 'Tech Reviews Blog',
                    'created_at' => date('Y-m-d H:i:s', strtotime('-2 hours'))
                ],
                [
                    'id' => 998,
                    'title' => 'Melhores Notebooks 2024 - Guia Completo',
                    'post_type' => 'article',
                    'status' => 'pending',
                    'user_id' => 998,
                    'user_name' => 'João Oliveira',
                    'user_email' => '<EMAIL>',
                    'site_name' => 'Dicas de Tecnologia',
                    'created_at' => date('Y-m-d H:i:s', strtotime('-4 hours'))
                ],
                [
                    'id' => 997,
                    'title' => 'Airfryer Philips Walita - Vale a Pena?',
                    'post_type' => 'product_review',
                    'status' => 'pending',
                    'user_id' => 999,
                    'user_name' => 'Maria Santos',
                    'user_email' => '<EMAIL>',
                    'site_name' => 'Tech Reviews Blog',
                    'created_at' => date('Y-m-d H:i:s', strtotime('-6 hours'))
                ],
                [
                    'id' => 996,
                    'title' => 'Como Escolher o Melhor Smartphone em 2024',
                    'post_type' => 'article',
                    'status' => 'pending',
                    'user_id' => 997,
                    'user_name' => 'Ana Costa',
                    'user_email' => '<EMAIL>',
                    'site_name' => 'Guia do Consumidor',
                    'created_at' => date('Y-m-d H:i:s', strtotime('-8 hours'))
                ]
            ];

            // Combinar posts reais e simulados
            $allPosts = array_merge($realPosts, $simulatedPosts);

            // Organizar por cliente
            $postsByClient = [];
            foreach ($allPosts as $post) {
                $clientKey = $post['user_id'] . '_' . $post['user_email'];

                if (!isset($postsByClient[$clientKey])) {
                    $postsByClient[$clientKey] = [
                        'client_info' => [
                            'user_id' => (int)$post['user_id'],
                            'user_name' => $post['user_name'],
                            'user_email' => $post['user_email']
                        ],
                        'posts' => [],
                        'total_posts' => 0
                    ];
                }
                $postsByClient[$clientKey]['posts'][] = $post;
                $postsByClient[$clientKey]['total_posts']++;
            }

            $this->json([
                'success' => true,
                'data' => [
                    'posts' => $allPosts,
                    'posts_by_client' => array_values($postsByClient),
                    'stats' => [
                        'total_posts' => count($allPosts),
                        'real_posts' => count($realPosts),
                        'simulated_posts' => count($simulatedPosts),
                        'total_clients' => count($postsByClient)
                    ]
                ],
                'note' => 'Este endpoint combina posts reais com dados simulados para demonstrar como a API funciona com múltiplos clientes',
                'timestamp' => date('Y-m-d H:i:s')
            ]);

        } catch (Exception $e) {
            $this->json([
                'success' => false,
                'error' => $e->getMessage(),
                'timestamp' => date('Y-m-d H:i:s')
            ], 500);
        }
    }

    /**
     * Debug específico para organização por cliente
     * GET /api/v1/client-posts/debug-organization
     */
    public function debugOrganization() {
        try {
            // Buscar posts pendentes exatamente como no método principal
            $status = 'pending';
            $conditions = ['u.status = \'active\''];
            $params = [];

            if ($status) {
                $conditions[] = "cp.status = :status";
                $params['status'] = $status;
            }

            $whereClause = implode(' AND ', $conditions);

            // Query exata do método principal
            $posts = $this->db->fetchAll("
                SELECT
                    cp.id,
                    cp.site_id,
                    cp.user_id,
                    cp.post_type,
                    cp.title,
                    cp.status,
                    cp.created_at,
                    COALESCE(cs.site_name, 'Unknown Site') as site_name,
                    COALESCE(cs.site_url, 'unknown') as site_url,
                    COALESCE(u.name, 'Unknown User') as user_name,
                    COALESCE(u.email, 'unknown') as user_email,
                    COALESCE(ak.api_key, '') as api_key
                FROM client_posts cp
                LEFT JOIN client_sites cs ON cp.site_id = cs.id
                LEFT JOIN users u ON cp.user_id = u.id
                LEFT JOIN api_keys ak ON cs.api_key_id = ak.id
                WHERE {$whereClause}
                GROUP BY cp.id
                ORDER BY cp.created_at DESC
            ", $params);

            // Simular a organização por cliente
            $postsByClient = [];
            $processedPosts = [];
            $debugInfo = [];

            foreach ($posts as $post) {
                $postKey = $post['id'] . '_' . $post['user_id'] . '_' . $post['site_id'];
                $clientKey = $post['user_id'] . '_' . $post['user_email'];

                $debugInfo[] = [
                    'post_id' => $post['id'],
                    'post_key' => $postKey,
                    'client_key' => $clientKey,
                    'user_id' => $post['user_id'],
                    'user_name' => $post['user_name'],
                    'already_processed' => isset($processedPosts[$postKey])
                ];

                if (isset($processedPosts[$postKey])) {
                    continue;
                }
                $processedPosts[$postKey] = true;

                if (!isset($postsByClient[$clientKey])) {
                    $postsByClient[$clientKey] = [
                        'client_info' => [
                            'user_id' => (int)$post['user_id'],
                            'user_name' => $post['user_name'],
                            'user_email' => $post['user_email']
                        ],
                        'posts' => [],
                        'total_posts' => 0
                    ];
                }
                $postsByClient[$clientKey]['posts'][] = $post;
                $postsByClient[$clientKey]['total_posts']++;
            }

            $this->json([
                'success' => true,
                'debug_data' => [
                    'raw_posts' => $posts,
                    'debug_info' => $debugInfo,
                    'posts_by_client_keys' => array_keys($postsByClient),
                    'posts_by_client' => array_values($postsByClient),
                    'summary' => [
                        'total_raw_posts' => count($posts),
                        'total_clients' => count($postsByClient),
                        'processed_posts' => count($processedPosts)
                    ]
                ],
                'timestamp' => date('Y-m-d H:i:s')
            ]);

        } catch (Exception $e) {
            $this->json([
                'success' => false,
                'error' => $e->getMessage(),
                'timestamp' => date('Y-m-d H:i:s')
            ], 500);
        }
    }

    /**
     * Versão simplificada para testar posts pendentes
     * GET /api/v1/client-posts/simple-pending
     */
    public function simplePending() {
        try {
            // Query simplificada
            $posts = $this->db->fetchAll("
                SELECT
                    cp.id,
                    cp.site_id,
                    cp.user_id,
                    cp.post_type,
                    cp.title,
                    cp.status,
                    cp.created_at,
                    COALESCE(cs.site_name, 'Unknown Site') as site_name,
                    COALESCE(u.name, 'Unknown User') as user_name,
                    COALESCE(u.email, 'unknown') as user_email
                FROM client_posts cp
                LEFT JOIN client_sites cs ON cp.site_id = cs.id
                LEFT JOIN users u ON cp.user_id = u.id
                WHERE u.status = 'active' AND cp.status = 'pending'
                GROUP BY cp.id
                ORDER BY cp.created_at DESC
            ");

            // Organizar por cliente
            $postsByClient = [];
            foreach ($posts as $post) {
                $clientKey = $post['user_id'] . '_' . $post['user_email'];

                if (!isset($postsByClient[$clientKey])) {
                    $postsByClient[$clientKey] = [
                        'client_info' => [
                            'user_id' => (int)$post['user_id'],
                            'user_name' => $post['user_name'],
                            'user_email' => $post['user_email']
                        ],
                        'posts' => [],
                        'total_posts' => 0
                    ];
                }
                $postsByClient[$clientKey]['posts'][] = $post;
                $postsByClient[$clientKey]['total_posts']++;
            }

            $this->json([
                'success' => true,
                'data' => [
                    'posts' => $posts,
                    'posts_by_client' => array_values($postsByClient),
                    'stats' => [
                        'total_posts' => count($posts),
                        'total_clients' => count($postsByClient)
                    ]
                ],
                'timestamp' => date('Y-m-d H:i:s')
            ]);

        } catch (Exception $e) {
            $this->json([
                'success' => false,
                'error' => $e->getMessage(),
                'timestamp' => date('Y-m-d H:i:s')
            ], 500);
        }
    }

    /**
     * Debug da query principal para identificar problema
     * GET /api/v1/client-posts/debug-query
     */
    public function debugQuery() {
        try {
            $status = 'pending';
            $conditions = ['u.status = \'active\''];
            $params = [];

            if ($status) {
                $conditions[] = "cp.status = :status";
                $params['status'] = $status;
            }

            $whereClause = implode(' AND ', $conditions);

            // 1. Query sem JOINs
            $postsNoJoin = $this->db->fetchAll("
                SELECT
                    cp.id,
                    cp.user_id,
                    cp.site_id,
                    cp.title,
                    cp.status
                FROM client_posts cp
                WHERE cp.status = 'pending'
                ORDER BY cp.created_at DESC
            ");

            // 2. Query com JOIN users apenas
            $postsWithUsers = $this->db->fetchAll("
                SELECT
                    cp.id,
                    cp.user_id,
                    cp.site_id,
                    cp.title,
                    cp.status,
                    u.name as user_name,
                    u.email as user_email,
                    u.status as user_status
                FROM client_posts cp
                INNER JOIN users u ON cp.user_id = u.id
                WHERE cp.status = 'pending' AND u.status = 'active'
                ORDER BY cp.created_at DESC
            ");

            // 3. Query com LEFT JOIN client_sites
            $postsWithSites = $this->db->fetchAll("
                SELECT
                    cp.id,
                    cp.user_id,
                    cp.site_id,
                    cp.title,
                    cp.status,
                    u.name as user_name,
                    u.email as user_email,
                    u.status as user_status,
                    cs.site_name,
                    cs.site_url
                FROM client_posts cp
                INNER JOIN users u ON cp.user_id = u.id
                LEFT JOIN client_sites cs ON cp.site_id = cs.id
                WHERE cp.status = 'pending' AND u.status = 'active'
                ORDER BY cp.created_at DESC
            ");

            // 4. Query atual da API
            $currentQuery = $this->db->fetchAll("
                SELECT
                    cp.id,
                    cp.user_id,
                    cp.site_id,
                    cp.title,
                    cp.status,
                    u.name as user_name,
                    u.email as user_email
                FROM client_posts cp
                LEFT JOIN client_sites cs ON cp.site_id = cs.id
                LEFT JOIN users u ON cp.user_id = u.id
                WHERE {$whereClause}
                GROUP BY cp.id
                ORDER BY cp.created_at DESC
            ", $params);

            // 5. Verificar sites dos usuários
            $userSites = $this->db->fetchAll("
                SELECT
                    u.id as user_id,
                    u.name as user_name,
                    u.email as user_email,
                    cs.id as site_id,
                    cs.site_name,
                    cs.status as site_status
                FROM users u
                LEFT JOIN client_sites cs ON u.id = cs.user_id
                WHERE u.role = 'client' AND u.status = 'active'
                ORDER BY u.id
            ");

            $this->json([
                'success' => true,
                'debug_data' => [
                    'posts_no_join' => $postsNoJoin,
                    'posts_with_users' => $postsWithUsers,
                    'posts_with_sites' => $postsWithSites,
                    'current_query' => $currentQuery,
                    'user_sites' => $userSites,
                    'summary' => [
                        'no_join_count' => count($postsNoJoin),
                        'with_users_count' => count($postsWithUsers),
                        'with_sites_count' => count($postsWithSites),
                        'current_query_count' => count($currentQuery)
                    ]
                ],
                'timestamp' => date('Y-m-d H:i:s')
            ]);

        } catch (Exception $e) {
            $this->json([
                'success' => false,
                'error' => $e->getMessage(),
                'timestamp' => date('Y-m-d H:i:s')
            ], 500);
        }
    }
}
