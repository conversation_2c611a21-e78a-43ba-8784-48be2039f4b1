<?php
$title = 'Configurações - E1Copy AI';
$pageTitle = 'Configurações do Sistema';
ob_start();
?>

<form method="POST" action="<?= url('/admin/settings') ?>">
    <input type="hidden" name="_token" value="<?= $csrf_token ?>">
    
    <!-- Configurações da API -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-cog me-2"></i>
                Configurações da API
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="groq_api_key" class="form-label">
                        <i class="fas fa-robot me-1"></i>
                        Chave da API Groq
                    </label>
                    <input type="password" class="form-control" id="groq_api_key" 
                           name="settings[groq_api_key]" 
                           value="<?= e($settingsGrouped['groq'][0]['value'] ?? '') ?>"
                           placeholder="gsk_...">
                    <div class="form-text">
                        Chave para geração de conteúdo com IA. 
                        <a href="https://console.groq.com/" target="_blank">Obter chave</a>
                    </div>
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="apiflash_key" class="form-label">
                        <i class="fas fa-camera me-1"></i>
                        Chave da API ApiFlash
                    </label>
                    <input type="password" class="form-control" id="apiflash_key" 
                           name="settings[apiflash_key]" 
                           value="<?= e($settingsGrouped['apiflash'][0]['value'] ?? '') ?>"
                           placeholder="access_key...">
                    <div class="form-text">
                        Chave para captura de screenshots. 
                        <a href="https://apiflash.com/" target="_blank">Obter chave</a>
                    </div>
                </div>
            </div>
            
            <div class="mb-3">
                <label for="default_prompt_instruction" class="form-label">
                    <i class="fas fa-comment-dots me-1"></i>
                    Instrução Padrão para Prompts
                </label>
                <textarea class="form-control" id="default_prompt_instruction" 
                          name="settings[default_prompt_instruction]" rows="3"
                          placeholder="escreva um artigo de três mil palavras otimizado para SEO"><?= e($settingsGrouped['default'][0]['value'] ?? '') ?></textarea>
                <div class="form-text">
                    Esta instrução será adicionada automaticamente a todos os prompts enviados para a IA.
                </div>
            </div>

            <div class="mb-3">
                <button type="button" class="btn btn-outline-info" data-bs-toggle="modal" data-bs-target="#apiDocsModal">
                    <i class="fas fa-book me-1"></i>
                    Documentação da API
                </button>
            </div>
        </div>
    </div>
    
    <!-- Configurações do Sistema -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-server me-2"></i>
                Configurações do Sistema
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="app_name" class="form-label">
                        <i class="fas fa-tag me-1"></i>
                        Nome da Aplicação
                    </label>
                    <input type="text" class="form-control" id="app_name" 
                           name="settings[app_name]" 
                           value="<?= e($settingsGrouped['app'][0]['value'] ?? 'E1Copy AI Dashboard') ?>">
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="max_api_requests_per_hour" class="form-label">
                        <i class="fas fa-tachometer-alt me-1"></i>
                        Limite de Requisições por Hora
                    </label>
                    <input type="number" class="form-control" id="max_api_requests_per_hour" 
                           name="settings[max_api_requests_per_hour]" 
                           value="<?= e($settingsGrouped['max'][0]['value'] ?? '1000') ?>"
                           min="1" max="10000">
                    <div class="form-text">
                        Limite máximo de requisições por hora por chave de API.
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6 mb-3">
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="enable_registration" 
                               name="settings[enable_registration]" value="true"
                               <?= ($settingsGrouped['enable'][0]['value'] ?? 'false') === 'true' ? 'checked' : '' ?>>
                        <label class="form-check-label" for="enable_registration">
                            <i class="fas fa-user-plus me-1"></i>
                            Permitir Registro de Novos Usuários
                        </label>
                    </div>
                    <div class="form-text">
                        Se habilitado, novos usuários poderão se registrar no sistema.
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Configurações de Email -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-envelope me-2"></i>
                Configurações de Email
            </h5>
        </div>
        <div class="card-body">
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                <strong>Nota:</strong> As configurações de email são definidas no arquivo .env por questões de segurança.
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <strong>Configurações Atuais:</strong>
                    <ul class="list-unstyled mt-2">
                        <li><i class="fas fa-server me-1"></i> Host: <?= $_ENV['MAIL_HOST'] ?? 'Não configurado' ?></li>
                        <li><i class="fas fa-plug me-1"></i> Porta: <?= $_ENV['MAIL_PORT'] ?? 'Não configurado' ?></li>
                        <li><i class="fas fa-envelope me-1"></i> De: <?= $_ENV['MAIL_FROM_ADDRESS'] ?? 'Não configurado' ?></li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <button type="button" class="btn btn-outline-primary" onclick="testEmail()">
                        <i class="fas fa-paper-plane me-1"></i>
                        Testar Envio de Email
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Informações do Sistema -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-info-circle me-2"></i>
                Informações do Sistema
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6>Servidor</h6>
                    <ul class="list-unstyled">
                        <li><strong>PHP:</strong> <?= PHP_VERSION ?></li>
                        <li><strong>Servidor:</strong> <?= $_SERVER['SERVER_SOFTWARE'] ?? 'N/A' ?></li>
                        <li><strong>Sistema:</strong> <?= PHP_OS ?></li>
                        <li><strong>Memória:</strong> <?= ini_get('memory_limit') ?></li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>Aplicação</h6>
                    <ul class="list-unstyled">
                        <li><strong>Versão:</strong> 1.0.0</li>
                        <li><strong>Ambiente:</strong> <?= config('app.debug') ? 'Desenvolvimento' : 'Produção' ?></li>
                        <li><strong>URL:</strong> <?= config('app.url') ?></li>
                        <li><strong>Timezone:</strong> <?= date_default_timezone_get() ?></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Botões de Ação -->
    <div class="card">
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <small class="text-muted">
                        <i class="fas fa-save me-1"></i>
                        As configurações serão salvas no banco de dados.
                    </small>
                </div>
                <div>
                    <button type="button" class="btn btn-outline-secondary me-2" onclick="resetForm()">
                        <i class="fas fa-undo me-1"></i>
                        Resetar
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i>
                        Salvar Configurações
                    </button>
                </div>
            </div>
        </div>
    </div>
</form>

<!-- Modal de Documentação da API -->
<div class="modal fade" id="apiDocsModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-book me-2"></i>
                    Documentação da API E1Copy AI
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="nav flex-column nav-pills" id="v-pills-tab" role="tablist">
                            <button class="nav-link active" id="v-pills-auth-tab" data-bs-toggle="pill"
                                    data-bs-target="#v-pills-auth" type="button" role="tab">
                                <i class="fas fa-key me-1"></i> Autenticação
                            </button>
                            <button class="nav-link" id="v-pills-validate-tab" data-bs-toggle="pill"
                                    data-bs-target="#v-pills-validate" type="button" role="tab">
                                <i class="fas fa-check-circle me-1"></i> Validar Chave
                            </button>
                            <button class="nav-link" id="v-pills-status-tab" data-bs-toggle="pill"
                                    data-bs-target="#v-pills-status" type="button" role="tab">
                                <i class="fas fa-info-circle me-1"></i> Status
                            </button>
                            <button class="nav-link" id="v-pills-content-tab" data-bs-toggle="pill"
                                    data-bs-target="#v-pills-content" type="button" role="tab">
                                <i class="fas fa-robot me-1"></i> Gerar Conteúdo
                            </button>
                            <button class="nav-link" id="v-pills-screenshot-tab" data-bs-toggle="pill"
                                    data-bs-target="#v-pills-screenshot" type="button" role="tab">
                                <i class="fas fa-camera me-1"></i> Screenshot
                            </button>
                        </div>
                    </div>
                    <div class="col-md-9">
                        <div class="tab-content" id="v-pills-tabContent">
                            <!-- Autenticação -->
                            <div class="tab-pane fade show active" id="v-pills-auth" role="tabpanel">
                                <h6>Autenticação</h6>
                                <p>Todas as requisições devem incluir uma chave de API válida.</p>

                                <h6>Base URL</h6>
                                <div class="bg-light p-2 rounded mb-3">
                                    <code><?= config('app.url') ?>/api/v1</code>
                                </div>

                                <h6>Headers Obrigatórios</h6>
                                <pre class="bg-dark text-light p-3 rounded"><code>Content-Type: application/json
Authorization: Bearer {api_key}
# ou
X-API-Key: {api_key}</code></pre>

                                <h6>Formato de Resposta</h6>
                                <pre class="bg-dark text-light p-3 rounded"><code>{
  "success": true,
  "message": "Sucesso",
  "data": {...},
  "timestamp": "2024-01-01T12:00:00Z"
}</code></pre>
                            </div>

                            <!-- Validar Chave -->
                            <div class="tab-pane fade" id="v-pills-validate" role="tabpanel">
                                <h6>POST /validate</h6>
                                <p>Valida uma chave de API e retorna informações de uso.</p>

                                <h6>Parâmetros</h6>
                                <pre class="bg-dark text-light p-3 rounded"><code>{
  "api_key": "e1copy_..."
}</code></pre>

                                <h6>Resposta de Sucesso</h6>
                                <pre class="bg-dark text-light p-3 rounded"><code>{
  "success": true,
  "data": {
    "valid": true,
    "status": "active",
    "user_id": 123,
    "monthly_usage": 45,
    "monthly_limit": 500,
    "remaining": 455
  }
}</code></pre>

                                <h6>Exemplo cURL</h6>
                                <pre class="bg-dark text-light p-3 rounded"><code>curl -X POST <?= config('app.url') ?>/api/v1/validate \
  -H "Content-Type: application/json" \
  -d '{"api_key": "e1copy_..."}'</code></pre>
                            </div>

                            <!-- Status -->
                            <div class="tab-pane fade" id="v-pills-status" role="tabpanel">
                                <h6>POST /status</h6>
                                <p>Retorna informações detalhadas sobre o status da chave.</p>

                                <h6>Parâmetros</h6>
                                <pre class="bg-dark text-light p-3 rounded"><code>{
  "api_key": "e1copy_..."
}</code></pre>

                                <h6>Resposta de Sucesso</h6>
                                <pre class="bg-dark text-light p-3 rounded"><code>{
  "success": true,
  "data": {
    "key_status": "active",
    "user_status": "active",
    "subscription_status": "active",
    "plan_name": "Profissional",
    "expires_at": "2024-12-31T23:59:59Z",
    "monthly_usage": 45,
    "monthly_limit": 500,
    "last_used_at": "2024-01-01T12:00:00Z"
  }
}</code></pre>
                            </div>

                            <!-- Gerar Conteúdo -->
                            <div class="tab-pane fade" id="v-pills-content" role="tabpanel">
                                <h6>POST /generate-content</h6>
                                <p>Gera conteúdo usando IA baseado no prompt fornecido.</p>

                                <h6>Parâmetros</h6>
                                <pre class="bg-dark text-light p-3 rounded"><code>{
  "api_key": "e1copy_...",
  "prompt": "Escreva sobre inteligência artificial"
}</code></pre>

                                <h6>Resposta de Sucesso</h6>
                                <pre class="bg-dark text-light p-3 rounded"><code>{
  "success": true,
  "data": {
    "content": "Conteúdo gerado pela IA...",
    "usage": {
      "monthly_usage": 46,
      "monthly_limit": 500
    }
  }
}</code></pre>

                                <h6>Exemplo cURL</h6>
                                <pre class="bg-dark text-light p-3 rounded"><code>curl -X POST <?= config('app.url') ?>/api/v1/generate-content \
  -H "Content-Type: application/json" \
  -d '{
    "api_key": "e1copy_...",
    "prompt": "Escreva sobre inteligência artificial"
  }'</code></pre>
                            </div>

                            <!-- Screenshot -->
                            <div class="tab-pane fade" id="v-pills-screenshot" role="tabpanel">
                                <h6>POST /screenshot</h6>
                                <p>Captura screenshot de uma URL.</p>

                                <h6>Parâmetros</h6>
                                <pre class="bg-dark text-light p-3 rounded"><code>{
  "api_key": "e1copy_...",
  "url": "https://exemplo.com"
}</code></pre>

                                <h6>Resposta de Sucesso</h6>
                                <pre class="bg-dark text-light p-3 rounded"><code>{
  "success": true,
  "data": {
    "screenshot_url": "https://api.apiflash.com/...",
    "usage": {
      "monthly_usage": 47,
      "monthly_limit": 500
    }
  }
}</code></pre>

                                <h6>Exemplo cURL</h6>
                                <pre class="bg-dark text-light p-3 rounded"><code>curl -X POST <?= config('app.url') ?>/api/v1/screenshot \
  -H "Content-Type: application/json" \
  -d '{
    "api_key": "e1copy_...",
    "url": "https://exemplo.com"
  }'</code></pre>
                            </div>
                        </div>
                    </div>
                </div>

                <hr>

                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle me-2"></i>Códigos de Erro</h6>
                    <ul class="mb-0">
                        <li><strong>400:</strong> Parâmetros inválidos ou ausentes</li>
                        <li><strong>401:</strong> Chave de API inválida ou expirada</li>
                        <li><strong>403:</strong> Acesso negado</li>
                        <li><strong>429:</strong> Limite de requisições excedido</li>
                        <li><strong>500:</strong> Erro interno do servidor</li>
                    </ul>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
                <button type="button" class="btn btn-primary" onclick="copyApiDocs()">
                    <i class="fas fa-copy me-1"></i>
                    Copiar Exemplos
                </button>
            </div>
        </div>
    </div>
</div>

<?php
$content = ob_get_clean();
include __DIR__ . '/../layouts/app.php';
?>

<script>
function testEmail() {
    alert('Funcionalidade de teste de email será implementada em breve.');
}

function resetForm() {
    if (confirm('Tem certeza que deseja resetar todas as alterações?')) {
        location.reload();
    }
}

// Mostrar/ocultar senhas
document.querySelectorAll('input[type="password"]').forEach(input => {
    const container = input.parentElement;
    const toggleBtn = document.createElement('button');
    toggleBtn.type = 'button';
    toggleBtn.className = 'btn btn-outline-secondary';
    toggleBtn.innerHTML = '<i class="fas fa-eye"></i>';
    toggleBtn.style.position = 'absolute';
    toggleBtn.style.right = '5px';
    toggleBtn.style.top = '50%';
    toggleBtn.style.transform = 'translateY(-50%)';
    toggleBtn.style.zIndex = '10';
    
    container.style.position = 'relative';
    container.appendChild(toggleBtn);
    
    toggleBtn.addEventListener('click', function() {
        if (input.type === 'password') {
            input.type = 'text';
            toggleBtn.innerHTML = '<i class="fas fa-eye-slash"></i>';
        } else {
            input.type = 'password';
            toggleBtn.innerHTML = '<i class="fas fa-eye"></i>';
        }
    });
});

function copyApiDocs() {
    const baseUrl = '<?= config('app.url') ?>/api/v1';
    const examples = `
# E1Copy AI - Exemplos de API

## Validar Chave
curl -X POST ${baseUrl}/validate \\
  -H "Content-Type: application/json" \\
  -d '{"api_key": "e1copy_..."}'

## Gerar Conteúdo
curl -X POST ${baseUrl}/generate-content \\
  -H "Content-Type: application/json" \\
  -d '{"api_key": "e1copy_...", "prompt": "Escreva sobre IA"}'

## Screenshot
curl -X POST ${baseUrl}/screenshot \\
  -H "Content-Type: application/json" \\
  -d '{"api_key": "e1copy_...", "url": "https://exemplo.com"}'

## Status da Chave
curl -X POST ${baseUrl}/status \\
  -H "Content-Type: application/json" \\
  -d '{"api_key": "e1copy_..."}'
    `;

    navigator.clipboard.writeText(examples.trim()).then(() => {
        alert('Exemplos copiados para a área de transferência!');
    }).catch(() => {
        alert('Erro ao copiar. Tente selecionar e copiar manualmente.');
    });
}
</script>
