<?php
/**
 * Script de Console para Testar Sincronização
 * Dashboard E1Copy AI
 */

require_once __DIR__ . '/bootstrap.php';

echo "=== TESTE DE SINCRONIZAÇÃO E1COPY AI ===\n\n";

// Verificar argumentos
if ($argc < 2) {
    echo "Uso: php test-sync-console.php [site_id|site_url]\n";
    echo "Exemplos:\n";
    echo "  php test-sync-console.php 1\n";
    echo "  php test-sync-console.php https://melhorcupom.shop\n\n";
    exit(1);
}

$input = $argv[1];
$db = Database::getInstance();

// Determinar se é ID ou URL
if (is_numeric($input)) {
    $siteId = $input;
    $site = $db->fetch("
        SELECT cs.*, u.name as user_name, u.email, ak.api_key, ak.status as key_status
        FROM client_sites cs
        LEFT JOIN users u ON cs.user_id = u.id
        LEFT JOIN api_keys ak ON cs.api_key_id = ak.id
        WHERE cs.id = ?
    ", [$siteId]);
} else {
    $siteUrl = rtrim($input, '/');
    $site = $db->fetch("
        SELECT cs.*, u.name as user_name, u.email, ak.api_key, ak.status as key_status
        FROM client_sites cs
        LEFT JOIN users u ON cs.user_id = u.id
        LEFT JOIN api_keys ak ON cs.api_key_id = ak.id
        WHERE cs.site_url = ?
    ", [$siteUrl]);
    $siteId = $site['id'] ?? null;
}

if (!$site) {
    echo "❌ Site não encontrado!\n";
    exit(1);
}

echo "📍 INFORMAÇÕES DO SITE:\n";
echo "ID: {$site['id']}\n";
echo "URL: {$site['site_url']}\n";
echo "Usuário: {$site['user_name']} ({$site['email']})\n";
echo "Chave API: " . substr($site['api_key'], 0, 8) . "...\n";
echo "Status Chave: {$site['key_status']}\n\n";

$siteUrl = rtrim($site['site_url'], '/');
$apiKey = $site['api_key'];

// Função para testar endpoint
function testEndpoint($url, $headers = []) {
    echo "🔍 Testando: $url\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 15);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'E1Copy-Dashboard-Console/1.0');
    
    if (!empty($headers)) {
        curl_setopt($ch, CURLOPT_HTTPHEADER, array_merge([
            'Accept: application/json',
            'Content-Type: application/json'
        ], $headers));
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    echo "Status: $httpCode\n";
    if ($error) {
        echo "Erro cURL: $error\n";
    }
    
    if ($response) {
        $decoded = json_decode($response, true);
        if ($decoded) {
            echo "Resposta: " . json_encode($decoded, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
        } else {
            echo "Resposta (raw): " . substr($response, 0, 200) . "...\n";
        }
    }
    echo "\n";
    
    return [
        'url' => $url,
        'http_code' => $httpCode,
        'success' => $httpCode >= 200 && $httpCode < 300,
        'response' => $response ? json_decode($response, true) : null,
        'error' => $error ?: null
    ];
}

echo "🔧 TESTE 1: Plugin Status\n";
$test1 = testEndpoint($siteUrl . '/wp-json/e1copy-ai/v1/test');

echo "🔧 TESTE 2: Autenticação\n";
$test2 = testEndpoint(
    $siteUrl . '/wp-json/e1copy-ai/v1/verify-key',
    ['X-E1Copy-API-Key: ' . $apiKey]
);

echo "🔧 TESTE 3: Posts Pendentes (Plugin)\n";
$test3 = testEndpoint(
    $siteUrl . '/wp-json/e1copy-ai/v1/posts',
    ['X-E1Copy-API-Key: ' . $apiKey]
);

echo "🔧 TESTE 4: Posts WordPress (API Nativa)\n";
$test4 = testEndpoint(
    $siteUrl . '/wp-json/wp/v2/posts?per_page=5',
    ['X-E1Copy-API-Key: ' . $apiKey]
);

echo "🔧 TESTE 5: Fila Antes da Sincronização\n";
$queueBefore = $db->fetchAll("
    SELECT * FROM content_queue 
    WHERE site_id = ? 
    ORDER BY created_at DESC 
    LIMIT 5
", [$siteId]);
echo "Posts na fila: " . count($queueBefore) . "\n";
if ($queueBefore) {
    foreach ($queueBefore as $item) {
        echo "- ID: {$item['id']}, Post: {$item['post_id']}, Status: {$item['status']}\n";
    }
}
echo "\n";

echo "🔧 TESTE 6: Executando Sincronização\n";
try {
    $syncService = new SiteSyncService();
    $syncResult = $syncService->syncSiteById($siteId);
    echo "Resultado da sincronização:\n";
    echo json_encode($syncResult, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";
} catch (Exception $e) {
    echo "❌ Erro na sincronização: " . $e->getMessage() . "\n\n";
}

echo "🔧 TESTE 7: Fila Após Sincronização\n";
$queueAfter = $db->fetchAll("
    SELECT * FROM content_queue 
    WHERE site_id = ? 
    ORDER BY created_at DESC 
    LIMIT 10
", [$siteId]);
echo "Posts na fila: " . count($queueAfter) . "\n";
if ($queueAfter) {
    foreach ($queueAfter as $item) {
        echo "- ID: {$item['id']}, Post: {$item['post_id']}, Status: {$item['status']}, Criado: {$item['created_at']}\n";
    }
}
echo "\n";

echo "🔧 TESTE 8: Logs de Sincronização\n";
$logs = $db->fetchAll("
    SELECT * FROM ai_execution_logs 
    WHERE site_id = ? 
    ORDER BY created_at DESC 
    LIMIT 5
", [$siteId]);
echo "Logs encontrados: " . count($logs) . "\n";
if ($logs) {
    foreach ($logs as $log) {
        echo "- {$log['created_at']}: {$log['action']} - {$log['status']}\n";
        if ($log['error_message']) {
            echo "  Erro: {$log['error_message']}\n";
        }
    }
}
echo "\n";

echo "🔧 TESTE 9: Verificar Estrutura da Tabela content_queue\n";
try {
    $structure = $db->fetchAll("DESCRIBE content_queue");
    echo "Colunas da tabela content_queue:\n";
    foreach ($structure as $column) {
        echo "- {$column['Field']}: {$column['Type']}\n";
    }
} catch (Exception $e) {
    echo "❌ Erro ao verificar estrutura: " . $e->getMessage() . "\n";
}
echo "\n";

echo "🔧 TESTE 10: Verificar Posts com Meta 'processed'\n";
$testMeta = testEndpoint(
    $siteUrl . '/wp-json/wp/v2/posts?meta_key=processed&meta_value=0&per_page=5',
    ['X-E1Copy-API-Key: ' . $apiKey]
);

echo "=== RESUMO DOS TESTES ===\n";
echo "Plugin Status: " . ($test1['success'] ? '✅' : '❌') . "\n";
echo "Autenticação: " . ($test2['success'] ? '✅' : '❌') . "\n";
echo "Posts Plugin: " . ($test3['success'] ? '✅' : '❌') . "\n";
echo "Posts WordPress: " . ($test4['success'] ? '✅' : '❌') . "\n";
echo "Posts na fila antes: " . count($queueBefore) . "\n";
echo "Posts na fila depois: " . count($queueAfter) . "\n";
echo "Logs de sincronização: " . count($logs) . "\n";

if (count($queueAfter) > count($queueBefore)) {
    echo "\n✅ SUCESSO: Novos posts foram adicionados à fila!\n";
} else {
    echo "\n⚠️  ATENÇÃO: Nenhum post novo foi adicionado à fila.\n";
    echo "Possíveis causas:\n";
    echo "- Não há posts pendentes no WordPress\n";
    echo "- Plugin não está configurado corretamente\n";
    echo "- Chave de API inválida\n";
    echo "- Erro na sincronização\n";
}

echo "\n=== FIM DOS TESTES ===\n";
?>
