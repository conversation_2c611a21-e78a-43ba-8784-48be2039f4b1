<?php
/**
 * Rotas da API para Dashboard
 * Sistema de Dashboard E1Copy AI
 * 
 * APENAS APIs de validação de chave para o dashboard
 */

// =====================================================
// APIS PARA DASHBOARD - APENAS VALIDAÇÃO
// =====================================================

// Endpoint de validação de chave para o dashboard
$router->post('/api/v1/validate', 'Api@validateKey');
$router->get('/api/v1/verify-key', 'Api@verifyKey');
$router->post('/api/v1/verify-key', 'Api@verifyKey');

// Endpoint de status da chave (informações detalhadas para admin)
$router->post('/api/v1/status', 'Api@keyStatus');
$router->get('/api/v1/client-status/{key}', 'Api@clientStatus');

// Endpoints administrativos (apenas para admin)
$router->post('/api/v1/suspend-key', 'Api@suspendKey');
$router->post('/api/v1/activate-key', 'Api@activateKey');
$router->get('/api/v1/usage-stats/{key}', 'Api@usageStats');

// Health check
$router->get('/api/v1/health', 'Api@healthCheck');
