<?php
/**
 * Script para Testar API do Plugin WordPress
 * Dashboard E1Copy AI
 */

require_once __DIR__ . '/bootstrap.php';

echo "=== TESTE DA API DO PLUGIN E1COPY AI ===\n\n";

// Verificar argumentos
if ($argc < 3) {
    echo "Uso: php test-plugin-api.php [site_url] [api_key]\n";
    echo "Exemplo: php test-plugin-api.php https://melhorcupom.shop e1copy_abc123...\n\n";
    exit(1);
}

$siteUrl = rtrim($argv[1], '/');
$apiKey = $argv[2];

echo "🌐 Site: $siteUrl\n";
echo "🔑 Chave: " . substr($apiKey, 0, 8) . "...\n\n";

// Função para testar endpoint
function testApiEndpoint($url, $headers = [], $method = 'GET', $data = null) {
    echo "🔍 $method $url\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 15);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'E1Copy-Dashboard-Test/1.0');
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
    
    if ($data && $method !== 'GET') {
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    }
    
    if (!empty($headers)) {
        curl_setopt($ch, CURLOPT_HTTPHEADER, array_merge([
            'Accept: application/json',
            'Content-Type: application/json'
        ], $headers));
    }
    
    $start = microtime(true);
    $response = curl_exec($ch);
    $end = microtime(true);
    $time = round(($end - $start) * 1000, 2);
    
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    echo "⏱️  Tempo: {$time}ms\n";
    echo "📊 Status: $httpCode\n";
    
    if ($error) {
        echo "❌ Erro cURL: $error\n";
        return false;
    }
    
    if ($httpCode >= 200 && $httpCode < 300) {
        echo "✅ Sucesso!\n";
    } else {
        echo "❌ Falha!\n";
    }
    
    if ($response) {
        $decoded = json_decode($response, true);
        if ($decoded) {
            echo "📄 Resposta:\n";
            echo json_encode($decoded, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
        } else {
            echo "📄 Resposta (raw): " . substr($response, 0, 500) . "\n";
        }
    } else {
        echo "📄 Nenhuma resposta\n";
    }
    
    echo str_repeat("-", 60) . "\n\n";
    
    return $httpCode >= 200 && $httpCode < 300;
}

// Testes sequenciais
$tests = [];

echo "🧪 TESTE 1: WordPress REST API Base\n";
$tests['wp_api'] = testApiEndpoint($siteUrl . '/wp-json/');

echo "🧪 TESTE 2: Plugin Status (sem autenticação)\n";
$tests['plugin_status'] = testApiEndpoint($siteUrl . '/wp-json/e1copy-ai/v1/test');

echo "🧪 TESTE 3: Verificar Chave de API\n";
$tests['verify_key'] = testApiEndpoint(
    $siteUrl . '/wp-json/e1copy-ai/v1/verify-key',
    ['X-E1Copy-API-Key: ' . $apiKey]
);

echo "🧪 TESTE 4: Verificar Chave via URL\n";
$tests['verify_key_url'] = testApiEndpoint(
    $siteUrl . '/wp-json/e1copy-ai/v1/verify-key?api_key=' . urlencode($apiKey)
);

echo "🧪 TESTE 5: Listar Posts do Plugin\n";
$tests['plugin_posts'] = testApiEndpoint(
    $siteUrl . '/wp-json/e1copy-ai/v1/posts',
    ['X-E1Copy-API-Key: ' . $apiKey]
);

echo "🧪 TESTE 6: Posts WordPress (API Nativa)\n";
$tests['wp_posts'] = testApiEndpoint(
    $siteUrl . '/wp-json/wp/v2/posts?per_page=5',
    ['X-E1Copy-API-Key: ' . $apiKey]
);

echo "🧪 TESTE 7: Posts com Meta 'processed=0'\n";
$tests['wp_posts_unprocessed'] = testApiEndpoint(
    $siteUrl . '/wp-json/wp/v2/posts?meta_key=processed&meta_value=0&per_page=10',
    ['X-E1Copy-API-Key: ' . $apiKey]
);

echo "🧪 TESTE 8: Custom Post Types\n";
$tests['post_types'] = testApiEndpoint(
    $siteUrl . '/wp-json/wp/v2/types',
    ['X-E1Copy-API-Key: ' . $apiKey]
);

echo "🧪 TESTE 9: Plugin Info\n";
$tests['plugin_info'] = testApiEndpoint($siteUrl . '/wp-json/e1copy/v1/status');

echo "🧪 TESTE 10: WordPress Health\n";
$tests['wp_health'] = testApiEndpoint($siteUrl . '/wp-json/wp-site-health/v1/tests/background-updates');

// Resumo
echo "📋 RESUMO DOS TESTES:\n";
echo str_repeat("=", 60) . "\n";

$passed = 0;
$total = count($tests);

foreach ($tests as $name => $result) {
    $status = $result ? '✅ PASSOU' : '❌ FALHOU';
    echo sprintf("%-25s %s\n", ucfirst(str_replace('_', ' ', $name)), $status);
    if ($result) $passed++;
}

echo str_repeat("=", 60) . "\n";
echo "📊 RESULTADO FINAL: $passed/$total testes passaram\n";

if ($passed === $total) {
    echo "🎉 TODOS OS TESTES PASSARAM!\n";
} elseif ($passed >= $total * 0.7) {
    echo "⚠️  MAIORIA DOS TESTES PASSOU - Verificar falhas\n";
} else {
    echo "❌ MUITOS TESTES FALHARAM - Verificar configuração\n";
}

echo "\n💡 DICAS PARA DEBUG:\n";
echo "- Se 'Plugin Status' falhou: Plugin não está ativo\n";
echo "- Se 'Verify Key' falhou: Chave de API incorreta\n";
echo "- Se 'Plugin Posts' falhou: Problema na autenticação\n";
echo "- Se 'WP Posts' falhou: Problema no WordPress\n";
echo "- Se nenhum post foi encontrado: Não há posts pendentes\n";

echo "\n=== FIM DOS TESTES ===\n";
?>
