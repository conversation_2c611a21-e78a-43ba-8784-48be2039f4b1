<?php
/**
 * Script para Verificar Posts Pendentes
 * Dashboard E1Copy AI
 */

require_once __DIR__ . '/bootstrap.php';

echo "=== VERIFICAÇÃO DE POSTS PENDENTES ===\n\n";

$db = Database::getInstance();

// Listar todos os sites
$sites = $db->fetchAll("
    SELECT cs.*, u.name as user_name, ak.api_key
    FROM client_sites cs
    LEFT JOIN users u ON cs.user_id = u.id
    LEFT JOIN api_keys ak ON cs.api_key_id = ak.id
    WHERE ak.status = 'active'
    ORDER BY cs.id
");

if (empty($sites)) {
    echo "❌ Nenhum site ativo encontrado!\n";
    exit(1);
}

echo "📍 SITES ENCONTRADOS: " . count($sites) . "\n\n";

foreach ($sites as $site) {
    echo "🌐 SITE: {$site['site_url']} (ID: {$site['id']})\n";
    echo "👤 Usuário: {$site['user_name']}\n";
    echo "🔑 Chave: " . substr($site['api_key'], 0, 8) . "...\n";
    
    $siteUrl = rtrim($site['site_url'], '/');
    $apiKey = $site['api_key'];
    
    // Testar plugin
    echo "🔍 Testando plugin...\n";
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $siteUrl . '/wp-json/e1copy-ai/v1/test');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode !== 200) {
        echo "❌ Plugin não está ativo (HTTP $httpCode)\n\n";
        continue;
    }
    
    echo "✅ Plugin ativo\n";
    
    // Testar autenticação
    echo "🔍 Testando autenticação...\n";
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $siteUrl . '/wp-json/e1copy-ai/v1/verify-key');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'X-E1Copy-API-Key: ' . $apiKey,
        'Accept: application/json'
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode !== 200) {
        echo "❌ Autenticação falhou (HTTP $httpCode)\n";
        if ($response) {
            $data = json_decode($response, true);
            if ($data && isset($data['message'])) {
                echo "Erro: {$data['message']}\n";
            }
        }
        echo "\n";
        continue;
    }
    
    echo "✅ Autenticação OK\n";
    
    // Buscar posts pendentes
    echo "🔍 Buscando posts pendentes...\n";
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $siteUrl . '/wp-json/e1copy-ai/v1/posts');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 15);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'X-E1Copy-API-Key: ' . $apiKey,
        'Accept: application/json'
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode !== 200) {
        echo "❌ Erro ao buscar posts (HTTP $httpCode)\n\n";
        continue;
    }
    
    $data = json_decode($response, true);
    if (!$data || !isset($data['posts'])) {
        echo "❌ Resposta inválida da API\n\n";
        continue;
    }
    
    $posts = $data['posts'];
    echo "📄 Posts encontrados: " . count($posts) . "\n";
    
    if (empty($posts)) {
        echo "ℹ️  Nenhum post pendente\n";
    } else {
        foreach ($posts as $post) {
            echo "  - ID: {$post['id']}, Título: " . substr($post['title'], 0, 50) . "...\n";
        }
        
        // Verificar se estão na fila
        $queueCount = $db->fetch("
            SELECT COUNT(*) as count 
            FROM content_queue 
            WHERE site_id = ?
        ", [$site['id']])['count'];
        
        echo "📋 Posts na fila do dashboard: $queueCount\n";
        
        if ($queueCount < count($posts)) {
            echo "⚠️  ATENÇÃO: Há posts pendentes que não estão na fila!\n";
            echo "💡 Execute a sincronização para este site\n";
        }
    }
    
    echo str_repeat("-", 60) . "\n\n";
}

// Resumo geral
echo "📊 RESUMO GERAL:\n";
$totalQueue = $db->fetch("SELECT COUNT(*) as count FROM content_queue")['count'];
echo "Total de posts na fila: $totalQueue\n";

$pendingQueue = $db->fetch("SELECT COUNT(*) as count FROM content_queue WHERE status = 'pending'")['count'];
echo "Posts pendentes na fila: $pendingQueue\n";

$processingQueue = $db->fetch("SELECT COUNT(*) as count FROM content_queue WHERE status = 'processing'")['count'];
echo "Posts em processamento: $processingQueue\n";

$completedQueue = $db->fetch("SELECT COUNT(*) as count FROM content_queue WHERE status = 'completed'")['count'];
echo "Posts concluídos: $completedQueue\n";

echo "\n💡 COMANDOS ÚTEIS:\n";
echo "Testar sincronização: php test-sync-console.php [site_id]\n";
echo "Testar API do plugin: php test-plugin-api.php [site_url] [api_key]\n";
echo "Processar fila: php cron/process-queue.php\n";

echo "\n=== FIM DA VERIFICAÇÃO ===\n";
?>
