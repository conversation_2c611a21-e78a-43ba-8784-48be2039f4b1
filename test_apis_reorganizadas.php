<?php
/**
 * Teste das APIs Reorganizadas
 * Sistema de Dashboard E1Copy AI
 */

echo "=== TESTE DAS APIs REORGANIZADAS ===\n";

// Carregar bootstrap
require_once __DIR__ . '/bootstrap.php';

echo "1. Verificando APIs ativas...\n";

// APIs que devem estar ativas
$activeApis = [
    'POST /api/v1/validate' => 'Validação de chave',
    'GET /api/v1/verify-key' => 'Verificação de chave (GET)',
    'POST /api/v1/verify-key' => 'Verificação de chave (POST)',
    'POST /api/v1/status' => 'Status da chave',
    'GET /api/v1/health' => 'Health check',
    'GET /api/v1/client-status/{key}' => 'Status do cliente (admin)',
    'POST /api/v1/suspend-key' => 'Suspender chave (admin)',
    'POST /api/v1/activate-key' => 'Ativar chave (admin)',
    'GET /api/v1/usage-stats/{key}' => 'Estatísticas de uso (admin)'
];

// APIs que devem ter sido removidas
$removedApis = [
    'POST /api/v1/generate-content' => 'Geração de conteúdo (REMOVIDA)',
    'POST /api/v1/screenshot' => 'Captura de tela (REMOVIDA)',
    'GET /api/v1/pending-posts' => 'Posts pendentes (REMOVIDA)',
    'GET /api/v1/client-posts' => 'Posts de clientes (REMOVIDA)',
    'GET /api/v1/plans' => 'Planos (REMOVIDA)',
    'POST /api/v1/register-site' => 'Registro de site (REMOVIDA)'
];

echo "\n📋 APIs ATIVAS:\n";
foreach ($activeApis as $endpoint => $description) {
    echo "   ✅ $endpoint - $description\n";
}

echo "\n❌ APIs REMOVIDAS:\n";
foreach ($removedApis as $endpoint => $description) {
    echo "   🚫 $endpoint - $description\n";
}

echo "\n2. Testando endpoint de health check...\n";

try {
    // Simular requisição GET para health check
    $_SERVER['REQUEST_METHOD'] = 'GET';
    $_SERVER['REQUEST_URI'] = '/api/v1/health';
    
    $controller = new ApiController();
    
    ob_start();
    $result = $controller->healthCheck();
    $output = ob_get_clean();
    
    if ($result) {
        echo "   ✅ Health check funcionando!\n";
        echo "   Resposta: $result\n";
    } else {
        echo "   ❌ Health check falhou\n";
    }
    
} catch (Exception $e) {
    echo "   ❌ Erro no health check: " . $e->getMessage() . "\n";
}

echo "\n3. Testando validação de chave (sem chave real)...\n";

try {
    // Simular requisição POST para validate
    $_SERVER['REQUEST_METHOD'] = 'POST';
    $_SERVER['REQUEST_URI'] = '/api/v1/validate';
    $_SERVER['CONTENT_TYPE'] = 'application/json';
    
    // Simular input JSON vazio (deve retornar erro)
    $controller = new ApiController();
    
    ob_start();
    $result = $controller->validateKey();
    $output = ob_get_clean();
    
    echo "   Teste sem chave de API:\n";
    echo "   Resposta: $result\n";
    
    if (strpos($result, 'Chave de API não fornecida') !== false) {
        echo "   ✅ Validação de entrada funcionando corretamente!\n";
    } else {
        echo "   ⚠️  Resposta inesperada\n";
    }
    
} catch (Exception $e) {
    echo "   ❌ Erro na validação: " . $e->getMessage() . "\n";
}

echo "\n4. Verificando se métodos removidos não existem mais...\n";

$controller = new ApiController();
$removedMethods = [
    'generateContent' => 'Geração de conteúdo',
    'takeScreenshot' => 'Captura de tela',
    'getPendingPosts' => 'Posts pendentes',
    'getClientPosts' => 'Posts de clientes'
];

foreach ($removedMethods as $method => $description) {
    if (method_exists($controller, $method)) {
        echo "   ⚠️  Método $method ainda existe ($description)\n";
    } else {
        echo "   ✅ Método $method removido corretamente ($description)\n";
    }
}

echo "\n5. Verificando arquivos de rotas...\n";

$routeFiles = [
    'routes/api.php' => 'Rotas principais da API',
    'routes/plugin_api.php' => 'Rotas específicas do plugin',
    'routes/dashboard_api.php' => 'Rotas específicas do dashboard'
];

foreach ($routeFiles as $file => $description) {
    if (file_exists(__DIR__ . '/' . $file)) {
        echo "   ✅ $file existe ($description)\n";
        
        $content = file_get_contents(__DIR__ . '/' . $file);
        $lines = count(explode("\n", $content));
        echo "     Linhas: $lines\n";
    } else {
        echo "   ❌ $file não encontrado\n";
    }
}

echo "\n6. Verificando documentação atualizada...\n";

$docFile = 'views/admin/api-docs.php';
if (file_exists(__DIR__ . '/' . $docFile)) {
    echo "   ✅ Arquivo de documentação existe\n";
    
    $content = file_get_contents(__DIR__ . '/' . $docFile);
    
    // Verificar se contém as seções atualizadas
    $checks = [
        'APIs Ativas - Apenas Validação de Chave' => 'Título atualizado',
        'APIs Removidas' => 'Seção de APIs removidas',
        'POST /api/v1/validate' => 'Endpoint de validação',
        'Endpoints Administrativos' => 'Seção administrativa'
    ];
    
    foreach ($checks as $search => $description) {
        if (strpos($content, $search) !== false) {
            echo "   ✅ $description encontrado\n";
        } else {
            echo "   ❌ $description não encontrado\n";
        }
    }
} else {
    echo "   ❌ Arquivo de documentação não encontrado\n";
}

echo "\n7. Verificando página de configurações...\n";

$settingsFile = 'views/admin/settings.php';
if (file_exists(__DIR__ . '/' . $settingsFile)) {
    echo "   ✅ Arquivo de configurações existe\n";
    
    $content = file_get_contents(__DIR__ . '/' . $settingsFile);
    
    // Verificar se a documentação foi removida
    if (strpos($content, 'apiDocsModal') === false) {
        echo "   ✅ Modal de documentação removido\n";
    } else {
        echo "   ❌ Modal de documentação ainda presente\n";
    }
    
    if (strpos($content, 'Documentação da API') === false) {
        echo "   ✅ Botão de documentação removido\n";
    } else {
        echo "   ❌ Botão de documentação ainda presente\n";
    }
} else {
    echo "   ❌ Arquivo de configurações não encontrado\n";
}

echo "\n8. Testando URLs das APIs...\n";

$apiUrls = [
    '/api/v1/health',
    '/api/v1/validate',
    '/api/v1/verify-key',
    '/api/v1/status'
];

foreach ($apiUrls as $endpoint) {
    $fullUrl = url($endpoint);
    echo "   📡 $endpoint -> $fullUrl\n";
}

echo "\n=== RESUMO DA REORGANIZAÇÃO ===\n";

echo "✅ CONCLUÍDO:\n";
echo "   • Documentação removida da página de configurações\n";
echo "   • Plugin mantém apenas validação de chave\n";
echo "   • Dashboard mantém apenas validação + admin\n";
echo "   • Documentação atualizada em menu específico\n";
echo "   • APIs desnecessárias removidas\n";

echo "\n🎯 APIS ATIVAS:\n";
echo "   • Validação de chave (validate, verify-key, status)\n";
echo "   • Health check (monitoramento)\n";
echo "   • Funções administrativas (suspend, activate, stats)\n";

echo "\n📚 DOCUMENTAÇÃO:\n";
echo "   • Localizada em: " . url('/admin/api-docs') . "\n";
echo "   • Atualizada com apenas APIs ativas\n";
echo "   • Seção de APIs removidas documentada\n";

echo "\n🔧 PRÓXIMOS PASSOS:\n";
echo "1. Teste o acesso ao dashboard: " . url('/') . "\n";
echo "2. Verifique a documentação: " . url('/admin/api-docs') . "\n";
echo "3. Teste as APIs de validação com chaves reais\n";
echo "4. Configure o plugin WordPress para usar apenas validação\n";

echo "\n=== TESTE CONCLUÍDO ===\n";
