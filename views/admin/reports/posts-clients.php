<?php
$title = 'Posts Clients - Relatórios - E1Copy AI';
$pageTitle = 'Posts Clients';
ob_start();

// Verificar se o sistema de IA está instalado
$aiSystemInstalled = isset($aiSystemInstalled) ? $aiSystemInstalled : true;
?>

<style>
.status-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}
.status-success { background-color: #d4edda; color: #155724; }
.status-error { background-color: #f8d7da; color: #721c24; }
.status-warning { background-color: #fff3cd; color: #856404; }
.status-pending { background-color: #cce7ff; color: #004085; }
.status-info { background-color: #e2e3e5; color: #383d41; }

.stats-card {
    border-left: 4px solid #007bff;
    transition: transform 0.2s;
}
.stats-card:hover {
    transform: translateY(-2px);
}
</style>

<?php if (!$aiSystemInstalled): ?>
    <div class="alert alert-warning">
        <h6><i class="fas fa-exclamation-triangle me-2"></i>Sistema de IA Não Instalado</h6>
        <p class="mb-2">
            As funcionalidades de geração de conteúdo com IA não estão disponíveis.
            Para habilitar essas funcionalidades, execute o arquivo <code>install-ai-system.sql</code> no seu banco de dados.
        </p>
        <a href="<?= url('/admin/ai-settings') ?>" class="btn btn-warning btn-sm">
            <i class="fas fa-cog me-1"></i>
            Ver Instruções de Instalação
        </a>
    </div>
<?php endif; ?>

<!-- Estatísticas Gerais -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <h3 class="text-primary"><?= number_format($generalStats['total_clients']) ?></h3>
                <p class="text-muted mb-0">Total de Clientes</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <h3 class="text-info"><?= number_format($generalStats['total_sites']) ?></h3>
                <p class="text-muted mb-0">Sites Cadastrados</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card stats-card">
            <div class="card-body text-center">
                <h3 class="text-success"><?= number_format($generalStats['total_posts']) ?></h3>
                <p class="text-muted mb-0">Posts Criados</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card stats-card">
            <div class="card-body text-center">
                <h3 class="text-warning"><?= number_format($generalStats['total_pending']) ?></h3>
                <p class="text-muted mb-0">Pendentes</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card stats-card">
            <div class="card-body text-center">
                <h3 class="text-primary"><?= number_format($generalStats['total_published']) ?></h3>
                <p class="text-muted mb-0">Publicados</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card stats-card">
            <div class="card-body text-center">
                <h3 class="text-danger"><?= number_format($generalStats['total_failed']) ?></h3>
                <p class="text-muted mb-0">Falharam</p>
            </div>
        </div>
    </div>
</div>

<!-- Filtros e Busca -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label for="search" class="form-label">Buscar</label>
                <input type="text" class="form-control" id="search" name="search" 
                       placeholder="Nome, email, site..." value="<?= e($search) ?>">
            </div>
            <div class="col-md-2">
                <label for="status" class="form-label">Status</label>
                <select class="form-select" id="status" name="status">
                    <option value="">Todos</option>
                    <option value="active" <?= $status === 'active' ? 'selected' : '' ?>>Ativos</option>
                    <option value="inactive" <?= $status === 'inactive' ? 'selected' : '' ?>>Inativos</option>
                    <option value="has_errors" <?= $status === 'has_errors' ? 'selected' : '' ?>>Com Erros</option>
                    <option value="has_pending" <?= $status === 'has_pending' ? 'selected' : '' ?>>Com Pendências</option>
                    <option value="has_posts" <?= $status === 'has_posts' ? 'selected' : '' ?>>Com Posts</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="post_type" class="form-label">Tipo de Post</label>
                <select class="form-select" id="post_type" name="post_type">
                    <option value="">Todos</option>
                    <option value="article" <?= ($postType ?? '') === 'article' ? 'selected' : '' ?>>Artigos</option>
                    <option value="product_review" <?= ($postType ?? '') === 'product_review' ? 'selected' : '' ?>>Reviews de Produtos</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="order_by" class="form-label">Ordenar por</label>
                <select class="form-select" id="order_by" name="order_by">
                    <option value="last_activity" <?= $orderBy === 'last_activity' ? 'selected' : '' ?>>Última Atividade</option>
                    <option value="site_name" <?= $orderBy === 'site_name' ? 'selected' : '' ?>>Nome do Site</option>
                    <option value="user_name" <?= $orderBy === 'user_name' ? 'selected' : '' ?>>Nome do Cliente</option>
                    <option value="total_posts" <?= $orderBy === 'total_posts' ? 'selected' : '' ?>>Total de Posts</option>
                    <option value="pending_posts" <?= $orderBy === 'pending_posts' ? 'selected' : '' ?>>Posts Pendentes</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="order_dir" class="form-label">Direção</label>
                <select class="form-select" id="order_dir" name="order_dir">
                    <option value="desc" <?= $orderDir === 'desc' ? 'selected' : '' ?>>Decrescente</option>
                    <option value="asc" <?= $orderDir === 'asc' ? 'selected' : '' ?>>Crescente</option>
                </select>
            </div>
            <div class="col-12">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search me-1"></i>
                    Filtrar
                </button>
                <a href="<?= url('/admin/reports/posts-clients') ?>" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-1"></i>
                    Limpar
                </a>
                <button type="button" class="btn btn-info ms-2" onclick="location.reload()">
                    <i class="fas fa-sync me-1"></i>
                    Atualizar
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Posts Pendentes em Tempo Real -->
<?php
$totalPendingPosts = $pendingPostsData['summary']['total_posts'] ?? $pendingPostsData['total_posts'] ?? 0;
if (isset($pendingPostsData) && $totalPendingPosts > 0): ?>
<div class="card mb-4">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-file-alt me-2"></i>
            Posts Pendentes do Dashboard (<?= $totalPendingPosts ?>)
        </h5>
        <small class="text-muted">
            Posts criados pelos clientes no dashboard - Última atualização: <?= date('d/m/Y H:i:s', strtotime($pendingPostsData['timestamp'])) ?>
        </small>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-sm">
                <thead>
                    <tr>
                        <th>Cliente/Site</th>
                        <th>Título</th>
                        <th>Tipo</th>
                        <th>Categoria</th>
                        <th>Criado em</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    $displayPosts = $pendingPostsData['all_posts'] ?? [];

                    foreach (array_slice($displayPosts, 0, 15) as $post): ?>
                        <tr>
                            <td>
                                <small>
                                    <strong><?= e($post['site_name'] ?? 'Site desconhecido') ?></strong><br>
                                    <span class="text-muted"><?= e($post['user_name'] ?? 'Usuário desconhecido') ?></span>
                                </small>
                            </td>
                            <td>
                                <small>
                                    <?= e(substr($post['title'], 0, 60)) ?><?= strlen($post['title']) > 60 ? '...' : '' ?>
                                    <?php if (!empty($post['product_name'])): ?>
                                        <br><span class="text-muted">Produto: <?= e(substr($post['product_name'], 0, 40)) ?></span>
                                    <?php endif; ?>
                                </small>
                            </td>
                            <td>
                                <span class="badge bg-<?= $post['post_type'] === 'article' ? 'primary' : 'success' ?>">
                                    <?= $post['post_type'] === 'article' ? 'Artigo' : 'Review' ?>
                                </span>
                            </td>
                            <td>
                                <small><?= e($post['category'] ?? '-') ?></small>
                            </td>
                            <td>
                                <small><?= date('d/m H:i', strtotime($post['created_at'])) ?></small>
                            </td>
                            <td>
                                <?php
                                $statusColors = [
                                    'pending' => 'warning',
                                    'processing' => 'info',
                                    'completed' => 'success',
                                    'failed' => 'danger',
                                    'published' => 'primary',
                                    'draft' => 'secondary'
                                ];
                                $statusLabels = [
                                    'pending' => 'Pendente',
                                    'processing' => 'Processando',
                                    'completed' => 'Concluído',
                                    'failed' => 'Falhou',
                                    'published' => 'Publicado',
                                    'draft' => 'Rascunho'
                                ];
                                $statusColor = $statusColors[$post['status']] ?? 'secondary';
                                $statusLabel = $statusLabels[$post['status']] ?? ucfirst($post['status']);
                                ?>
                                <span class="badge bg-<?= $statusColor ?>"><?= $statusLabel ?></span>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php if ($totalPendingPosts > 15): ?>
            <div class="text-center">
                <small class="text-muted">
                    Mostrando 15 de <?= $totalPendingPosts ?> posts pendentes do dashboard
                </small>
            </div>
        <?php endif; ?>
    </div>
</div>
<?php endif; ?>


<!-- Lista de Sites/Clientes -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-list me-2"></i>
            Sites e Estatísticas de Posts
        </h5>
    </div>
    <div class="card-body">
        <?php if (empty($sites)): ?>
            <div class="text-center py-4">
                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                <h6>Nenhum site encontrado</h6>
                <p class="text-muted">Não há sites cadastrados ou que correspondam aos filtros aplicados.</p>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Status</th>
                            <th>Cliente</th>
                            <th>Site</th>
                            <th>Posts</th>
                            <th>Pendentes</th>
                            <th>Status Dashboard</th>
                            <th>Última Atividade</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($sites as $site): ?>
                            <tr>
                                <td>
                                    <?php
                                    $statusClass = 'status-' . $site['overall_status'];
                                    $statusText = '';
                                    switch ($site['overall_status']) {
                                        case 'success': $statusText = 'Ativo'; break;
                                        case 'error': $statusText = 'Erro'; break;
                                        case 'warning': $statusText = 'Atenção'; break;
                                        case 'pending': $statusText = 'Pendente'; break;
                                        default: $statusText = 'Neutro'; break;
                                    }
                                    ?>
                                    <span class="badge status-badge <?= $statusClass ?>"><?= $statusText ?></span>
                                </td>
                                <td>
                                    <div>
                                        <strong><?= e($site['user_name']) ?></strong>
                                        <br>
                                        <small class="text-muted"><?= e($site['user_email']) ?></small>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <strong><?= e($site['site_name'] ?: 'Site sem nome') ?></strong>
                                        <br>
                                        <small class="text-muted">
                                            <a href="<?= e($site['site_url']) ?>" target="_blank" class="text-decoration-none">
                                                <?= e($site['site_url']) ?>
                                                <i class="fas fa-external-link-alt ms-1"></i>
                                            </a>
                                        </small>
                                    </div>
                                </td>
                                <td>
                                    <div class="text-center">
                                        <div class="fw-bold text-primary"><?= number_format($site['total_posts']) ?></div>
                                        <small class="text-muted">
                                            <?= $site['published_posts'] ?> publicados
                                            <?php if ($site['failed_posts'] > 0): ?>
                                                <br><span class="text-danger"><?= $site['failed_posts'] ?> falharam</span>
                                            <?php endif; ?>
                                        </small>
                                    </div>
                                </td>
                                <td>
                                    <div class="text-center">
                                        <?php if (isset($site['pending_posts']) && $site['pending_posts'] > 0): ?>
                                            <span class="badge bg-warning"><?= $site['pending_posts'] ?> pendentes</span>
                                        <?php else: ?>
                                            <span class="text-muted">0</span>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <?php if ($site['connection_status'] === 'connected'): ?>
                                            <span class="badge bg-success">Conectado</span>
                                        <?php else: ?>
                                            <span class="badge bg-warning"><?= ucfirst($site['connection_status']) ?></span>
                                        <?php endif; ?>

                                        <?php if (!empty($site['last_post_status'])): ?>
                                            <br>
                                            <small class="text-muted">
                                                Último:
                                                <?php
                                                $statusLabels = [
                                                    'pending' => 'Pendente',
                                                    'processing' => 'Processando',
                                                    'completed' => 'Concluído',
                                                    'failed' => 'Falhou',
                                                    'published' => 'Publicado',
                                                    'draft' => 'Rascunho'
                                                ];
                                                echo $statusLabels[$site['last_post_status']] ?? ucfirst($site['last_post_status']);
                                                ?>
                                            </small>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <?php if ($site['last_activity'] && $site['last_activity'] !== '1970-01-01 00:00:00'): ?>
                                        <div>
                                            <?= date('d/m/Y', strtotime($site['last_activity'])) ?>
                                            <br>
                                            <small class="text-muted"><?= date('H:i', strtotime($site['last_activity'])) ?></small>
                                        </div>
                                    <?php else: ?>
                                        <span class="text-muted">Nunca</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="<?= url("/admin/clients/{$site['user_id']}") ?>"
                                           class="btn btn-outline-primary" title="Ver Cliente">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <?php if ($site['site_id'] && $site['total_posts'] > 0): ?>
                                            <button class="btn btn-outline-info"
                                                    onclick="viewSitePosts(<?= $site['site_id'] ?>, '<?= e($site['site_name']) ?>')"
                                                    title="Ver Posts do Site">
                                                <i class="fas fa-file-alt"></i>
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
function viewSitePosts(siteId, siteName) {
    // Redirecionar para uma página que mostra os posts do site específico
    // Por enquanto, vamos mostrar um alert com informações
    alert(`Visualizar posts do site: ${siteName}\nSite ID: ${siteId}\n\nEsta funcionalidade pode ser implementada para mostrar uma lista detalhada dos posts deste site.`);
}

// Auto-refresh a cada 2 minutos para manter os dados atualizados
setInterval(function() {
    // Só recarrega se não há modais abertos
    if (!document.querySelector('.modal.show')) {
        location.reload();
    }
}, 120000);
</script>

<?php
$content = ob_get_clean();
include __DIR__ . '/../../layouts/app.php';
?>
