<?php include __DIR__ . '/../../layouts/client-header.php'; ?>

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <?php if ($post['post_type'] === 'article'): ?>
                        <i class="fas fa-file-alt text-primary"></i> Artigo Simples
                    <?php else: ?>
                        <i class="fas fa-star text-warning"></i> Produto Review
                    <?php endif; ?>
                </h1>
                <div>
                    <a href="/client/posts" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Voltar
                    </a>
                    <?php if (in_array($post['status'], ['draft', 'pending', 'failed'])): ?>
                        <a href="/client/posts/<?= $post['id'] ?>/edit" class="btn btn-warning">
                            <i class="fas fa-edit"></i> Editar
                        </a>
                    <?php endif; ?>
                    <?php if ($post['status'] === 'draft'): ?>
                        <form method="POST" action="/client/posts/<?= $post['id'] ?>/publish" style="display: inline;">
                            <button type="submit" class="btn btn-primary" 
                                    onclick="return confirm('Tem certeza que deseja publicar este post?')">
                                <i class="fas fa-paper-plane"></i> Publicar
                            </button>
                        </form>
                    <?php endif; ?>
                </div>
            </div>

            <?php if (isset($_GET['success'])): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?= htmlspecialchars($_GET['success']) ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (isset($_GET['error'])): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= htmlspecialchars($_GET['error']) ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <div class="row">
                <div class="col-md-8">
                    <!-- Informações Principais -->
                    <div class="card mb-4">
                        <div class="card-body">
                            <h2 class="card-title"><?= htmlspecialchars($post['title']) ?></h2>
                            
                            <?php if ($post['excerpt']): ?>
                                <div class="card bg-info bg-opacity-10 border-info mb-3">
                                    <div class="card-body">
                                        <strong>Resumo:</strong> <?= htmlspecialchars($post['excerpt']) ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                            
                            <div class="post-content">
                                <?= nl2br(htmlspecialchars($post['content'])) ?>
                            </div>
                        </div>
                    </div>

                    <?php if ($post['post_type'] === 'product_review'): ?>
                    <!-- Informações do Produto -->
                    <div class="card mb-4">
                        <div class="card-body">
                            <h5 class="card-title">
                                <i class="fas fa-box"></i> Informações do Produto
                            </h5>

                            <div class="row">
                                <div class="col-md-12">
                                    <strong>Nome do Produto:</strong><br>
                                    <?= htmlspecialchars($post['product_name']) ?>
                                </div>
                            </div>

                            <?php if ($post['product_description']): ?>
                            <div class="row mt-3">
                                <div class="col-md-12">
                                    <strong>Descrição:</strong><br>
                                    <?= nl2br(htmlspecialchars($post['product_description'])) ?>
                                </div>
                            </div>
                            <?php endif; ?>

                            <?php if ($post['keyword']): ?>
                            <div class="row mt-3">
                                <div class="col-md-12">
                                    <strong>Palavra-Chave Principal:</strong><br>
                                    <span class="badge bg-primary"><?= htmlspecialchars($post['keyword']) ?></span>
                                </div>
                            </div>
                            <?php endif; ?>

                            <?php if ($post['youtube_video']): ?>
                            <div class="row mt-3">
                                <div class="col-md-12">
                                    <strong>Vídeo YouTube:</strong><br>
                                    <a href="<?= htmlspecialchars($post['youtube_video']) ?>" target="_blank" class="btn btn-sm btn-outline-danger">
                                        <i class="fab fa-youtube"></i> Ver Vídeo
                                    </a>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>

                <div class="col-md-4">
                    <!-- Status e Informações -->
                    <div class="card mb-4">
                        <div class="card-body">
                            <h6 class="card-title">
                                <i class="fas fa-info-circle"></i> Informações
                            </h6>
                            
                            <div class="mb-3">
                                <strong>Status:</strong><br>
                                <?php
                                $statusClasses = [
                                    'draft' => 'bg-secondary',
                                    'pending' => 'bg-warning',
                                    'processing' => 'bg-info',
                                    'completed' => 'bg-success',
                                    'published' => 'bg-success',
                                    'failed' => 'bg-danger'
                                ];
                                $statusLabels = [
                                    'draft' => 'Rascunho',
                                    'pending' => 'Pendente',
                                    'processing' => 'Processando',
                                    'completed' => 'Concluído',
                                    'published' => 'Publicado',
                                    'failed' => 'Falhou'
                                ];
                                ?>
                                <span class="badge <?= $statusClasses[$post['status']] ?>">
                                    <?= $statusLabels[$post['status']] ?>
                                </span>
                            </div>

                            <div class="mb-3">
                                <strong>Site:</strong><br>
                                <?= htmlspecialchars($post['site_name']) ?><br>
                                <small class="text-muted"><?= htmlspecialchars($post['site_url']) ?></small>
                            </div>

                            <div class="mb-3">
                                <strong>Criado em:</strong><br>
                                <?= date('d/m/Y H:i', strtotime($post['created_at'])) ?>
                            </div>

                            <?php if ($post['updated_at'] !== $post['created_at']): ?>
                            <div class="mb-3">
                                <strong>Atualizado em:</strong><br>
                                <?= date('d/m/Y H:i', strtotime($post['updated_at'])) ?>
                            </div>
                            <?php endif; ?>

                            <?php if ($post['published_at']): ?>
                            <div class="mb-3">
                                <strong>Publicado em:</strong><br>
                                <?= date('d/m/Y H:i', strtotime($post['published_at'])) ?>
                            </div>
                            <?php endif; ?>

                            <?php if ($post['wordpress_post_id']): ?>
                            <div class="mb-3">
                                <strong>ID no WordPress:</strong><br>
                                #<?= $post['wordpress_post_id'] ?>
                            </div>
                            <?php endif; ?>

                            <?php if ($post['error_message']): ?>
                            <div class="card bg-danger bg-opacity-10 border-danger">
                                <div class="card-body">
                                    <strong class="text-danger">Erro:</strong><br>
                                    <span class="text-danger"><?= htmlspecialchars($post['error_message']) ?></span>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- SEO e Categorização -->
                    <div class="card mb-4">
                        <div class="card-body">
                            <h6 class="card-title">
                                <i class="fas fa-search"></i> SEO e Categorização
                            </h6>

                            <?php if ($post['category']): ?>
                            <div class="mb-3">
                                <strong>Categoria:</strong><br>
                                <span class="badge bg-primary"><?= htmlspecialchars($post['category']) ?></span>
                            </div>
                            <?php endif; ?>

                            <?php if ($post['existing_tags']): ?>
                            <div class="mb-3">
                                <strong>Tags Existentes:</strong><br>
                                <?php
                                $tags = explode(',', $post['existing_tags']);
                                foreach ($tags as $tag) {
                                    echo '<span class="badge bg-secondary me-1">' . htmlspecialchars(trim($tag)) . '</span>';
                                }
                                ?>
                            </div>
                            <?php endif; ?>

                            <?php if ($post['slug']): ?>
                            <div class="mb-3">
                                <strong>Slug:</strong><br>
                                <code><?= htmlspecialchars($post['slug']) ?></code>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.post-content {
    line-height: 1.6;
    font-size: 1.1rem;
}

.post-content p {
    margin-bottom: 1rem;
}

.badge {
    font-size: 0.8rem;
}
</style>

<?php include __DIR__ . '/../../layouts/client-footer.php'; ?>
