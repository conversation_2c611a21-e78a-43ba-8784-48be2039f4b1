<?php
/**
 * API Controller para Posts dos Clientes
 * Sistema de Dashboard E1Copy AI
 */

class ApiClientPostsController extends Controller {
    
    /**
     * Lista posts dos clientes com filtros - NOVA IMPLEMENTAÇÃO FUNCIONAL
     * GET /api/v1/client-posts
     */
    public function index() {
        try {
            // Parâmetros de filtro
            $status = $this->input('status', '');
            $postType = $this->input('post_type', '');
            $userId = $this->input('user_id', '');
            $siteId = $this->input('site_id', '');
            $limit = min(100, max(1, (int)$this->input('limit', 50)));
            $offset = max(0, (int)$this->input('offset', 0));
            $includeContent = $this->input('include_content', 'true') === 'true';

            // IMPLEMENTAÇÃO NOVA E SIMPLES QUE FUNCIONA

            // 1. Construir WHERE clause - REMOVIDO FILTRO POR ROLE
            $conditions = ['u.status = \'active\''];
            $params = [];

            if ($status) {
                $conditions[] = 'cp.status = :status';
                $params['status'] = $status;
            }
            if ($postType) {
                $conditions[] = 'cp.post_type = :post_type';
                $params['post_type'] = $postType;
            }
            if ($userId) {
                $conditions[] = 'cp.user_id = :user_id';
                $params['user_id'] = $userId;
            }
            if ($siteId) {
                $conditions[] = 'cp.site_id = :site_id';
                $params['site_id'] = $siteId;
            }

            $whereClause = implode(' AND ', $conditions);

            // 2. Campos base
            $baseFields = "
                cp.id, cp.site_id, cp.user_id, cp.post_type, cp.title, cp.excerpt,
                cp.keywords, cp.category, cp.tags, cp.slug, cp.product_name,
                cp.product_url, cp.product_price, cp.product_rating, cp.product_pros,
                cp.product_cons, cp.affiliate_link, cp.status, cp.wordpress_post_id,
                cp.error_message, cp.created_at, cp.updated_at, cp.published_at,
                u.name as user_name, u.email as user_email,
                COALESCE(cs.site_name, 'Unknown Site') as site_name,
                COALESCE(cs.site_url, 'unknown') as site_url
            ";

            if ($includeContent) {
                $baseFields .= ", cp.content";
            }

            // 3. Query principal
            $posts = $this->db->fetchAll("
                SELECT {$baseFields}
                FROM client_posts cp
                INNER JOIN users u ON cp.user_id = u.id
                LEFT JOIN client_sites cs ON cp.site_id = cs.id
                WHERE {$whereClause}
                ORDER BY cp.created_at DESC
                LIMIT {$limit} OFFSET {$offset}
            ", $params);

            // 4. Contar total
            $totalCount = $this->db->fetch("
                SELECT COUNT(DISTINCT cp.id) as total
                FROM client_posts cp
                INNER JOIN users u ON cp.user_id = u.id
                WHERE {$whereClause}
            ", $params)['total'];
            
            // 5. Processar dados dos posts
            foreach ($posts as &$post) {
                // Converter campos JSON
                if (!empty($post['tags'])) {
                    $post['tags'] = json_decode($post['tags'], true) ?: [];
                }
                if (!empty($post['product_pros'])) {
                    $post['product_pros'] = json_decode($post['product_pros'], true) ?: [];
                }
                if (!empty($post['product_cons'])) {
                    $post['product_cons'] = json_decode($post['product_cons'], true) ?: [];
                }

                // Converter tipos numéricos
                $post['id'] = (int)$post['id'];
                $post['site_id'] = (int)$post['site_id'];
                $post['user_id'] = (int)$post['user_id'];
                $post['wordpress_post_id'] = $post['wordpress_post_id'] ? (int)$post['wordpress_post_id'] : null;
                $post['product_price'] = $post['product_price'] ? (float)$post['product_price'] : null;
                $post['product_rating'] = $post['product_rating'] ? (float)$post['product_rating'] : null;

                // Processar imagens
                $post['images'] = [];
                $post['api_key'] = '';
            }
            
            // 6. Organizar por cliente com indexação por user_id e conteúdos separados
            $postsByClient = [];
            foreach ($posts as $post) {
                $userId = $post['user_id'];

                if (!isset($postsByClient[$userId])) {
                    $postsByClient[$userId] = [
                        'client_info' => [
                            'user_id' => $post['user_id'],
                            'user_name' => $post['user_name'],
                            'user_email' => $post['user_email']
                        ],
                        'posts' => [],
                        'post_contents' => [],
                        'post_titles' => [],
                        'post_ids' => [],
                        'total_posts' => 0
                    ];
                }

                // Separar conteúdos em arrays distintos
                $postData = [
                    'id' => $post['id'],
                    'site_id' => $post['site_id'],
                    'post_type' => $post['post_type'],
                    'title' => $post['title'],
                    'excerpt' => $post['excerpt'],
                    'keywords' => $post['keywords'],
                    'category' => $post['category'],
                    'tags' => $post['tags'],
                    'slug' => $post['slug'],
                    'product_name' => $post['product_name'],
                    'product_url' => $post['product_url'],
                    'product_price' => $post['product_price'],
                    'product_rating' => $post['product_rating'],
                    'product_pros' => $post['product_pros'],
                    'product_cons' => $post['product_cons'],
                    'affiliate_link' => $post['affiliate_link'],
                    'status' => $post['status'],
                    'wordpress_post_id' => $post['wordpress_post_id'],
                    'error_message' => $post['error_message'],
                    'created_at' => $post['created_at'],
                    'updated_at' => $post['updated_at'],
                    'published_at' => $post['published_at'],
                    'site_name' => $post['site_name'],
                    'site_url' => $post['site_url'],
                    'images' => $post['images']
                ];

                $postsByClient[$userId]['posts'][] = $postData;
                $postsByClient[$userId]['post_ids'][] = $post['id'];
                $postsByClient[$userId]['post_titles'][] = $post['title'];

                // Adicionar conteúdo separadamente se existir
                if (isset($post['content']) && !empty($post['content'])) {
                    $postsByClient[$userId]['post_contents'][] = [
                        'post_id' => $post['id'],
                        'content' => $post['content']
                    ];
                }

                $postsByClient[$userId]['total_posts']++;
            }

            // 7. Organizar por site
            $postsBySite = [];
            foreach ($posts as $post) {
                $siteId = $post['site_id'];

                if (!isset($postsBySite[$siteId])) {
                    $postsBySite[$siteId] = [
                        'site_info' => [
                            'site_id' => $post['site_id'],
                            'site_name' => $post['site_name'],
                            'site_url' => $post['site_url'],
                            'user_name' => $post['user_name'],
                            'user_email' => $post['user_email'],
                            'api_key' => ''
                        ],
                        'posts' => [],
                        'total_posts' => 0
                    ];
                }
                $postsBySite[$siteId]['posts'][] = $post;
                $postsBySite[$siteId]['total_posts']++;
            }

            // 8. Estatísticas
            $stats = [
                'total_posts' => (int)$totalCount,
                'returned_posts' => count($posts),
                'total_clients' => count($postsByClient),
                'total_sites' => count($postsBySite),
                'limit' => $limit,
                'offset' => $offset,
                'has_more' => ($offset + $limit) < $totalCount
            ];

            // 9. Resposta final com indexação por user_id
            $this->json([
                'success' => true,
                'data' => [
                    'posts' => $posts,
                    'posts_by_client_id' => $postsByClient, // Indexado por user_id
                    'posts_by_client' => array_values($postsByClient), // Array sequencial (compatibilidade)
                    'posts_by_site' => array_values($postsBySite),
                    'stats' => $stats,
                    'filters_applied' => [
                        'status' => $status ?: null,
                        'post_type' => $postType ?: null,
                        'user_id' => $userId ?: null,
                        'site_id' => $siteId ?: null,
                        'include_content' => $includeContent
                    ]
                ],
                'timestamp' => date('Y-m-d H:i:s')
            ]);

        } catch (Exception $e) {
            $this->json([
                'success' => false,
                'error' => $e->getMessage(),
                'timestamp' => date('Y-m-d H:i:s')
            ], 500);
        }
    }

    /**
     * Busca um post específico por ID
     * GET /api/v1/client-posts/{id}
     */
    public function show($id) {
        try {
            $post = $this->db->fetch("
                SELECT
                    cp.*,
                    cs.site_name,
                    cs.site_url,
                    u.name as user_name,
                    u.email as user_email,
                    ak.api_key
                FROM client_posts cp
                LEFT JOIN client_sites cs ON cp.site_id = cs.id
                LEFT JOIN users u ON cp.user_id = u.id
                LEFT JOIN api_keys ak ON cs.api_key_id = ak.id
                WHERE cp.id = :id AND u.status = 'active'
            ", ['id' => $id]);

            if (!$post) {
                $this->json([
                    'success' => false,
                    'error' => 'Post não encontrado',
                    'timestamp' => date('Y-m-d H:i:s')
                ], 404);
                return;
            }

            // Processar dados do post
            $this->processPostData($post);

            $this->json([
                'success' => true,
                'data' => $post,
                'timestamp' => date('Y-m-d H:i:s')
            ]);

        } catch (Exception $e) {
            $this->json([
                'success' => false,
                'error' => $e->getMessage(),
                'timestamp' => date('Y-m-d H:i:s')
            ], 500);
        }
    }

    /**
     * Retorna estatísticas dos posts dos clientes
     * GET /api/v1/client-posts/stats
     */
    public function stats() {
        try {
            $stats = $this->db->fetch("
                SELECT
                    COUNT(*) as total_posts,
                    COUNT(DISTINCT cp.user_id) as total_clients,
                    COUNT(DISTINCT cp.site_id) as total_sites,
                    SUM(CASE WHEN cp.status = 'pending' THEN 1 ELSE 0 END) as pending_posts,
                    SUM(CASE WHEN cp.status = 'processing' THEN 1 ELSE 0 END) as processing_posts,
                    SUM(CASE WHEN cp.status = 'completed' THEN 1 ELSE 0 END) as completed_posts,
                    SUM(CASE WHEN cp.status = 'published' THEN 1 ELSE 0 END) as published_posts,
                    SUM(CASE WHEN cp.status = 'failed' THEN 1 ELSE 0 END) as failed_posts,
                    SUM(CASE WHEN cp.status = 'draft' THEN 1 ELSE 0 END) as draft_posts,
                    SUM(CASE WHEN cp.post_type = 'article' THEN 1 ELSE 0 END) as articles,
                    SUM(CASE WHEN cp.post_type = 'product_review' THEN 1 ELSE 0 END) as product_reviews
                FROM client_posts cp
                INNER JOIN users u ON cp.user_id = u.id
                WHERE u.status = 'active'
            ");

            // Estatísticas por período
            $dailyStats = $this->db->fetchAll("
                SELECT
                    DATE(cp.created_at) as date,
                    COUNT(*) as posts_created,
                    SUM(CASE WHEN cp.status = 'published' THEN 1 ELSE 0 END) as posts_published
                FROM client_posts cp
                INNER JOIN users u ON cp.user_id = u.id
                WHERE u.status = 'active'
                AND cp.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                GROUP BY DATE(cp.created_at)
                ORDER BY date DESC
            ");

            // Top clientes por posts
            $topClients = $this->db->fetchAll("
                SELECT
                    u.name as user_name,
                    u.email as user_email,
                    COUNT(cp.id) as total_posts,
                    SUM(CASE WHEN cp.status = 'published' THEN 1 ELSE 0 END) as published_posts
                FROM client_posts cp
                INNER JOIN users u ON cp.user_id = u.id
                WHERE u.status = 'active'
                GROUP BY u.id, u.name, u.email
                ORDER BY total_posts DESC
                LIMIT 10
            ");

            $this->json([
                'success' => true,
                'data' => [
                    'general_stats' => $stats,
                    'daily_stats' => $dailyStats,
                    'top_clients' => $topClients
                ],
                'timestamp' => date('Y-m-d H:i:s')
            ]);

        } catch (Exception $e) {
            $this->json([
                'success' => false,
                'error' => $e->getMessage(),
                'timestamp' => date('Y-m-d H:i:s')
            ], 500);
        }
    }

    /**
     * Processa dados de um post (conversões de tipo, etc.)
     */
    private function processPostData(&$post) {
        // Converter campos JSON
        if (!empty($post['tags'])) {
            $post['tags'] = json_decode($post['tags'], true) ?: [];
        }

        if (!empty($post['product_pros'])) {
            $post['product_pros'] = json_decode($post['product_pros'], true) ?: [];
        }

        if (!empty($post['product_cons'])) {
            $post['product_cons'] = json_decode($post['product_cons'], true) ?: [];
        }

        // Converter tipos numéricos
        $post['id'] = (int)$post['id'];
        $post['site_id'] = (int)$post['site_id'];
        $post['user_id'] = (int)$post['user_id'];
        $post['wordpress_post_id'] = $post['wordpress_post_id'] ? (int)$post['wordpress_post_id'] : null;
        $post['product_price'] = $post['product_price'] ? (float)$post['product_price'] : null;
        $post['product_rating'] = $post['product_rating'] ? (float)$post['product_rating'] : null;

        // Processar imagens
        $post['images'] = [];
        for ($i = 1; $i <= 5; $i++) {
            $imageField = "product_images{$i}";
            if (!empty($post[$imageField])) {
                $post['images'][] = $post[$imageField];
            }
            unset($post[$imageField]);
        }
    }

    /**
     * Debug endpoint para verificar posts pendentes
     * GET /api/v1/client-posts/debug
     */
    public function debug() {
        try {
            // 1. Verificar posts pendentes por cliente
            $postsByClient = $this->db->fetchAll("
                SELECT
                    u.id as user_id,
                    u.name as user_name,
                    u.email as user_email,
                    u.status as user_status,
                    COUNT(cp.id) as total_posts,
                    COUNT(CASE WHEN cp.status = 'pending' THEN 1 END) as pending_posts
                FROM users u
                LEFT JOIN client_posts cp ON u.id = cp.user_id
                WHERE u.role = 'client'
                GROUP BY u.id, u.name, u.email, u.status
                HAVING pending_posts > 0 OR total_posts > 0
                ORDER BY pending_posts DESC, total_posts DESC
            ");

            // 2. Verificar posts pendentes únicos
            $pendingPosts = $this->db->fetchAll("
                SELECT DISTINCT
                    cp.id,
                    cp.title,
                    cp.status,
                    cp.user_id,
                    u.name as user_name,
                    u.email as user_email,
                    u.status as user_status,
                    cp.created_at
                FROM client_posts cp
                INNER JOIN users u ON cp.user_id = u.id
                WHERE cp.status = 'pending' AND u.status = 'active'
                ORDER BY cp.created_at DESC
            ");

            // 3. Verificar query da API
            $apiQuery = "
                SELECT
                    cp.id,
                    cp.title,
                    cp.user_id,
                    u.name as user_name,
                    u.email as user_email,
                    cs.site_name
                FROM client_posts cp
                LEFT JOIN client_sites cs ON cp.site_id = cs.id
                LEFT JOIN users u ON cp.user_id = u.id
                LEFT JOIN api_keys ak ON cs.api_key_id = ak.id
                WHERE u.status = 'active' AND cp.status = 'pending'
                GROUP BY cp.id
                ORDER BY cp.created_at DESC
            ";

            $apiResults = $this->db->fetchAll($apiQuery);

            // 4. Verificar clientes ativos
            $activeClients = $this->db->fetchAll("
                SELECT id, name, email, status, created_at
                FROM users
                WHERE role = 'client'
                ORDER BY created_at DESC
            ");

            $this->json([
                'success' => true,
                'debug_data' => [
                    'posts_by_client' => $postsByClient,
                    'pending_posts_unique' => $pendingPosts,
                    'api_query_results' => $apiResults,
                    'active_clients' => $activeClients,
                    'summary' => [
                        'total_clients_with_posts' => count($postsByClient),
                        'total_pending_posts' => count($pendingPosts),
                        'api_results_count' => count($apiResults),
                        'total_active_clients' => count($activeClients)
                    ]
                ],
                'timestamp' => date('Y-m-d H:i:s')
            ]);

        } catch (Exception $e) {
            $this->json([
                'success' => false,
                'error' => $e->getMessage(),
                'timestamp' => date('Y-m-d H:i:s')
            ], 500);
        }
    }

    /**
     * Endpoint de teste com dados simulados de múltiplos clientes
     * GET /api/v1/client-posts/test-multiple
     */
    public function testMultiple() {
        try {
            // Buscar posts reais
            $realPosts = $this->db->fetchAll("
                SELECT
                    cp.id,
                    cp.title,
                    cp.post_type,
                    cp.status,
                    cp.user_id,
                    u.name as user_name,
                    u.email as user_email,
                    cs.site_name,
                    cp.created_at
                FROM client_posts cp
                INNER JOIN users u ON cp.user_id = u.id
                LEFT JOIN client_sites cs ON cp.site_id = cs.id
                WHERE cp.status = 'pending' AND u.status = 'active'
                ORDER BY cp.created_at DESC
                LIMIT 5
            ");

            // Simular posts de outros clientes
            $simulatedPosts = [
                [
                    'id' => 999,
                    'title' => 'Review do Samsung Galaxy S24 - Análise Completa',
                    'post_type' => 'product_review',
                    'status' => 'pending',
                    'user_id' => 999,
                    'user_name' => 'Maria Santos',
                    'user_email' => '<EMAIL>',
                    'site_name' => 'Tech Reviews Blog',
                    'created_at' => date('Y-m-d H:i:s', strtotime('-2 hours'))
                ],
                [
                    'id' => 998,
                    'title' => 'Melhores Notebooks 2024 - Guia Completo',
                    'post_type' => 'article',
                    'status' => 'pending',
                    'user_id' => 998,
                    'user_name' => 'João Oliveira',
                    'user_email' => '<EMAIL>',
                    'site_name' => 'Dicas de Tecnologia',
                    'created_at' => date('Y-m-d H:i:s', strtotime('-4 hours'))
                ],
                [
                    'id' => 997,
                    'title' => 'Airfryer Philips Walita - Vale a Pena?',
                    'post_type' => 'product_review',
                    'status' => 'pending',
                    'user_id' => 999,
                    'user_name' => 'Maria Santos',
                    'user_email' => '<EMAIL>',
                    'site_name' => 'Tech Reviews Blog',
                    'created_at' => date('Y-m-d H:i:s', strtotime('-6 hours'))
                ],
                [
                    'id' => 996,
                    'title' => 'Como Escolher o Melhor Smartphone em 2024',
                    'post_type' => 'article',
                    'status' => 'pending',
                    'user_id' => 997,
                    'user_name' => 'Ana Costa',
                    'user_email' => '<EMAIL>',
                    'site_name' => 'Guia do Consumidor',
                    'created_at' => date('Y-m-d H:i:s', strtotime('-8 hours'))
                ]
            ];

            // Combinar posts reais e simulados
            $allPosts = array_merge($realPosts, $simulatedPosts);

            // Organizar por cliente
            $postsByClient = [];
            foreach ($allPosts as $post) {
                $clientKey = $post['user_id'] . '_' . $post['user_email'];

                if (!isset($postsByClient[$clientKey])) {
                    $postsByClient[$clientKey] = [
                        'client_info' => [
                            'user_id' => (int)$post['user_id'],
                            'user_name' => $post['user_name'],
                            'user_email' => $post['user_email']
                        ],
                        'posts' => [],
                        'total_posts' => 0
                    ];
                }
                $postsByClient[$clientKey]['posts'][] = $post;
                $postsByClient[$clientKey]['total_posts']++;
            }

            $this->json([
                'success' => true,
                'data' => [
                    'posts' => $allPosts,
                    'posts_by_client' => array_values($postsByClient),
                    'stats' => [
                        'total_posts' => count($allPosts),
                        'real_posts' => count($realPosts),
                        'simulated_posts' => count($simulatedPosts),
                        'total_clients' => count($postsByClient)
                    ]
                ],
                'note' => 'Este endpoint combina posts reais com dados simulados para demonstrar como a API funciona com múltiplos clientes',
                'timestamp' => date('Y-m-d H:i:s')
            ]);

        } catch (Exception $e) {
            $this->json([
                'success' => false,
                'error' => $e->getMessage(),
                'timestamp' => date('Y-m-d H:i:s')
            ], 500);
        }
    }

    /**
     * Debug específico para organização por cliente
     * GET /api/v1/client-posts/debug-organization
     */
    public function debugOrganization() {
        try {
            // Buscar posts pendentes exatamente como no método principal
            $status = 'pending';
            $conditions = ['u.status = \'active\''];
            $params = [];

            if ($status) {
                $conditions[] = "cp.status = :status";
                $params['status'] = $status;
            }

            $whereClause = implode(' AND ', $conditions);

            // Query exata do método principal
            $posts = $this->db->fetchAll("
                SELECT
                    cp.id,
                    cp.site_id,
                    cp.user_id,
                    cp.post_type,
                    cp.title,
                    cp.status,
                    cp.created_at,
                    COALESCE(cs.site_name, 'Unknown Site') as site_name,
                    COALESCE(cs.site_url, 'unknown') as site_url,
                    COALESCE(u.name, 'Unknown User') as user_name,
                    COALESCE(u.email, 'unknown') as user_email,
                    COALESCE(ak.api_key, '') as api_key
                FROM client_posts cp
                LEFT JOIN client_sites cs ON cp.site_id = cs.id
                LEFT JOIN users u ON cp.user_id = u.id
                LEFT JOIN api_keys ak ON cs.api_key_id = ak.id
                WHERE {$whereClause}
                GROUP BY cp.id
                ORDER BY cp.created_at DESC
            ", $params);

            // Simular a organização por cliente
            $postsByClient = [];
            $processedPosts = [];
            $debugInfo = [];

            foreach ($posts as $post) {
                $postKey = $post['id'] . '_' . $post['user_id'] . '_' . $post['site_id'];
                $clientKey = $post['user_id'] . '_' . $post['user_email'];

                $debugInfo[] = [
                    'post_id' => $post['id'],
                    'post_key' => $postKey,
                    'client_key' => $clientKey,
                    'user_id' => $post['user_id'],
                    'user_name' => $post['user_name'],
                    'already_processed' => isset($processedPosts[$postKey])
                ];

                if (isset($processedPosts[$postKey])) {
                    continue;
                }
                $processedPosts[$postKey] = true;

                if (!isset($postsByClient[$clientKey])) {
                    $postsByClient[$clientKey] = [
                        'client_info' => [
                            'user_id' => (int)$post['user_id'],
                            'user_name' => $post['user_name'],
                            'user_email' => $post['user_email']
                        ],
                        'posts' => [],
                        'total_posts' => 0
                    ];
                }
                $postsByClient[$clientKey]['posts'][] = $post;
                $postsByClient[$clientKey]['total_posts']++;
            }

            $this->json([
                'success' => true,
                'debug_data' => [
                    'raw_posts' => $posts,
                    'debug_info' => $debugInfo,
                    'posts_by_client_keys' => array_keys($postsByClient),
                    'posts_by_client' => array_values($postsByClient),
                    'summary' => [
                        'total_raw_posts' => count($posts),
                        'total_clients' => count($postsByClient),
                        'processed_posts' => count($processedPosts)
                    ]
                ],
                'timestamp' => date('Y-m-d H:i:s')
            ]);

        } catch (Exception $e) {
            $this->json([
                'success' => false,
                'error' => $e->getMessage(),
                'timestamp' => date('Y-m-d H:i:s')
            ], 500);
        }
    }

    /**
     * Versão simplificada para testar posts pendentes
     * GET /api/v1/client-posts/simple-pending
     */
    public function simplePending() {
        try {
            // Query simplificada
            $posts = $this->db->fetchAll("
                SELECT
                    cp.id,
                    cp.site_id,
                    cp.user_id,
                    cp.post_type,
                    cp.title,
                    cp.status,
                    cp.created_at,
                    COALESCE(cs.site_name, 'Unknown Site') as site_name,
                    COALESCE(u.name, 'Unknown User') as user_name,
                    COALESCE(u.email, 'unknown') as user_email
                FROM client_posts cp
                LEFT JOIN client_sites cs ON cp.site_id = cs.id
                LEFT JOIN users u ON cp.user_id = u.id
                WHERE u.status = 'active' AND cp.status = 'pending'
                GROUP BY cp.id
                ORDER BY cp.created_at DESC
            ");

            // Organizar por cliente
            $postsByClient = [];
            foreach ($posts as $post) {
                $clientKey = $post['user_id'] . '_' . $post['user_email'];

                if (!isset($postsByClient[$clientKey])) {
                    $postsByClient[$clientKey] = [
                        'client_info' => [
                            'user_id' => (int)$post['user_id'],
                            'user_name' => $post['user_name'],
                            'user_email' => $post['user_email']
                        ],
                        'posts' => [],
                        'total_posts' => 0
                    ];
                }
                $postsByClient[$clientKey]['posts'][] = $post;
                $postsByClient[$clientKey]['total_posts']++;
            }

            $this->json([
                'success' => true,
                'data' => [
                    'posts' => $posts,
                    'posts_by_client' => array_values($postsByClient),
                    'stats' => [
                        'total_posts' => count($posts),
                        'total_clients' => count($postsByClient)
                    ]
                ],
                'timestamp' => date('Y-m-d H:i:s')
            ]);

        } catch (Exception $e) {
            $this->json([
                'success' => false,
                'error' => $e->getMessage(),
                'timestamp' => date('Y-m-d H:i:s')
            ], 500);
        }
    }

    /**
     * Debug da query principal para identificar problema
     * GET /api/v1/client-posts/debug-query
     */
    public function debugQuery() {
        try {
            $status = 'pending';
            $conditions = ['u.status = \'active\''];
            $params = [];

            if ($status) {
                $conditions[] = "cp.status = :status";
                $params['status'] = $status;
            }

            $whereClause = implode(' AND ', $conditions);

            // 1. Query sem JOINs
            $postsNoJoin = $this->db->fetchAll("
                SELECT
                    cp.id,
                    cp.user_id,
                    cp.site_id,
                    cp.title,
                    cp.status
                FROM client_posts cp
                WHERE cp.status = 'pending'
                ORDER BY cp.created_at DESC
            ");

            // 2. Query com JOIN users apenas
            $postsWithUsers = $this->db->fetchAll("
                SELECT
                    cp.id,
                    cp.user_id,
                    cp.site_id,
                    cp.title,
                    cp.status,
                    u.name as user_name,
                    u.email as user_email,
                    u.status as user_status
                FROM client_posts cp
                INNER JOIN users u ON cp.user_id = u.id
                WHERE cp.status = 'pending' AND u.status = 'active'
                ORDER BY cp.created_at DESC
            ");

            // 3. Query com LEFT JOIN client_sites
            $postsWithSites = $this->db->fetchAll("
                SELECT
                    cp.id,
                    cp.user_id,
                    cp.site_id,
                    cp.title,
                    cp.status,
                    u.name as user_name,
                    u.email as user_email,
                    u.status as user_status,
                    cs.site_name,
                    cs.site_url
                FROM client_posts cp
                INNER JOIN users u ON cp.user_id = u.id
                LEFT JOIN client_sites cs ON cp.site_id = cs.id
                WHERE cp.status = 'pending' AND u.status = 'active'
                ORDER BY cp.created_at DESC
            ");

            // 4. Query atual da API
            $currentQuery = $this->db->fetchAll("
                SELECT
                    cp.id,
                    cp.user_id,
                    cp.site_id,
                    cp.title,
                    cp.status,
                    u.name as user_name,
                    u.email as user_email
                FROM client_posts cp
                LEFT JOIN client_sites cs ON cp.site_id = cs.id
                LEFT JOIN users u ON cp.user_id = u.id
                WHERE {$whereClause}
                GROUP BY cp.id
                ORDER BY cp.created_at DESC
            ", $params);

            // 5. Verificar sites dos usuários
            $userSites = $this->db->fetchAll("
                SELECT
                    u.id as user_id,
                    u.name as user_name,
                    u.email as user_email,
                    cs.id as site_id,
                    cs.site_name,
                    cs.status as site_status
                FROM users u
                LEFT JOIN client_sites cs ON u.id = cs.user_id
                WHERE u.role = 'client' AND u.status = 'active'
                ORDER BY u.id
            ");

            $this->json([
                'success' => true,
                'debug_data' => [
                    'posts_no_join' => $postsNoJoin,
                    'posts_with_users' => $postsWithUsers,
                    'posts_with_sites' => $postsWithSites,
                    'current_query' => $currentQuery,
                    'user_sites' => $userSites,
                    'summary' => [
                        'no_join_count' => count($postsNoJoin),
                        'with_users_count' => count($postsWithUsers),
                        'with_sites_count' => count($postsWithSites),
                        'current_query_count' => count($currentQuery)
                    ]
                ],
                'timestamp' => date('Y-m-d H:i:s')
            ]);

        } catch (Exception $e) {
            $this->json([
                'success' => false,
                'error' => $e->getMessage(),
                'timestamp' => date('Y-m-d H:i:s')
            ], 500);
        }
    }

    /**
     * Endpoint temporário para verificar dados reais
     * GET /api/v1/client-posts/verify-data
     */
    public function verifyData() {
        try {
            // 1. Verificar posts pendentes direto no banco
            $allPendingPosts = $this->db->fetchAll("
                SELECT id, user_id, title, status, created_at
                FROM client_posts
                WHERE status = 'pending'
                ORDER BY created_at DESC
            ");

            // 2. Verificar usuários ativos
            $activeUsers = $this->db->fetchAll("
                SELECT id, name, email, status
                FROM users
                WHERE status = 'active' AND role = 'client'
                ORDER BY id
            ");

            // 3. Verificar posts pendentes com usuários
            $postsWithUsers = $this->db->fetchAll("
                SELECT
                    cp.id,
                    cp.user_id,
                    cp.title,
                    cp.status,
                    u.name as user_name,
                    u.email as user_email,
                    u.status as user_status
                FROM client_posts cp
                INNER JOIN users u ON cp.user_id = u.id
                WHERE cp.status = 'pending' AND u.status = 'active'
                ORDER BY cp.created_at DESC
            ");

            $this->json([
                'success' => true,
                'verification_data' => [
                    'all_pending_posts' => $allPendingPosts,
                    'active_users' => $activeUsers,
                    'posts_with_users' => $postsWithUsers,
                    'summary' => [
                        'total_pending_posts' => count($allPendingPosts),
                        'total_active_users' => count($activeUsers),
                        'posts_with_active_users' => count($postsWithUsers)
                    ]
                ],
                'timestamp' => date('Y-m-d H:i:s')
            ]);

        } catch (Exception $e) {
            $this->json([
                'success' => false,
                'error' => $e->getMessage(),
                'timestamp' => date('Y-m-d H:i:s')
            ], 500);
        }
    }

    /**
     * Endpoint NOVO e SIMPLES para posts pendentes
     * GET /api/v1/client-posts/pending-simple
     */
    public function pendingSimple() {
        try {
            // Query mais simples possível
            $posts = $this->db->fetchAll("
                SELECT
                    cp.id,
                    cp.user_id,
                    cp.title,
                    cp.status,
                    cp.post_type,
                    cp.created_at,
                    u.name as user_name,
                    u.email as user_email
                FROM client_posts cp
                INNER JOIN users u ON cp.user_id = u.id
                WHERE cp.status = 'pending' AND u.status = 'active'
                ORDER BY cp.created_at DESC
            ");

            // Organizar por cliente
            $byClient = [];
            foreach ($posts as $post) {
                $userId = $post['user_id'];
                if (!isset($byClient[$userId])) {
                    $byClient[$userId] = [
                        'client_info' => [
                            'user_id' => (int)$userId,
                            'user_name' => $post['user_name'],
                            'user_email' => $post['user_email']
                        ],
                        'posts' => [],
                        'total_posts' => 0
                    ];
                }
                $byClient[$userId]['posts'][] = $post;
                $byClient[$userId]['total_posts']++;
            }

            $this->json([
                'success' => true,
                'data' => [
                    'posts' => $posts,
                    'posts_by_client' => array_values($byClient),
                    'stats' => [
                        'total_posts' => count($posts),
                        'total_clients' => count($byClient)
                    ]
                ],
                'timestamp' => date('Y-m-d H:i:s')
            ]);

        } catch (Exception $e) {
            $this->json([
                'success' => false,
                'error' => $e->getMessage(),
                'timestamp' => date('Y-m-d H:i:s')
            ], 500);
        }
    }

    /**
     * Endpoint para demonstrar a nova estrutura indexada
     * GET /api/v1/client-posts/indexed-structure
     */
    public function indexedStructure() {
        try {
            // Buscar posts pendentes
            $posts = $this->db->fetchAll("
                SELECT
                    cp.id,
                    cp.user_id,
                    cp.title,
                    cp.content,
                    cp.status,
                    cp.post_type,
                    cp.created_at,
                    u.name as user_name,
                    u.email as user_email
                FROM client_posts cp
                INNER JOIN users u ON cp.user_id = u.id
                WHERE cp.status = 'pending' AND u.status = 'active'
                ORDER BY cp.created_at DESC
            ");

            // Organizar com estrutura indexada
            $indexedByUserId = [];
            $contentsByUserId = [];
            $titlesByUserId = [];
            $idsByUserId = [];

            foreach ($posts as $post) {
                $userId = $post['user_id'];

                // Inicializar arrays se não existirem
                if (!isset($indexedByUserId[$userId])) {
                    $indexedByUserId[$userId] = [
                        'client_info' => [
                            'user_id' => (int)$userId,
                            'user_name' => $post['user_name'],
                            'user_email' => $post['user_email']
                        ],
                        'posts' => [],
                        'total_posts' => 0
                    ];
                    $contentsByUserId[$userId] = [];
                    $titlesByUserId[$userId] = [];
                    $idsByUserId[$userId] = [];
                }

                // Adicionar post (sem conteúdo)
                $postData = [
                    'id' => (int)$post['id'],
                    'title' => $post['title'],
                    'status' => $post['status'],
                    'post_type' => $post['post_type'],
                    'created_at' => $post['created_at']
                ];

                $indexedByUserId[$userId]['posts'][] = $postData;
                $indexedByUserId[$userId]['total_posts']++;

                // Separar conteúdos em arrays distintos
                $idsByUserId[$userId][] = (int)$post['id'];
                $titlesByUserId[$userId][] = $post['title'];

                if (!empty($post['content'])) {
                    $contentsByUserId[$userId][] = [
                        'post_id' => (int)$post['id'],
                        'content' => $post['content']
                    ];
                }
            }

            $this->json([
                'success' => true,
                'data' => [
                    'indexed_by_user_id' => $indexedByUserId,
                    'contents_by_user_id' => $contentsByUserId,
                    'titles_by_user_id' => $titlesByUserId,
                    'ids_by_user_id' => $idsByUserId,
                    'example_usage' => [
                        'get_user_2_posts' => 'data.indexed_by_user_id[2].posts',
                        'get_user_2_contents' => 'data.contents_by_user_id[2]',
                        'get_user_4_titles' => 'data.titles_by_user_id[4]',
                        'get_user_4_ids' => 'data.ids_by_user_id[4]'
                    ],
                    'stats' => [
                        'total_posts' => count($posts),
                        'total_users' => count($indexedByUserId),
                        'users_with_content' => count(array_filter($contentsByUserId, function($contents) {
                            return !empty($contents);
                        }))
                    ]
                ],
                'timestamp' => date('Y-m-d H:i:s')
            ]);

        } catch (Exception $e) {
            $this->json([
                'success' => false,
                'error' => $e->getMessage(),
                'timestamp' => date('Y-m-d H:i:s')
            ], 500);
        }
    }

    /**
     * Debug direto no banco para verificar posts pendentes
     * GET /api/v1/client-posts/debug-database
     */
    public function debugDatabase() {
        try {
            // 1. Verificar TODOS os posts pendentes
            $allPending = $this->db->fetchAll("
                SELECT id, user_id, title, status, created_at
                FROM client_posts
                WHERE status = 'pending'
                ORDER BY user_id, created_at DESC
            ");

            // 2. Verificar usuários ativos
            $activeUsers = $this->db->fetchAll("
                SELECT id, name, email, status, role
                FROM users
                WHERE status = 'active'
                ORDER BY id
            ");

            // 3. Verificar posts pendentes COM usuários ativos
            $pendingWithActiveUsers = $this->db->fetchAll("
                SELECT
                    cp.id,
                    cp.user_id,
                    cp.title,
                    cp.status as post_status,
                    u.name as user_name,
                    u.email as user_email,
                    u.status as user_status,
                    u.role as user_role
                FROM client_posts cp
                INNER JOIN users u ON cp.user_id = u.id
                WHERE cp.status = 'pending'
                ORDER BY cp.user_id, cp.created_at DESC
            ");

            // 4. Verificar posts pendentes COM usuários ativos E role client
            $pendingWithClients = $this->db->fetchAll("
                SELECT
                    cp.id,
                    cp.user_id,
                    cp.title,
                    cp.status as post_status,
                    u.name as user_name,
                    u.email as user_email,
                    u.status as user_status,
                    u.role as user_role
                FROM client_posts cp
                INNER JOIN users u ON cp.user_id = u.id
                WHERE cp.status = 'pending' AND u.status = 'active' AND u.role = 'client'
                ORDER BY cp.user_id, cp.created_at DESC
            ");

            // 5. Agrupar por user_id
            $groupedByUser = [];
            foreach ($pendingWithActiveUsers as $post) {
                $userId = $post['user_id'];
                if (!isset($groupedByUser[$userId])) {
                    $groupedByUser[$userId] = [
                        'user_info' => [
                            'user_id' => $userId,
                            'user_name' => $post['user_name'],
                            'user_email' => $post['user_email'],
                            'user_status' => $post['user_status'],
                            'user_role' => $post['user_role']
                        ],
                        'posts' => []
                    ];
                }
                $groupedByUser[$userId]['posts'][] = [
                    'id' => $post['id'],
                    'title' => $post['title'],
                    'post_status' => $post['post_status']
                ];
            }

            $this->json([
                'success' => true,
                'debug_data' => [
                    'all_pending_posts' => $allPending,
                    'active_users' => $activeUsers,
                    'pending_with_active_users' => $pendingWithActiveUsers,
                    'pending_with_clients' => $pendingWithClients,
                    'grouped_by_user' => $groupedByUser,
                    'summary' => [
                        'total_pending_posts' => count($allPending),
                        'total_active_users' => count($activeUsers),
                        'pending_with_active_users' => count($pendingWithActiveUsers),
                        'pending_with_clients' => count($pendingWithClients),
                        'unique_users_with_pending' => count($groupedByUser)
                    ]
                ],
                'timestamp' => date('Y-m-d H:i:s')
            ]);

        } catch (Exception $e) {
            $this->json([
                'success' => false,
                'error' => $e->getMessage(),
                'timestamp' => date('Y-m-d H:i:s')
            ], 500);
        }
    }

    /**
     * Verificar todos os posts por usuário e status
     * GET /api/v1/client-posts/check-all-posts
     */
    public function checkAllPosts() {
        try {
            // Verificar TODOS os posts de TODOS os usuários
            $allPosts = $this->db->fetchAll("
                SELECT
                    cp.id,
                    cp.user_id,
                    cp.title,
                    cp.status,
                    cp.created_at,
                    u.name as user_name,
                    u.email as user_email,
                    u.status as user_status,
                    u.role as user_role
                FROM client_posts cp
                LEFT JOIN users u ON cp.user_id = u.id
                ORDER BY cp.user_id, cp.status, cp.created_at DESC
            ");

            // Agrupar por usuário e status
            $byUserAndStatus = [];
            foreach ($allPosts as $post) {
                $userId = $post['user_id'];
                $status = $post['status'];

                if (!isset($byUserAndStatus[$userId])) {
                    $byUserAndStatus[$userId] = [
                        'user_info' => [
                            'user_id' => $userId,
                            'user_name' => $post['user_name'] ?: 'Unknown',
                            'user_email' => $post['user_email'] ?: 'unknown',
                            'user_status' => $post['user_status'] ?: 'unknown',
                            'user_role' => $post['user_role'] ?: 'unknown'
                        ],
                        'posts_by_status' => []
                    ];
                }

                if (!isset($byUserAndStatus[$userId]['posts_by_status'][$status])) {
                    $byUserAndStatus[$userId]['posts_by_status'][$status] = [];
                }

                $byUserAndStatus[$userId]['posts_by_status'][$status][] = [
                    'id' => $post['id'],
                    'title' => $post['title'],
                    'created_at' => $post['created_at']
                ];
            }

            // Estatísticas por status
            $statusStats = [];
            foreach ($allPosts as $post) {
                $status = $post['status'];
                if (!isset($statusStats[$status])) {
                    $statusStats[$status] = 0;
                }
                $statusStats[$status]++;
            }

            $this->json([
                'success' => true,
                'data' => [
                    'all_posts' => $allPosts,
                    'by_user_and_status' => $byUserAndStatus,
                    'status_statistics' => $statusStats,
                    'summary' => [
                        'total_posts' => count($allPosts),
                        'unique_users' => count($byUserAndStatus),
                        'pending_posts' => $statusStats['pending'] ?? 0
                    ]
                ],
                'timestamp' => date('Y-m-d H:i:s')
            ]);

        } catch (Exception $e) {
            $this->json([
                'success' => false,
                'error' => $e->getMessage(),
                'timestamp' => date('Y-m-d H:i:s')
            ], 500);
        }
    }
}
