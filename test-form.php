<!DOCTYPE html>
<html>
<head>
    <title>Teste de Upload</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h2>Teste de Upload de Imagens</h2>
        
        <?php if ($_SERVER['REQUEST_METHOD'] === 'POST'): ?>
            <div class="alert alert-info">
                <h4>Dados Recebidos:</h4>
                <pre><?= htmlspecialchars(print_r($_POST, true)) ?></pre>
                
                <h4>Arquivos Recebidos:</h4>
                <pre><?= htmlspecialchars(print_r($_FILES, true)) ?></pre>
                
                <?php if (isset($_FILES['product_images'])): ?>
                    <h4>Processamento das Imagens:</h4>
                    <?php for ($i = 0; $i < count($_FILES['product_images']['name']); $i++): ?>
                        <?php if (!empty($_FILES['product_images']['name'][$i])): ?>
                            <p>
                                <strong>Imagem <?= $i + 1 ?>:</strong><br>
                                Nome: <?= $_FILES['product_images']['name'][$i] ?><br>
                                Tipo: <?= $_FILES['product_images']['type'][$i] ?><br>
                                Tamanho: <?= number_format($_FILES['product_images']['size'][$i] / 1024, 2) ?> KB<br>
                                Erro: <?= $_FILES['product_images']['error'][$i] ?><br>
                                Temp: <?= $_FILES['product_images']['tmp_name'][$i] ?><br>
                                Existe temp: <?= file_exists($_FILES['product_images']['tmp_name'][$i]) ? 'SIM' : 'NÃO' ?>
                            </p>
                        <?php endif; ?>
                    <?php endfor; ?>
                <?php endif; ?>
            </div>
        <?php endif; ?>
        
        <form method="POST" enctype="multipart/form-data">
            <div class="mb-3">
                <label for="product_name" class="form-label">Nome do Produto</label>
                <input type="text" class="form-control" id="product_name" name="product_name" required>
            </div>
            
            <div class="mb-3">
                <label class="form-label">Imagens do Produto</label>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="product_image_1" class="form-label">Imagem 1 *</label>
                            <input type="file" class="form-control" id="product_image_1" name="product_images[]" required accept="image/*">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="product_image_2" class="form-label">Imagem 2 *</label>
                            <input type="file" class="form-control" id="product_image_2" name="product_images[]" required accept="image/*">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="product_image_3" class="form-label">Imagem 3 *</label>
                            <input type="file" class="form-control" id="product_image_3" name="product_images[]" required accept="image/*">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="product_image_4" class="form-label">Imagem 4 *</label>
                            <input type="file" class="form-control" id="product_image_4" name="product_images[]" required accept="image/*">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="product_image_5" class="form-label">Imagem 5 *</label>
                            <input type="file" class="form-control" id="product_image_5" name="product_images[]" required accept="image/*">
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="mb-3">
                <label for="post_cover" class="form-label">Capa do Post</label>
                <input type="file" class="form-control" id="post_cover" name="post_cover" accept="image/*">
            </div>
            
            <button type="submit" class="btn btn-primary">Testar Upload</button>
        </form>
    </div>
</body>
</html>
