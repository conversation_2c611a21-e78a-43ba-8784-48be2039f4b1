<?php
if (!defined('ABSPATH')) exit; // Evita acesso direto

/**
 * Cria a tabela para armazenar os dados dos afiliados
 */
function e1copy_ai_create_affiliates_table() {
    global $wpdb;
    $charset_collate = $wpdb->get_charset_collate();
    $table_name = $wpdb->prefix . 'e1copy_affiliates';

    $sql = "CREATE TABLE IF NOT EXISTS $table_name (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        title varchar(255) NOT NULL,
        affiliate_link text DEFAULT '',
        card_text text DEFAULT '',
        button_text varchar(255) DEFAULT '',
        product_image text DEFAULT '',
        template_id varchar(50) DEFAULT 'model1',
        content longtext NOT NULL,
        custom_css text DEFAULT '',
        shortcode varchar(100) NOT NULL,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY  (id)
    ) $charset_collate;";

    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);

    // Verificar se a coluna affiliate_link existe
    $columns = $wpdb->get_results("SHOW COLUMNS FROM $table_name");
    $column_names = [];
    foreach ($columns as $column) {
        $column_names[] = $column->Field;
    }

    // Adicionar a coluna affiliate_link se não existir
    if (!in_array('affiliate_link', $column_names)) {
        $wpdb->query("ALTER TABLE $table_name ADD COLUMN affiliate_link text DEFAULT ''");
        error_log('E1Copy Handler: Coluna affiliate_link adicionada à tabela de afiliados');
    }

    // Adicionar a coluna custom_css se não existir
    if (!in_array('custom_css', $column_names)) {
        $wpdb->query("ALTER TABLE $table_name ADD COLUMN custom_css text DEFAULT ''");
        error_log('E1Copy Handler: Coluna custom_css adicionada à tabela de afiliados');
    }

    // Adicionar a coluna card_text se não existir
    if (!in_array('card_text', $column_names)) {
        $wpdb->query("ALTER TABLE $table_name ADD COLUMN card_text text DEFAULT ''");
        error_log('E1Copy Handler: Coluna card_text adicionada à tabela de afiliados');
    }

    // Adicionar a coluna button_text se não existir
    if (!in_array('button_text', $column_names)) {
        $wpdb->query("ALTER TABLE $table_name ADD COLUMN button_text varchar(255) DEFAULT 'Comprar'");
        error_log('E1Copy Handler: Coluna button_text adicionada à tabela de afiliados');
    }

    // Adicionar a coluna template_id se não existir
    if (!in_array('template_id', $column_names)) {
        $wpdb->query("ALTER TABLE $table_name ADD COLUMN template_id varchar(50) DEFAULT 'model1'");
        error_log('E1Copy Handler: Coluna template_id adicionada à tabela de afiliados');
    }

    // Adicionar a coluna product_image se não existir
    if (!in_array('product_image', $column_names)) {
        $wpdb->query("ALTER TABLE $table_name ADD COLUMN product_image text DEFAULT ''");
        error_log('E1Copy Handler: Coluna product_image adicionada à tabela de afiliados');
    }

    // NÃO atualizar registros existentes para preservar configurações atuais
    // Apenas novos registros usarão o valor padrão "Comprar"
}

/**
 * Salva um novo afiliado na tabela
 */
function e1copy_ai_save_affiliate($data) {
    global $wpdb;
    $table_name = $wpdb->prefix . 'e1copy_affiliates';

    // Preparar dados para inserção
    $affiliate_data = [
        'title' => sanitize_text_field($data['title']),
        'affiliate_link' => esc_url_raw($data['affiliate_link']),
        'card_text' => isset($data['card_text']) ? sanitize_textarea_field($data['card_text']) : '',
        'button_text' => isset($data['button_text']) && !empty($data['button_text']) ? sanitize_text_field($data['button_text']) : 'Comprar',
        'product_image' => isset($data['product_image']) ? esc_url_raw($data['product_image']) : '',
        'template_id' => isset($data['template_id']) ? sanitize_text_field($data['template_id']) : 'model1',
        // Gerar conteúdo HTML com base no template e nos dados do formulário
        'content' => isset($data['content']) && !empty($data['content']) ? wp_unslash($data['content']) : e1copy_ai_generate_template_content($data),
        'custom_css' => isset($data['custom_css']) ? $data['custom_css'] : '',
    ];

    // Gerar shortcode se for um novo afiliado
    if (!isset($data['id'])) {
        $affiliate_data['shortcode'] = 'e1copy_affiliate_' . time() . '_' . wp_rand(1000, 9999);
    }

    // Log para depuração
    $affiliate_id = isset($data['id']) ? $data['id'] : 'novo';
    error_log('E1Copy Handler: Salvando afiliado ID ' . $affiliate_id);

    // Log dos dados para depuração
    error_log('E1Copy Handler: Dados do afiliado: ' . print_r($data, true));

    // Inserir ou atualizar
    if (isset($data['id']) && $data['id'] > 0) {
        // Log da consulta SQL para depuração
        $wpdb->show_errors();
        $result = $wpdb->update(
            $table_name,
            $affiliate_data,
            ['id' => $data['id']]
        );

        // Log do resultado da atualização
        if ($result === false) {
            error_log('E1Copy Handler: Erro ao atualizar afiliado ID ' . $data['id'] . ': ' . $wpdb->last_error);
            error_log('E1Copy Handler: Última consulta SQL: ' . $wpdb->last_query);
            return false;
        } else {
            error_log('E1Copy Handler: Afiliado ID ' . $data['id'] . ' atualizado com sucesso. Linhas afetadas: ' . $result);
            return $data['id'];
        }
    } else {
        // Log da consulta SQL para depuração
        $wpdb->show_errors();
        $result = $wpdb->insert($table_name, $affiliate_data);

        if ($result === false) {
            error_log('E1Copy Handler: Erro ao inserir novo afiliado: ' . $wpdb->last_error);
            error_log('E1Copy Handler: Última consulta SQL: ' . $wpdb->last_query);
            return false;
        } else {
            error_log('E1Copy Handler: Novo afiliado criado com ID ' . $wpdb->insert_id);
            return $wpdb->insert_id;
        }
    }
}

/**
 * Obtém um afiliado pelo ID
 */
function e1copy_ai_get_affiliate($id) {
    global $wpdb;
    $table_name = $wpdb->prefix . 'e1copy_affiliates';

    // Verificar se a coluna affiliate_link existe
    $columns = $wpdb->get_results("SHOW COLUMNS FROM $table_name");
    $column_names = [];
    foreach ($columns as $column) {
        $column_names[] = $column->Field;
    }

    // Adicionar a coluna affiliate_link se não existir
    if (!in_array('affiliate_link', $column_names)) {
        $wpdb->query("ALTER TABLE $table_name ADD COLUMN affiliate_link text DEFAULT ''");
        error_log('E1Copy Handler: Coluna affiliate_link adicionada à tabela de afiliados durante a consulta');
    }

    // Adicionar a coluna custom_css se não existir
    if (!in_array('custom_css', $column_names)) {
        $wpdb->query("ALTER TABLE $table_name ADD COLUMN custom_css text DEFAULT ''");
        error_log('E1Copy Handler: Coluna custom_css adicionada à tabela de afiliados durante a consulta');
    }

    $affiliate = $wpdb->get_row(
        $wpdb->prepare("SELECT * FROM $table_name WHERE id = %d", $id),
        ARRAY_A
    );

    // Log para depuração
    error_log('E1Copy Handler: Dados do afiliado carregados: ' . print_r($affiliate, true));

    // Garantir que os campos necessários existam no resultado
    if ($affiliate) {
        if (!isset($affiliate['affiliate_link'])) {
            $affiliate['affiliate_link'] = '';
        }
        if (!isset($affiliate['custom_css'])) {
            $affiliate['custom_css'] = '';
        }
    }

    return $affiliate;
}

/**
 * Obtém todos os afiliados
 */
function e1copy_ai_get_all_affiliates() {
    global $wpdb;
    $table_name = $wpdb->prefix . 'e1copy_affiliates';

    // Verificar se a coluna affiliate_link existe
    $columns = $wpdb->get_results("SHOW COLUMNS FROM $table_name");
    $column_names = [];
    foreach ($columns as $column) {
        $column_names[] = $column->Field;
    }

    // Adicionar a coluna affiliate_link se não existir
    if (!in_array('affiliate_link', $column_names)) {
        $wpdb->query("ALTER TABLE $table_name ADD COLUMN affiliate_link text DEFAULT ''");
        error_log('E1Copy Handler: Coluna affiliate_link adicionada à tabela de afiliados durante a listagem');
    }

    // Adicionar a coluna custom_css se não existir
    if (!in_array('custom_css', $column_names)) {
        $wpdb->query("ALTER TABLE $table_name ADD COLUMN custom_css text DEFAULT ''");
        error_log('E1Copy Handler: Coluna custom_css adicionada à tabela de afiliados durante a listagem');
    }

    $affiliates = $wpdb->get_results("SELECT * FROM $table_name ORDER BY created_at DESC", ARRAY_A);

    // Garantir que os campos necessários existam em todos os resultados
    foreach ($affiliates as &$affiliate) {
        if (!isset($affiliate['affiliate_link'])) {
            $affiliate['affiliate_link'] = '';
        }
        if (!isset($affiliate['custom_css'])) {
            $affiliate['custom_css'] = '';
        }
    }

    return $affiliates;
}

/**
 * Exclui um afiliado pelo ID
 */
function e1copy_ai_delete_affiliate($id) {
    global $wpdb;
    $table_name = $wpdb->prefix . 'e1copy_affiliates';

    return $wpdb->delete($table_name, ['id' => $id]);
}

/**
 * Limpa o conteúdo HTML para exibição (versão simplificada e segura)
 */
function e1copy_ai_clean_html_content($content) {
    // Verificar se o conteúdo é válido
    if (empty($content) || !is_string($content)) {
        return $content;
    }

    // Aplicar apenas limpezas básicas e seguras
    $content = stripslashes($content);
    $content = html_entity_decode($content, ENT_QUOTES, 'UTF-8');

    return $content;
}

/**
 * Processa o conteúdo HTML do afiliado para adicionar links
 */
function e1copy_ai_process_affiliate_content($content, $affiliate_link) {
    // Verificar se os parâmetros são válidos
    if (empty($content) || empty($affiliate_link)) {
        error_log('E1Copy Affiliate: Conteúdo ou link do afiliado vazio, retornando conteúdo original');
        return $content;
    }

    // Limpar o conteúdo HTML
    $content = e1copy_ai_clean_html_content($content);

    // Verificar se o conteúdo já tem os links adicionados
    if (strpos($content, 'style="text-decoration: none; color: inherit;"') !== false) {
        // O conteúdo já tem os links adicionados
        error_log('E1Copy Affiliate: Conteúdo já tem links adicionados');
        return $content;
    }

    // Abordagem alternativa usando manipulação de string com expressões regulares

    // Log do conteúdo original para depuração
    error_log('E1Copy Affiliate: Conteúdo original: ' . substr($content, 0, 200) . '...');

    // Padrão para encontrar a tag img
    $imgPattern = '/<img[^>]*src="([^"]*)"[^>]*>/i';

    // Padrão para encontrar o título (h5 com classe card-title)
    $titlePattern = '/<h5[^>]*class="card-title"[^>]*>(.*?)<\/h5>/is';

    // Padrão para encontrar o texto (p com classe card-text)
    $textPattern = '/<p[^>]*class="card-text"[^>]*>(.*?)<\/p>/is';

    // Substituir a tag img por uma tag img dentro de um link
    if (preg_match($imgPattern, $content, $imgMatches)) {
        $originalImg = $imgMatches[0];
        $linkedImg = '<a href="' . $affiliate_link . '" target="_blank" rel="nofollow" style="text-decoration: none; color: inherit;">' . $originalImg . '</a>';
        $content = str_replace($originalImg, $linkedImg, $content);
        error_log('E1Copy Affiliate: Imagem linkada adicionada');
    } else {
        error_log('E1Copy Affiliate: Não foi possível encontrar a tag img');
    }

    // Substituir o título por um título dentro de um link
    if (preg_match($titlePattern, $content, $titleMatches)) {
        $originalTitle = $titleMatches[0];
        $titleContent = $titleMatches[1];
        $linkedTitle = '<h5 class="card-title"><a href="' . $affiliate_link . '" target="_blank" rel="nofollow" style="text-decoration: none; color: inherit;">' . $titleContent . '</a></h5>';
        $content = str_replace($originalTitle, $linkedTitle, $content);
        error_log('E1Copy Affiliate: Título linkado adicionado');
    } else {
        error_log('E1Copy Affiliate: Não foi possível encontrar o título');
    }

    // Substituir o texto por um texto dentro de um link
    if (preg_match($textPattern, $content, $textMatches)) {
        $originalText = $textMatches[0];
        $textContent = $textMatches[1];
        $linkedText = '<p class="card-text"><a href="' . $affiliate_link . '" target="_blank" rel="nofollow" style="text-decoration: none; color: inherit;">' . $textContent . '</a></p>';
        $content = str_replace($originalText, $linkedText, $content);
        error_log('E1Copy Affiliate: Texto linkado adicionado');
    } else {
        error_log('E1Copy Affiliate: Não foi possível encontrar o texto');
    }

    // Log do conteúdo processado para depuração
    error_log('E1Copy Affiliate: Conteúdo processado: ' . substr($content, 0, 200) . '...');

    return $content;
}

/**
 * Renderiza o conteúdo do shortcode do afiliado
 */
function e1copy_ai_affiliate_shortcode($atts) {
    $atts = shortcode_atts(array(
        'id' => 0,
    ), $atts, 'e1copy_affiliate');

    $id = intval($atts['id']);
    if ($id <= 0) {
        return '';
    }

    $affiliate = e1copy_ai_get_affiliate($id);
    if (!$affiliate) {
        return '';
    }

    // Carregar os estilos necessários para o shortcode
    wp_enqueue_style('e1copy-bootstrap');
    wp_enqueue_style('e1copy-font-awesome');
    wp_enqueue_style('e1copy-bootstrap-icons');
    wp_enqueue_style('e1copy-affiliate-custom-css');

    // Carregar os scripts necessários
    wp_enqueue_script('jquery');
    wp_enqueue_script('e1copy-popper-js');
    wp_enqueue_script('e1copy-bootstrap-js');

    // Obter o link do afiliado
    $affiliate_link = isset($affiliate['affiliate_link']) ? $affiliate['affiliate_link'] : '';

    // Log para depuração
    error_log('E1Copy Affiliate: Processando shortcode para afiliado ID ' . $id . ' com link ' . $affiliate_link);

    // Verificar se o conteúdo já tem os links adicionados
    $content_has_links = strpos($affiliate['content'], 'style="text-decoration: none; color: inherit;"') !== false;

    // Usar sempre o conteúdo original sem processamento adicional para evitar problemas
    $content = $affiliate['content'];

    // Log para depuração
    error_log('E1Copy Affiliate: Conteúdo processado com sucesso');

    return '<div class="e1copy-affiliate">' . $content . '</div>';
}

// Registrar hook de ativação para criar a tabela
add_action('init', 'e1copy_ai_create_affiliates_table');



// Registrar shortcode
add_shortcode('e1copy_affiliate', 'e1copy_ai_affiliate_shortcode');



/**
 * Registra os estilos necessários para os afiliados
 */
function e1copy_ai_register_affiliate_styles() {
    // Registrar um arquivo CSS vazio que servirá como gancho para adicionar estilos inline
    wp_register_style('e1copy-affiliate-styles', false);
    wp_enqueue_style('e1copy-affiliate-styles');

    // Obter todos os afiliados com CSS personalizado
    global $wpdb;
    $table_name = $wpdb->prefix . 'e1copy_affiliates';
    $affiliates_with_css = $wpdb->get_results("SELECT id, custom_css FROM $table_name WHERE custom_css != '' AND custom_css IS NOT NULL", ARRAY_A);

    if (!empty($affiliates_with_css)) {
        $all_css = '';
        foreach ($affiliates_with_css as $affiliate) {
            $all_css .= "/* Affiliate ID: {$affiliate['id']} */\n";
            $all_css .= $affiliate['custom_css'] . "\n\n";
        }

        if (!empty($all_css)) {
            wp_add_inline_style('e1copy-affiliate-styles', $all_css);
        }
    }
}

// Registrar os estilos no frontend
add_action('wp_enqueue_scripts', 'e1copy_ai_register_affiliate_styles');

/**
 * Adiciona suporte para o Elementor
 */
function e1copy_ai_elementor_support() {
    // Verificar se o Elementor está ativo
    if (did_action('elementor/loaded')) {
        // Adicionar suporte para o editor do Elementor
        add_action('elementor/preview/enqueue_styles', 'e1copy_ai_register_affiliate_styles');

        // Adicionar suporte para o frontend do Elementor
        add_action('elementor/frontend/after_enqueue_styles', 'e1copy_ai_register_affiliate_styles');

        // Adicionar CSS diretamente no editor do Elementor
        add_action('elementor/editor/after_enqueue_styles', function() {
            global $wpdb;
            $table_name = $wpdb->prefix . 'e1copy_affiliates';
            $affiliates_with_css = $wpdb->get_results("SELECT id, custom_css FROM $table_name WHERE custom_css != '' AND custom_css IS NOT NULL", ARRAY_A);

            if (!empty($affiliates_with_css)) {
                echo '<style id="e1copy-affiliate-elementor-editor-styles">';
                foreach ($affiliates_with_css as $affiliate) {
                    echo "/* Affiliate ID: {$affiliate['id']} */\n";
                    echo $affiliate['custom_css'] . "\n\n";
                }
                echo '</style>';
            }
        });
    }
}
add_action('init', 'e1copy_ai_elementor_support');

/**
 * Gera o conteúdo HTML com base no template selecionado e nos dados do formulário
 */
function e1copy_ai_generate_template_content($data) {
    $template_id = isset($data['template_id']) ? sanitize_text_field($data['template_id']) : 'model1';
    $title = isset($data['title']) ? sanitize_text_field($data['title']) : '';
    $affiliate_link = isset($data['affiliate_link']) ? esc_url_raw($data['affiliate_link']) : '';
    $card_text = isset($data['card_text']) ? sanitize_textarea_field($data['card_text']) : '';
    $button_text = isset($data['button_text']) ? sanitize_text_field($data['button_text']) : 'Comprar';
    $product_image = isset($data['product_image']) && !empty($data['product_image']) ? esc_url($data['product_image']) : 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w0NzEyNjZ8MHwxfHNlYXJjaHwxfHxzaG9lfGVufDB8MHx8fDE3MjEwNDEzNjd8MA&ixlib=rb-4.0.3&q=80&w=1080';

    // Não precisamos mais incluir os recursos CSS e JavaScript diretamente no template
    // pois eles serão carregados via wp_enqueue_style e wp_enqueue_script
    $resources = '';

    // Template padrão (Modelo 1)
    $html = $resources . '<div class="card" style="max-width: 320px">
    <a href="' . $affiliate_link . '" target="_blank" rel="nofollow" style="text-decoration: none; color: inherit;">
        <img src="' . $product_image . '" class="card-img-top" alt="' . esc_attr($title) . '" style="width: 100%; height: auto; object-fit: cover; max-height: 200px;">
    </a>
    <div class="card-body">
        <a href="' . $affiliate_link . '" target="_blank" rel="nofollow" style="text-decoration: none; color: inherit;">
            <h5 class="card-title">' . $title . '</h5>
            <p class="card-text">' . $card_text . '</p>
        </a>
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <i class="fas fa-star text-warning"></i>
                <i class="fas fa-star text-warning"></i>
                <i class="fas fa-star text-warning"></i>
                <i class="fas fa-star text-warning"></i>
                <i class="fas fa-star-half-alt text-warning"></i>
                <small class="text-muted">(4.5)</small>
            </div>
        </div>
    </div>
    <div class="card-footer d-flex justify-content-between bg-light">
        <a href="' . $affiliate_link . '" class="btn btn-primary btn-sm" target="_blank" rel="nofollow">' . $button_text . '</a>
        <button class="btn btn-outline-secondary btn-sm"><i class="far fa-heart"></i></button>
    </div>
</div>';

    // Adicionar mais templates conforme necessário
    // if ($template_id === 'model2') { ... }

    // Retornar o HTML diretamente sem processamento adicional para evitar problemas
    return $html;
}

/**
 * Retorna a lista de templates disponíveis
 */
function e1copy_ai_get_templates() {
    return [
        'model1' => [
            'name' => 'Card Básico',
            'description' => 'Card com imagem, título, texto e botão',
            'preview' => plugins_url('/assets/images/template-model1.jpg', dirname(__FILE__)),
            'html' => '<div class="card" style="max-width: 320px">
    <a href="{{Link_Afiliado}}" target="_blank" rel="nofollow" style="text-decoration: none; color: inherit;">
        <img src="{{Imagem_do_Produto}}" class="card-img-top" alt="Product Image" style="width: 100%; height: auto; object-fit: cover; max-height: 200px;">
    </a>
    <div class="card-body">
        <a href="{{Link_Afiliado}}" target="_blank" rel="nofollow" style="text-decoration: none; color: inherit;">
            <h5 class="card-title">{{Título_definido_no_formulario}}</h5>
            <p class="card-text">{{Texto_do_card}}</p>
        </a>
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <i class="fas fa-star text-warning"></i>
                <i class="fas fa-star text-warning"></i>
                <i class="fas fa-star text-warning"></i>
                <i class="fas fa-star text-warning"></i>
                <i class="fas fa-star-half-alt text-warning"></i>
                <small class="text-muted">(4.5)</small>
            </div>
        </div>
    </div>
    <div class="card-footer d-flex justify-content-between bg-light">
        <a href="{{Link_Afiliado}}" class="btn btn-primary btn-sm" target="_blank" rel="nofollow">{{Texto_do_botao}}</a>
        <button class="btn btn-outline-secondary btn-sm"><i class="far fa-heart"></i></button>
    </div>
</div>'
        ],
        // Adicionar mais templates conforme necessário
    ];
}
