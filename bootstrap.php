<?php
/**
 * Bootstrap da Aplicação
 * Sistema de Dashboard E1Copy AI
 */

// Definir constantes básicas
define('APP_ROOT', __DIR__);
define('APP_START_TIME', microtime(true));

// Carregar autoloader do Composer (se existir)
if (file_exists(__DIR__ . '/vendor/autoload.php')) {
    require_once __DIR__ . '/vendor/autoload.php';
}

// Carregar variáveis de ambiente
if (file_exists(__DIR__ . '/.env')) {
    $lines = file(__DIR__ . '/.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) continue;
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value);
    }
}

// Configurar timezone
date_default_timezone_set('America/Sao_Paulo');

// Configurar exibição de erros
$debug = $_ENV['APP_DEBUG'] ?? false;
if ($debug) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// Iniciar sessão
session_start();

// Carregar classes principais
require_once __DIR__ . '/core/Database.php';
require_once __DIR__ . '/core/Router.php';
require_once __DIR__ . '/core/Controller.php';
require_once __DIR__ . '/core/Model.php';
require_once __DIR__ . '/core/View.php';
require_once __DIR__ . '/core/Auth.php';
require_once __DIR__ . '/core/ApiResponse.php';

// Carregar middlewares
require_once __DIR__ . '/middleware/AuthMiddleware.php';
require_once __DIR__ . '/middleware/AdminMiddleware.php';

// Carregar controllers
require_once __DIR__ . '/controllers/AuthController.php';
require_once __DIR__ . '/controllers/ClientController.php';
require_once __DIR__ . '/controllers/ClientPostsController.php';
require_once __DIR__ . '/controllers/AdminController.php';
require_once __DIR__ . '/controllers/ApiController.php';
require_once __DIR__ . '/controllers/ApiPostsController.php';
require_once __DIR__ . '/controllers/ApiClientPostsController.php';
require_once __DIR__ . '/controllers/ReportsController.php';
require_once __DIR__ . '/controllers/ToolsController.php';

// Carregar services
require_once __DIR__ . '/services/SiteSyncService.php';

// Criar aliases para controllers (para compatibilidade com rotas)
if (class_exists('ReportsController') && !class_exists('Reports')) {
    class_alias('ReportsController', 'Reports');
}

if (class_exists('ToolsController') && !class_exists('Tools')) {
    class_alias('ToolsController', 'Tools');
}
if (class_exists('ClientPostsController') && !class_exists('ClientPosts')) {
    class_alias('ClientPostsController', 'ClientPosts');
}

if (class_exists('ApiPostsController') && !class_exists('ApiPosts')) {
    class_alias('ApiPostsController', 'ApiPosts');
}

if (class_exists('ApiClientPostsController') && !class_exists('ApiClientPosts')) {
    class_alias('ApiClientPostsController', 'ApiClientPosts');
}

// Função para carregar configurações
function config($key, $default = null) {
    static $configs = [];
    
    $parts = explode('.', $key);
    $file = array_shift($parts);
    
    if (!isset($configs[$file])) {
        $configFile = APP_ROOT . "/config/{$file}.php";
        if (file_exists($configFile)) {
            $configs[$file] = require $configFile;
        } else {
            return $default;
        }
    }
    
    $value = $configs[$file];
    foreach ($parts as $part) {
        if (is_array($value) && isset($value[$part])) {
            $value = $value[$part];
        } else {
            return $default;
        }
    }
    
    return $value;
}

// Função para gerar URLs
function url($path = '') {
    $baseUrl = rtrim(config('app.url'), '/');
    return $baseUrl . '/' . ltrim($path, '/');
}

// Função para redirecionamento
function redirect($url) {
    header('Location: ' . $url);
    exit;
}

// Função para escape de HTML
function e($value) {
    return htmlspecialchars($value, ENT_QUOTES, 'UTF-8');
}

// Função para debug
function dd($data) {
    echo '<pre>';
    var_dump($data);
    echo '</pre>';
    die();
}
