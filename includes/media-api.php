<?php
if (!defined('ABSPATH')) exit; // Evita acesso direto

/**
 * Registra os endpoints da API REST para manipulação de mídia
 */
function e1copy_register_media_api_endpoints() {
    // Namespace da API
    $namespace = 'e1copy-ai/v1';

    // Rota para upload de mídia
    register_rest_route($namespace, '/media/upload', [
        'methods' => 'POST',
        'callback' => 'e1copy_upload_media',
        'permission_callback' => 'e1copy_check_api_key',
    ]);

    // Rota para definir imagem destacada para um post
    register_rest_route($namespace, '/posts/(?P<id>[\d]+)/featured-image', [
        'methods' => 'POST',
        'callback' => 'e1copy_set_featured_image',
        'permission_callback' => 'e1copy_check_api_key',
        'args' => [
            'id' => [
                'validate_callback' => function($param) {
                    return is_numeric($param);
                }
            ],
        ],
    ]);

    // Rota para obter informações sobre a biblioteca de mídia
    register_rest_route($namespace, '/media', [
        'methods' => 'GET',
        'callback' => 'e1copy_get_media',
        'permission_callback' => 'e1copy_check_api_key',
    ]);

    // Rota para obter informações sobre um item específico da biblioteca de mídia
    register_rest_route($namespace, '/media/(?P<id>[\d]+)', [
        'methods' => 'GET',
        'callback' => 'e1copy_get_media_item',
        'permission_callback' => 'e1copy_check_api_key',
        'args' => [
            'id' => [
                'validate_callback' => function($param) {
                    return is_numeric($param);
                }
            ],
        ],
    ]);
}
add_action('rest_api_init', 'e1copy_register_media_api_endpoints');

/**
 * Endpoint para upload de mídia
 *
 * Aceita upload de imagem via:
 * 1. Arquivo binário (multipart/form-data)
 * 2. URL externa (application/json com campo 'url')
 * 3. Base64 (application/json com campo 'base64_data')
 */
function e1copy_upload_media($request) {
    e1copy_add_cors_headers();

    // Verificar se a licença está ativa (verificação em tempo real)
    if (!e1copy_ai_is_activated(true)) {
        return new WP_Error('license_invalid', 'Licença do E1Copy AI inválida ou suspensa', ['status' => 403]);
    }

    // A verificação de permissão já é feita pela função e1copy_check_api_key
    // que é usada como permission_callback no registro da rota
    // e define o usuário atual como administrador

    $params = $request->get_params();
    $headers = $request->get_headers();
    $content_type = $request->get_header('content-type');

    // Verificar o tipo de upload
    if (isset($_FILES['file'])) {
        // Upload via arquivo (multipart/form-data)
        $file = $_FILES['file'];
        $attachment_id = e1copy_handle_file_upload($file);
    } elseif (isset($params['url'])) {
        // Upload via URL externa
        $url = esc_url_raw($params['url']);
        $attachment_id = e1copy_handle_url_upload($url, $params['title'] ?? '');
    } elseif (isset($params['base64_data'])) {
        // Upload via dados Base64
        $base64_data = $params['base64_data'];
        $filename = sanitize_file_name($params['filename'] ?? 'image-' . uniqid() . '.jpg');
        $attachment_id = e1copy_handle_base64_upload($base64_data, $filename, $params['title'] ?? '');
    } else {
        return new WP_Error(
            'rest_missing_param',
            'Nenhum arquivo, URL ou dados Base64 fornecidos para upload.',
            ['status' => 400]
        );
    }

    if (is_wp_error($attachment_id)) {
        return $attachment_id;
    }

    // Obter informações sobre o anexo
    $attachment = get_post($attachment_id);
    $attachment_url = wp_get_attachment_url($attachment_id);
    $attachment_meta = wp_get_attachment_metadata($attachment_id);
    $attachment_sizes = [];

    // Obter URLs de diferentes tamanhos da imagem
    if (isset($attachment_meta['sizes'])) {
        foreach ($attachment_meta['sizes'] as $size => $size_data) {
            $attachment_sizes[$size] = wp_get_attachment_image_url($attachment_id, $size);
        }
    }

    $response = [
        'success' => true,
        'id' => $attachment_id,
        'title' => $attachment->post_title,
        'url' => $attachment_url,
        'sizes' => $attachment_sizes,
        'mime_type' => $attachment->post_mime_type,
        'alt' => get_post_meta($attachment_id, '_wp_attachment_image_alt', true),
    ];

    return rest_ensure_response($response);
}

/**
 * Processa o upload de um arquivo
 */
function e1copy_handle_file_upload($file) {
    require_once(ABSPATH . 'wp-admin/includes/file.php');
    require_once(ABSPATH . 'wp-admin/includes/image.php');
    require_once(ABSPATH . 'wp-admin/includes/media.php');

    // Verificar erros no upload
    if ($file['error'] !== UPLOAD_ERR_OK) {
        return new WP_Error(
            'upload_error',
            'Erro no upload do arquivo: ' . e1copy_get_upload_error_message($file['error']),
            ['status' => 400]
        );
    }

    // Verificar tipo de arquivo
    $filetype = wp_check_filetype(basename($file['name']), null);
    if (!$filetype['type'] || strpos($filetype['type'], 'image/') !== 0) {
        return new WP_Error(
            'invalid_file_type',
            'Tipo de arquivo inválido. Apenas imagens são permitidas.',
            ['status' => 400]
        );
    }

    // Processar o upload
    $attachment_id = media_handle_upload('file', 0);

    if (is_wp_error($attachment_id)) {
        return $attachment_id;
    }

    return $attachment_id;
}

/**
 * Processa o upload de uma imagem a partir de uma URL
 */
function e1copy_handle_url_upload($url, $title = '') {
    require_once(ABSPATH . 'wp-admin/includes/file.php');
    require_once(ABSPATH . 'wp-admin/includes/image.php');
    require_once(ABSPATH . 'wp-admin/includes/media.php');

    // Baixar a imagem
    $tmp = download_url($url);
    if (is_wp_error($tmp)) {
        return $tmp;
    }

    // Determinar o nome do arquivo a partir da URL
    $filename = basename(parse_url($url, PHP_URL_PATH));
    if (empty($filename)) {
        $filename = 'image-' . uniqid() . '.jpg';
    }

    // Verificar tipo de arquivo
    $filetype = wp_check_filetype($filename, null);
    if (!$filetype['type'] || strpos($filetype['type'], 'image/') !== 0) {
        @unlink($tmp);
        return new WP_Error(
            'invalid_file_type',
            'Tipo de arquivo inválido. Apenas imagens são permitidas.',
            ['status' => 400]
        );
    }

    // Preparar array de arquivo
    $file = [
        'name' => $filename,
        'type' => $filetype['type'],
        'tmp_name' => $tmp,
        'error' => 0,
        'size' => filesize($tmp),
    ];

    // Mover o arquivo para o diretório de uploads
    $upload = wp_handle_sideload($file, ['test_form' => false]);

    if (isset($upload['error'])) {
        @unlink($tmp);
        return new WP_Error(
            'upload_error',
            $upload['error'],
            ['status' => 400]
        );
    }

    // Criar o anexo
    $attachment = [
        'post_mime_type' => $upload['type'],
        'post_title' => !empty($title) ? $title : preg_replace('/\.[^.]+$/', '', $filename),
        'post_content' => '',
        'post_status' => 'inherit',
        'guid' => $upload['url'],
    ];

    $attachment_id = wp_insert_attachment($attachment, $upload['file']);

    if (is_wp_error($attachment_id)) {
        @unlink($upload['file']);
        return $attachment_id;
    }

    // Gerar metadados
    $attachment_data = wp_generate_attachment_metadata($attachment_id, $upload['file']);
    wp_update_attachment_metadata($attachment_id, $attachment_data);

    return $attachment_id;
}

/**
 * Processa o upload de uma imagem a partir de dados Base64
 */
function e1copy_handle_base64_upload($base64_data, $filename, $title = '') {
    require_once(ABSPATH . 'wp-admin/includes/file.php');
    require_once(ABSPATH . 'wp-admin/includes/image.php');
    require_once(ABSPATH . 'wp-admin/includes/media.php');

    // Remover cabeçalho de dados Base64 se presente
    if (strpos($base64_data, ';base64,') !== false) {
        list(, $base64_data) = explode(';base64,', $base64_data);
    }

    // Decodificar os dados
    $decoded_data = base64_decode($base64_data);
    if ($decoded_data === false) {
        return new WP_Error(
            'invalid_base64',
            'Dados Base64 inválidos.',
            ['status' => 400]
        );
    }

    // Obter diretório de upload
    $upload_dir = wp_upload_dir();
    if (isset($upload_dir['error']) && $upload_dir['error'] !== false) {
        return new WP_Error(
            'upload_dir_error',
            $upload_dir['error'],
            ['status' => 500]
        );
    }

    // Criar arquivo temporário
    $tmp_file = wp_tempnam($filename);
    file_put_contents($tmp_file, $decoded_data);

    // Verificar tipo de arquivo
    $filetype = wp_check_filetype($filename, null);
    if (!$filetype['type'] || strpos($filetype['type'], 'image/') !== 0) {
        @unlink($tmp_file);
        return new WP_Error(
            'invalid_file_type',
            'Tipo de arquivo inválido. Apenas imagens são permitidas.',
            ['status' => 400]
        );
    }

    // Preparar array de arquivo
    $file = [
        'name' => $filename,
        'type' => $filetype['type'],
        'tmp_name' => $tmp_file,
        'error' => 0,
        'size' => filesize($tmp_file),
    ];

    // Mover o arquivo para o diretório de uploads
    $upload = wp_handle_sideload($file, ['test_form' => false]);

    if (isset($upload['error'])) {
        @unlink($tmp_file);
        return new WP_Error(
            'upload_error',
            $upload['error'],
            ['status' => 400]
        );
    }

    // Criar o anexo
    $attachment = [
        'post_mime_type' => $upload['type'],
        'post_title' => !empty($title) ? $title : preg_replace('/\.[^.]+$/', '', $filename),
        'post_content' => '',
        'post_status' => 'inherit',
        'guid' => $upload['url'],
    ];

    $attachment_id = wp_insert_attachment($attachment, $upload['file']);

    if (is_wp_error($attachment_id)) {
        @unlink($upload['file']);
        return $attachment_id;
    }

    // Gerar metadados
    $attachment_data = wp_generate_attachment_metadata($attachment_id, $upload['file']);
    wp_update_attachment_metadata($attachment_id, $attachment_data);

    return $attachment_id;
}

/**
 * Retorna a mensagem de erro correspondente ao código de erro de upload
 */
function e1copy_get_upload_error_message($error_code) {
    switch ($error_code) {
        case UPLOAD_ERR_INI_SIZE:
            return 'O arquivo enviado excede o limite definido na diretiva upload_max_filesize do php.ini.';
        case UPLOAD_ERR_FORM_SIZE:
            return 'O arquivo enviado excede o limite definido no formulário HTML.';
        case UPLOAD_ERR_PARTIAL:
            return 'O arquivo foi apenas parcialmente carregado.';
        case UPLOAD_ERR_NO_FILE:
            return 'Nenhum arquivo foi enviado.';
        case UPLOAD_ERR_NO_TMP_DIR:
            return 'Falta uma pasta temporária.';
        case UPLOAD_ERR_CANT_WRITE:
            return 'Falha ao gravar o arquivo em disco.';
        case UPLOAD_ERR_EXTENSION:
            return 'Uma extensão PHP interrompeu o upload do arquivo.';
        default:
            return 'Erro desconhecido no upload.';
    }
}

/**
 * Endpoint para definir uma imagem destacada para um post
 */
function e1copy_set_featured_image($request) {
    e1copy_add_cors_headers();

    // Verificar se a licença está ativa (verificação em tempo real)
    if (!e1copy_ai_is_activated(true)) {
        return new WP_Error('license_invalid', 'Licença do E1Copy AI inválida ou suspensa', ['status' => 403]);
    }

    $post_id = (int) $request['id'];
    $params = $request->get_params();

    // Verificar se o post existe
    $post = get_post($post_id);
    if (!$post) {
        return new WP_Error(
            'post_not_found',
            'Post não encontrado.',
            ['status' => 404]
        );
    }

    // A verificação de permissão já é feita pela função e1copy_check_api_key
    // que é usada como permission_callback no registro da rota
    // e define o usuário atual como administrador

    // Verificar se foi fornecido um ID de anexo
    if (isset($params['attachment_id'])) {
        $attachment_id = (int) $params['attachment_id'];

        // Verificar se o anexo existe
        $attachment = get_post($attachment_id);
        if (!$attachment || $attachment->post_type !== 'attachment') {
            return new WP_Error(
                'invalid_attachment',
                'Anexo inválido ou não encontrado.',
                ['status' => 400]
            );
        }

        // Definir a imagem destacada
        $result = set_post_thumbnail($post_id, $attachment_id);

        if (!$result) {
            return new WP_Error(
                'featured_image_error',
                'Não foi possível definir a imagem destacada.',
                ['status' => 500]
            );
        }

        $response = [
            'success' => true,
            'message' => 'Imagem destacada definida com sucesso.',
            'post_id' => $post_id,
            'attachment_id' => $attachment_id,
            'thumbnail_url' => wp_get_attachment_url($attachment_id),
        ];

        return rest_ensure_response($response);
    } elseif (isset($params['url'])) {
        // Se foi fornecida uma URL, fazer upload da imagem primeiro
        $url = esc_url_raw($params['url']);
        $title = isset($params['title']) ? sanitize_text_field($params['title']) : '';

        $attachment_id = e1copy_handle_url_upload($url, $title);

        if (is_wp_error($attachment_id)) {
            return $attachment_id;
        }

        // Definir a imagem destacada
        $result = set_post_thumbnail($post_id, $attachment_id);

        if (!$result) {
            return new WP_Error(
                'featured_image_error',
                'Não foi possível definir a imagem destacada.',
                ['status' => 500]
            );
        }

        $response = [
            'success' => true,
            'message' => 'Imagem destacada definida com sucesso a partir da URL.',
            'post_id' => $post_id,
            'attachment_id' => $attachment_id,
            'thumbnail_url' => wp_get_attachment_url($attachment_id),
        ];

        return rest_ensure_response($response);
    } elseif (isset($params['base64_data'])) {
        // Se foram fornecidos dados Base64, fazer upload da imagem primeiro
        $base64_data = $params['base64_data'];
        $filename = sanitize_file_name($params['filename'] ?? 'featured-image-' . uniqid() . '.jpg');
        $title = isset($params['title']) ? sanitize_text_field($params['title']) : '';

        $attachment_id = e1copy_handle_base64_upload($base64_data, $filename, $title);

        if (is_wp_error($attachment_id)) {
            return $attachment_id;
        }

        // Definir a imagem destacada
        $result = set_post_thumbnail($post_id, $attachment_id);

        if (!$result) {
            return new WP_Error(
                'featured_image_error',
                'Não foi possível definir a imagem destacada.',
                ['status' => 500]
            );
        }

        $response = [
            'success' => true,
            'message' => 'Imagem destacada definida com sucesso a partir de dados Base64.',
            'post_id' => $post_id,
            'attachment_id' => $attachment_id,
            'thumbnail_url' => wp_get_attachment_url($attachment_id),
        ];

        return rest_ensure_response($response);
    } else {
        return new WP_Error(
            'missing_parameter',
            'É necessário fornecer attachment_id, url ou base64_data.',
            ['status' => 400]
        );
    }
}

/**
 * Endpoint para listar itens da biblioteca de mídia
 */
function e1copy_get_media($request) {
    e1copy_add_cors_headers();

    // Verificar se a licença está ativa (verificação em tempo real)
    if (!e1copy_ai_is_activated(true)) {
        return new WP_Error('license_invalid', 'Licença do E1Copy AI inválida ou suspensa', ['status' => 403]);
    }

    // Parâmetros de paginação
    $per_page = isset($request['per_page']) ? (int) $request['per_page'] : 10;
    $page = isset($request['page']) ? (int) $request['page'] : 1;

    // Parâmetros de filtro
    $search = isset($request['search']) ? sanitize_text_field($request['search']) : '';
    $mime_type = isset($request['mime_type']) ? sanitize_text_field($request['mime_type']) : '';

    // Construir argumentos da consulta
    $args = [
        'post_type' => 'attachment',
        'post_status' => 'inherit',
        'posts_per_page' => $per_page,
        'paged' => $page,
        'orderby' => 'date',
        'order' => 'DESC',
    ];

    // Adicionar filtro de pesquisa
    if (!empty($search)) {
        $args['s'] = $search;
    }

    // Adicionar filtro de tipo MIME
    if (!empty($mime_type)) {
        $args['post_mime_type'] = $mime_type;
    }

    // Executar a consulta
    $query = new WP_Query($args);

    // Preparar os resultados
    $items = [];
    foreach ($query->posts as $post) {
        $attachment_url = wp_get_attachment_url($post->ID);
        $attachment_meta = wp_get_attachment_metadata($post->ID);
        $attachment_sizes = [];

        // Obter URLs de diferentes tamanhos da imagem
        if (isset($attachment_meta['sizes'])) {
            foreach ($attachment_meta['sizes'] as $size => $size_data) {
                $attachment_sizes[$size] = wp_get_attachment_image_url($post->ID, $size);
            }
        }

        $items[] = [
            'id' => $post->ID,
            'title' => $post->post_title,
            'url' => $attachment_url,
            'sizes' => $attachment_sizes,
            'mime_type' => $post->post_mime_type,
            'alt' => get_post_meta($post->ID, '_wp_attachment_image_alt', true),
            'date' => $post->post_date,
        ];
    }

    $response = [
        'success' => true,
        'items' => $items,
        'total' => $query->found_posts,
        'pages' => $query->max_num_pages,
        'page' => $page,
        'per_page' => $per_page,
    ];

    return rest_ensure_response($response);
}

/**
 * Endpoint para obter informações sobre um item específico da biblioteca de mídia
 */
function e1copy_get_media_item($request) {
    e1copy_add_cors_headers();

    // Verificar se a licença está ativa
    if (!e1copy_ai_is_activated()) {
        return new WP_Error('license_invalid', 'Licença do E1Copy AI inválida ou suspensa', ['status' => 403]);
    }

    $attachment_id = (int) $request['id'];

    // Verificar se o anexo existe
    $attachment = get_post($attachment_id);
    if (!$attachment || $attachment->post_type !== 'attachment') {
        return new WP_Error(
            'invalid_attachment',
            'Anexo inválido ou não encontrado.',
            ['status' => 404]
        );
    }

    // Obter informações sobre o anexo
    $attachment_url = wp_get_attachment_url($attachment_id);
    $attachment_meta = wp_get_attachment_metadata($attachment_id);
    $attachment_sizes = [];

    // Obter URLs de diferentes tamanhos da imagem
    if (isset($attachment_meta['sizes'])) {
        foreach ($attachment_meta['sizes'] as $size => $size_data) {
            $attachment_sizes[$size] = wp_get_attachment_image_url($attachment_id, $size);
        }
    }

    $response = [
        'success' => true,
        'id' => $attachment_id,
        'title' => $attachment->post_title,
        'description' => $attachment->post_content,
        'caption' => $attachment->post_excerpt,
        'url' => $attachment_url,
        'sizes' => $attachment_sizes,
        'mime_type' => $attachment->post_mime_type,
        'alt' => get_post_meta($attachment_id, '_wp_attachment_image_alt', true),
        'date' => $attachment->post_date,
        'modified' => $attachment->post_modified,
        'author' => (int) $attachment->post_author,
        'meta' => $attachment_meta,
    ];

    return rest_ensure_response($response);
}
