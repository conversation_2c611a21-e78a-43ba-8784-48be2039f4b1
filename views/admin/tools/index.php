<?php
$pageTitle = 'Ferramentas de Debug';
ob_start();
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">🔧 Ferramentas de Debug</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item"><a href="<?= url('/admin') ?>">Dashboard</a></li>
                        <li class="breadcrumb-item active">Ferramentas</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Teste de Conexão com Plugin -->
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-plug me-2"></i>
                        Teste de Conexão
                    </h5>
                </div>
                <div class="card-body">
                    <p class="card-text">Testar conexão com plugin WordPress e validar endpoints da API.</p>
                    <form id="test-connection-form">
                        <div class="mb-3">
                            <label for="site_url" class="form-label">URL do Site</label>
                            <input type="url" class="form-control" id="site_url" name="site_url" 
                                   placeholder="https://exemplo.com" required>
                        </div>
                        <div class="mb-3">
                            <label for="api_key" class="form-label">Chave de API</label>
                            <input type="text" class="form-control" id="api_key" name="api_key" 
                                   placeholder="e1copy_..." required>
                        </div>
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-play me-1"></i>
                            Testar Conexão
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Validar Chave de API -->
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-key me-2"></i>
                        Validar Chave
                    </h5>
                </div>
                <div class="card-body">
                    <p class="card-text">Verificar se uma chave de API existe no sistema e obter informações.</p>
                    <form id="validate-key-form">
                        <div class="mb-3">
                            <label for="validate_api_key" class="form-label">Chave de API</label>
                            <input type="text" class="form-control" id="validate_api_key" name="api_key" 
                                   placeholder="e1copy_..." required>
                        </div>
                        <button type="submit" class="btn btn-success w-100">
                            <i class="fas fa-search me-1"></i>
                            Validar Chave
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Debug de Site -->
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header bg-warning text-dark">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bug me-2"></i>
                        Debug de Site
                    </h5>
                </div>
                <div class="card-body">
                    <p class="card-text">Debug completo de um site específico com logs e sincronização.</p>
                    <form id="debug-site-form">
                        <div class="mb-3">
                            <label for="site_id" class="form-label">ID do Site</label>
                            <input type="number" class="form-control" id="site_id" name="site_id" 
                                   placeholder="1" required>
                        </div>
                        <button type="submit" class="btn btn-warning w-100">
                            <i class="fas fa-search-plus me-1"></i>
                            Debug Site
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Teste de Endpoints -->
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-network-wired me-2"></i>
                        Teste de Endpoints
                    </h5>
                </div>
                <div class="card-body">
                    <p class="card-text">Testar todos os endpoints disponíveis de um site WordPress.</p>
                    <form id="test-endpoints-form">
                        <div class="mb-3">
                            <label for="endpoints_site_url" class="form-label">URL do Site</label>
                            <input type="url" class="form-control" id="endpoints_site_url" name="site_url" 
                                   placeholder="https://exemplo.com" required>
                        </div>
                        <button type="submit" class="btn btn-info w-100">
                            <i class="fas fa-globe me-1"></i>
                            Testar Endpoints
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Verificar Banco de Dados -->
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header bg-secondary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-database me-2"></i>
                        Verificar BD
                    </h5>
                </div>
                <div class="card-body">
                    <p class="card-text">Verificar integridade do banco de dados e estrutura das tabelas.</p>
                    <button type="button" class="btn btn-secondary w-100" onclick="checkDatabase()">
                        <i class="fas fa-check-circle me-1"></i>
                        Verificar Banco
                    </button>
                </div>
            </div>
        </div>

        <!-- Teste de Sincronização -->
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header bg-danger text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-sync me-2"></i>
                        Teste Sincronização
                    </h5>
                </div>
                <div class="card-body">
                    <p class="card-text">Testar sincronização completa de um site específico com logs detalhados.</p>
                    <form id="test-sync-form">
                        <div class="mb-3">
                            <label for="sync_site_id" class="form-label">ID do Site</label>
                            <input type="number" class="form-control" id="sync_site_id" name="site_id"
                                   placeholder="1" required>
                        </div>
                        <button type="submit" class="btn btn-danger w-100">
                            <i class="fas fa-sync-alt me-1"></i>
                            Testar Sincronização
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Teste Posts WordPress -->
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header bg-purple text-white" style="background-color: #6f42c1 !important;">
                    <h5 class="card-title mb-0">
                        <i class="fab fa-wordpress me-2"></i>
                        Posts WordPress
                    </h5>
                </div>
                <div class="card-body">
                    <p class="card-text">Testar acesso aos posts do WordPress via API nativa e plugin.</p>
                    <form id="test-wp-posts-form">
                        <div class="mb-3">
                            <label for="wp_site_url" class="form-label">URL do Site</label>
                            <input type="url" class="form-control" id="wp_site_url" name="site_url"
                                   placeholder="https://exemplo.com" required>
                        </div>
                        <div class="mb-3">
                            <label for="wp_api_key" class="form-label">Chave de API</label>
                            <input type="text" class="form-control" id="wp_api_key" name="api_key"
                                   placeholder="e1copy_..." required>
                        </div>
                        <button type="submit" class="btn w-100" style="background-color: #6f42c1; color: white;">
                            <i class="fab fa-wordpress me-1"></i>
                            Testar Posts WP
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Informações do Sistema -->
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header bg-dark text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-server me-2"></i>
                        Info do Sistema
                    </h5>
                </div>
                <div class="card-body">
                    <p class="card-text">Informações sobre o servidor, PHP, banco de dados e recursos.</p>
                    <button type="button" class="btn btn-dark w-100" onclick="getSystemInfo()">
                        <i class="fas fa-info-circle me-1"></i>
                        Ver Informações
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal para Resultados -->
    <div class="modal fade" id="resultsModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="resultsModalTitle">Resultados</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="resultsContent">
                        <!-- Conteúdo será inserido aqui -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
                    <button type="button" class="btn btn-primary" onclick="copyResults()">
                        <i class="fas fa-copy me-1"></i>
                        Copiar Resultados
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Função para mostrar loading
function showLoading(button) {
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Processando...';
    button.disabled = true;
    return originalText;
}

// Função para restaurar botão
function restoreButton(button, originalText) {
    button.innerHTML = originalText;
    button.disabled = false;
}

// Função para mostrar resultados
function showResults(title, data) {
    document.getElementById('resultsModalTitle').textContent = title;
    document.getElementById('resultsContent').innerHTML = formatResults(data);
    new bootstrap.Modal(document.getElementById('resultsModal')).show();
}

// Função para formatar resultados
function formatResults(data) {
    if (typeof data === 'object') {
        return '<pre class="bg-light p-3 rounded">' + JSON.stringify(data, null, 2) + '</pre>';
    }
    return '<div class="alert alert-info">' + data + '</div>';
}

// Função para copiar resultados
function copyResults() {
    const content = document.getElementById('resultsContent').textContent;
    navigator.clipboard.writeText(content).then(() => {
        alert('Resultados copiados para a área de transferência!');
    });
}

// Teste de Conexão
document.getElementById('test-connection-form').addEventListener('submit', function(e) {
    e.preventDefault();
    const button = e.target.querySelector('button[type="submit"]');
    const originalText = showLoading(button);
    
    const formData = new FormData(e.target);
    
    fetch('<?= url('/api/tools/test-plugin-connection') ?>', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        showResults('Teste de Conexão', data);
        restoreButton(button, originalText);
    })
    .catch(error => {
        alert('Erro: ' + error.message);
        restoreButton(button, originalText);
    });
});

// Validar Chave
document.getElementById('validate-key-form').addEventListener('submit', function(e) {
    e.preventDefault();
    const button = e.target.querySelector('button[type="submit"]');
    const originalText = showLoading(button);
    
    const formData = new FormData(e.target);
    
    fetch('<?= url('/api/tools/validate-api-key') ?>', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        showResults('Validação de Chave', data);
        restoreButton(button, originalText);
    })
    .catch(error => {
        alert('Erro: ' + error.message);
        restoreButton(button, originalText);
    });
});

// Debug de Site
document.getElementById('debug-site-form').addEventListener('submit', function(e) {
    e.preventDefault();
    const button = e.target.querySelector('button[type="submit"]');
    const originalText = showLoading(button);
    
    const formData = new FormData(e.target);
    
    fetch('<?= url('/api/tools/debug-site') ?>', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        showResults('Debug de Site', data);
        restoreButton(button, originalText);
    })
    .catch(error => {
        alert('Erro: ' + error.message);
        restoreButton(button, originalText);
    });
});

// Teste de Endpoints
document.getElementById('test-endpoints-form').addEventListener('submit', function(e) {
    e.preventDefault();
    const button = e.target.querySelector('button[type="submit"]');
    const originalText = showLoading(button);
    
    const formData = new FormData(e.target);
    
    fetch('<?= url('/api/tools/test-endpoints') ?>', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        showResults('Teste de Endpoints', data);
        restoreButton(button, originalText);
    })
    .catch(error => {
        alert('Erro: ' + error.message);
        restoreButton(button, originalText);
    });
});

// Verificar Banco de Dados
function checkDatabase() {
    const button = event.target;
    const originalText = showLoading(button);
    
    fetch('<?= url('/api/tools/check-database') ?>', {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        showResults('Verificação do Banco', data);
        restoreButton(button, originalText);
    })
    .catch(error => {
        alert('Erro: ' + error.message);
        restoreButton(button, originalText);
    });
}

// Teste de Sincronização
document.getElementById('test-sync-form').addEventListener('submit', function(e) {
    e.preventDefault();
    const button = e.target.querySelector('button[type="submit"]');
    const originalText = showLoading(button);

    const formData = new FormData(e.target);

    fetch('<?= url('/api/tools/test-sync') ?>', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        showResults('Teste de Sincronização', data);
        restoreButton(button, originalText);
    })
    .catch(error => {
        alert('Erro: ' + error.message);
        restoreButton(button, originalText);
    });
});

// Teste Posts WordPress
document.getElementById('test-wp-posts-form').addEventListener('submit', function(e) {
    e.preventDefault();
    const button = e.target.querySelector('button[type="submit"]');
    const originalText = showLoading(button);

    const formData = new FormData(e.target);

    fetch('<?= url('/api/tools/test-wp-posts') ?>', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        showResults('Teste Posts WordPress', data);
        restoreButton(button, originalText);
    })
    .catch(error => {
        alert('Erro: ' + error.message);
        restoreButton(button, originalText);
    });
});

// Informações do Sistema
function getSystemInfo() {
    const button = event.target;
    const originalText = showLoading(button);

    fetch('<?= url('/api/tools/system-info') ?>', {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        showResults('Informações do Sistema', data);
        restoreButton(button, originalText);
    })
    .catch(error => {
        alert('Erro: ' + error.message);
        restoreButton(button, originalText);
    });
}
</script>

<?php
$content = ob_get_clean();
include_once __DIR__ . '/../../layouts/app.php';
?>


