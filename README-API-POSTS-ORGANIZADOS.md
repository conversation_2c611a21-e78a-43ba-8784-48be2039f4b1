# API de Posts Organizados - E1Copy AI

## Problema Resolvido

Anteriormente, a API `/api/v1/pending-posts` retornava todos os posts misturados em um único array, dificultando a separação por cliente/site no N8N e na interface administrativa.

## Solução Implementada

A API foi reestruturada para organizar os posts de forma hierárquica, separando por cliente e por site, mantendo compatibilidade com o código existente.

## Novos Endpoints

### 1. `/api/v1/pending-posts` (Atualizado)
**Método:** GET  
**Descrição:** Retorna estrutura completa com posts organizados + dados de compatibilidade

**Resposta:**
```json
{
  "success": true,
  "data": {
    "summary": {
      "total_sites": 3,
      "total_posts": 5,
      "active_sites": 2,
      "active_clients": 2
    },
    "posts_by_client": [
      {
        "client_info": {
          "user_name": "E1Cursos",
          "user_email": "<EMAIL>"
        },
        "sites": [
          {
            "site_id": 1,
            "site_name": "E1Cursos",
            "site_url": "https://e1cursos.com",
            "posts_count": 2,
            "status": "success"
          }
        ],
        "total_posts": 2,
        "posts": [
          {
            "id": 1,
            "title": "Produto A",
            "post_type": "product",
            "created_at": "2025-06-20 11:08:19",
            "site_info": {
              "site_id": 1,
              "site_name": "E1Cursos",
              "site_url": "https://e1cursos.com",
              "user_name": "E1Cursos",
              "user_email": "<EMAIL>"
            }
          }
        ]
      }
    ],
    "posts_by_site": [
      {
        "site_info": {
          "site_id": 1,
          "site_name": "E1Cursos",
          "site_url": "https://e1cursos.com",
          "user_name": "E1Cursos",
          "user_email": "<EMAIL>"
        },
        "posts": [...],
        "total_posts": 2,
        "status": "success"
      }
    ],
    "all_posts": [...], // Compatibilidade
    "sites_stats": [...], // Compatibilidade
    "timestamp": "2025-06-20 11:38:00"
  }
}
```

### 2. `/api/v1/pending-posts/by-client` (Novo)
**Método:** GET  
**Descrição:** Retorna apenas posts organizados por cliente (ideal para N8N)

**Resposta:**
```json
{
  "success": true,
  "data": {
    "clients": [
      {
        "client_info": {
          "user_name": "E1Cursos",
          "user_email": "<EMAIL>"
        },
        "sites": [...],
        "total_posts": 2,
        "posts": [...]
      }
    ],
    "summary": {
      "total_sites": 3,
      "total_posts": 5,
      "active_sites": 2,
      "active_clients": 2
    },
    "timestamp": "2025-06-20 11:38:00"
  }
}
```

### 3. `/api/v1/pending-posts/by-site` (Novo)
**Método:** GET  
**Descrição:** Retorna apenas posts organizados por site (ideal para N8N)

**Resposta:**
```json
{
  "success": true,
  "data": {
    "sites": [
      {
        "site_info": {
          "site_id": 1,
          "site_name": "E1Cursos",
          "site_url": "https://e1cursos.com",
          "user_name": "E1Cursos",
          "user_email": "<EMAIL>"
        },
        "posts": [...],
        "total_posts": 2,
        "status": "success"
      }
    ],
    "summary": {...},
    "timestamp": "2025-06-20 11:38:00"
  }
}
```

## Uso no N8N

### Para processar por cliente:
1. Use o endpoint `/api/v1/pending-posts/by-client`
2. Itere sobre `data.clients`
3. Para cada cliente, acesse `client_info` e `posts`

### Para processar por site:
1. Use o endpoint `/api/v1/pending-posts/by-site`
2. Itere sobre `data.sites`
3. Para cada site, acesse `site_info` e `posts`

## Vantagens

1. **Separação Clara:** Posts agora são organizados por cliente/site
2. **Compatibilidade:** Mantém estrutura antiga para não quebrar código existente
3. **Flexibilidade:** 3 endpoints para diferentes necessidades
4. **Performance:** N8N pode usar endpoints específicos sem dados desnecessários
5. **Manutenibilidade:** Código mais organizado e fácil de entender

## Arquivos Modificados

- `app/controllers/ApiController.php` - Novos métodos e organização de dados
- `app/controllers/ReportsController.php` - Atualizado para nova estrutura
- `app/views/admin/reports/posts-clients.php` - Interface atualizada
- `app/routes/api.php` - Novas rotas adicionadas

## Teste

Execute `php test-new-api.php` para ver a demonstração da nova estrutura.
