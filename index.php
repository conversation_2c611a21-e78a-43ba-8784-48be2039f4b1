<?php
/**
 * Ponto de Entrada da Aplicação
 * Sistema de Dashboard E1Copy AI
 */

// Handle CORS preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With, X-E1Copy-API-Key');
    header('Access-Control-Max-Age: 86400');
    http_response_code(200);
    exit;
}

// Carregar bootstrap
require_once __DIR__ . '/bootstrap.php';

// Inicializar roteador
$router = new Router();

// Carregar rotas
require_once __DIR__ . '/routes/web.php';
require_once __DIR__ . '/routes/api.php';

// Executar roteamento
try {
    $router->dispatch();
} catch (Exception $e) {
    if (config('app.debug')) {
        echo '<h1>Erro:</h1>';
        echo '<p>' . $e->getMessage() . '</p>';
        echo '<pre>' . $e->getTraceAsString() . '</pre>';
    } else {
        http_response_code(500);
        echo 'Erro interno do servidor';
    }
}
