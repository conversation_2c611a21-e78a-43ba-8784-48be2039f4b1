<?php
require_once __DIR__ . '/config/database.php';

try {
    $db = new Database();
    
    // Verificar estrutura da tabela
    $columns = $db->fetchAll("DESCRIBE client_posts");
    
    echo "=== ESTRUTURA DA TABELA client_posts ===\n";
    foreach ($columns as $column) {
        echo sprintf("%-20s %-15s %-10s %-10s\n", 
            $column['Field'], 
            $column['Type'], 
            $column['Null'], 
            $column['Default']
        );
    }
    
    echo "\n=== VERIFICANDO CAMPOS ESPECÍFICOS ===\n";
    $fieldsToCheck = ['product_images', 'post_cover'];
    
    foreach ($fieldsToCheck as $field) {
        $exists = false;
        foreach ($columns as $column) {
            if ($column['Field'] === $field) {
                $exists = true;
                echo "✓ Campo '$field' existe: {$column['Type']}\n";
                break;
            }
        }
        if (!$exists) {
            echo "✗ Campo '$field' NÃO existe\n";
        }
    }
    
    // Verificar dados de um post específico
    echo "\n=== DADOS DO POST ID 6 ===\n";
    $post = $db->fetch("SELECT id, product_images, post_cover FROM client_posts WHERE id = 6");
    if ($post) {
        echo "ID: {$post['id']}\n";
        echo "product_images: " . ($post['product_images'] ?? 'NULL') . "\n";
        echo "post_cover: " . ($post['post_cover'] ?? 'NULL') . "\n";
    } else {
        echo "Post não encontrado\n";
    }
    
} catch (Exception $e) {
    echo "Erro: " . $e->getMessage() . "\n";
}
?>
