<?php
$title = 'Configurações - E1Copy AI';
$pageTitle = 'Configurações do Sistema';
ob_start();
?>

<form method="POST" action="<?= url('/admin/settings') ?>">
    <input type="hidden" name="_token" value="<?= $csrf_token ?>">
    
    <!-- Configurações da API -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-cog me-2"></i>
                Configurações da API
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="groq_api_key" class="form-label">
                        <i class="fas fa-robot me-1"></i>
                        Chave da API Groq
                    </label>
                    <input type="password" class="form-control" id="groq_api_key" 
                           name="settings[groq_api_key]" 
                           value="<?= e($settingsGrouped['groq'][0]['value'] ?? '') ?>"
                           placeholder="gsk_...">
                    <div class="form-text">
                        Chave para geração de conteúdo com IA. 
                        <a href="https://console.groq.com/" target="_blank">Obter chave</a>
                    </div>
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="apiflash_key" class="form-label">
                        <i class="fas fa-camera me-1"></i>
                        Chave da API ApiFlash
                    </label>
                    <input type="password" class="form-control" id="apiflash_key" 
                           name="settings[apiflash_key]" 
                           value="<?= e($settingsGrouped['apiflash'][0]['value'] ?? '') ?>"
                           placeholder="access_key...">
                    <div class="form-text">
                        Chave para captura de screenshots. 
                        <a href="https://apiflash.com/" target="_blank">Obter chave</a>
                    </div>
                </div>
            </div>
            
            <div class="mb-3">
                <label for="default_prompt_instruction" class="form-label">
                    <i class="fas fa-comment-dots me-1"></i>
                    Instrução Padrão para Prompts
                </label>
                <textarea class="form-control" id="default_prompt_instruction" 
                          name="settings[default_prompt_instruction]" rows="3"
                          placeholder="escreva um artigo de três mil palavras otimizado para SEO"><?= e($settingsGrouped['default'][0]['value'] ?? '') ?></textarea>
                <div class="form-text">
                    Esta instrução será adicionada automaticamente a todos os prompts enviados para a IA.
                </div>
            </div>


        </div>
    </div>
    
    <!-- Configurações do Sistema -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-server me-2"></i>
                Configurações do Sistema
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="app_name" class="form-label">
                        <i class="fas fa-tag me-1"></i>
                        Nome da Aplicação
                    </label>
                    <input type="text" class="form-control" id="app_name" 
                           name="settings[app_name]" 
                           value="<?= e($settingsGrouped['app'][0]['value'] ?? 'E1Copy AI Dashboard') ?>">
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="max_api_requests_per_hour" class="form-label">
                        <i class="fas fa-tachometer-alt me-1"></i>
                        Limite de Requisições por Hora
                    </label>
                    <input type="number" class="form-control" id="max_api_requests_per_hour" 
                           name="settings[max_api_requests_per_hour]" 
                           value="<?= e($settingsGrouped['max'][0]['value'] ?? '1000') ?>"
                           min="1" max="10000">
                    <div class="form-text">
                        Limite máximo de requisições por hora por chave de API.
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6 mb-3">
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="enable_registration" 
                               name="settings[enable_registration]" value="true"
                               <?= ($settingsGrouped['enable'][0]['value'] ?? 'false') === 'true' ? 'checked' : '' ?>>
                        <label class="form-check-label" for="enable_registration">
                            <i class="fas fa-user-plus me-1"></i>
                            Permitir Registro de Novos Usuários
                        </label>
                    </div>
                    <div class="form-text">
                        Se habilitado, novos usuários poderão se registrar no sistema.
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Configurações de Email -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-envelope me-2"></i>
                Configurações de Email
            </h5>
        </div>
        <div class="card-body">
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                <strong>Nota:</strong> As configurações de email são definidas no arquivo .env por questões de segurança.
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <strong>Configurações Atuais:</strong>
                    <ul class="list-unstyled mt-2">
                        <li><i class="fas fa-server me-1"></i> Host: <?= $_ENV['MAIL_HOST'] ?? 'Não configurado' ?></li>
                        <li><i class="fas fa-plug me-1"></i> Porta: <?= $_ENV['MAIL_PORT'] ?? 'Não configurado' ?></li>
                        <li><i class="fas fa-envelope me-1"></i> De: <?= $_ENV['MAIL_FROM_ADDRESS'] ?? 'Não configurado' ?></li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <button type="button" class="btn btn-outline-primary" onclick="testEmail()">
                        <i class="fas fa-paper-plane me-1"></i>
                        Testar Envio de Email
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Informações do Sistema -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-info-circle me-2"></i>
                Informações do Sistema
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6>Servidor</h6>
                    <ul class="list-unstyled">
                        <li><strong>PHP:</strong> <?= PHP_VERSION ?></li>
                        <li><strong>Servidor:</strong> <?= $_SERVER['SERVER_SOFTWARE'] ?? 'N/A' ?></li>
                        <li><strong>Sistema:</strong> <?= PHP_OS ?></li>
                        <li><strong>Memória:</strong> <?= ini_get('memory_limit') ?></li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>Aplicação</h6>
                    <ul class="list-unstyled">
                        <li><strong>Versão:</strong> 1.0.0</li>
                        <li><strong>Ambiente:</strong> <?= config('app.debug') ? 'Desenvolvimento' : 'Produção' ?></li>
                        <li><strong>URL:</strong> <?= config('app.url') ?></li>
                        <li><strong>Timezone:</strong> <?= date_default_timezone_get() ?></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Botões de Ação -->
    <div class="card">
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <small class="text-muted">
                        <i class="fas fa-save me-1"></i>
                        As configurações serão salvas no banco de dados.
                    </small>
                </div>
                <div>
                    <button type="button" class="btn btn-outline-secondary me-2" onclick="resetForm()">
                        <i class="fas fa-undo me-1"></i>
                        Resetar
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i>
                        Salvar Configurações
                    </button>
                </div>
            </div>
        </div>
    </div>
</form>




<?php
$content = ob_get_clean();
include __DIR__ . '/../layouts/app.php';
?>

<script>
function testEmail() {
    alert('Funcionalidade de teste de email será implementada em breve.');
}

function resetForm() {
    if (confirm('Tem certeza que deseja resetar todas as alterações?')) {
        location.reload();
    }
}

// Mostrar/ocultar senhas
document.querySelectorAll('input[type="password"]').forEach(input => {
    const container = input.parentElement;
    const toggleBtn = document.createElement('button');
    toggleBtn.type = 'button';
    toggleBtn.className = 'btn btn-outline-secondary';
    toggleBtn.innerHTML = '<i class="fas fa-eye"></i>';
    toggleBtn.style.position = 'absolute';
    toggleBtn.style.right = '5px';
    toggleBtn.style.top = '50%';
    toggleBtn.style.transform = 'translateY(-50%)';
    toggleBtn.style.zIndex = '10';
    
    container.style.position = 'relative';
    container.appendChild(toggleBtn);
    
    toggleBtn.addEventListener('click', function() {
        if (input.type === 'password') {
            input.type = 'text';
            toggleBtn.innerHTML = '<i class="fas fa-eye-slash"></i>';
        } else {
            input.type = 'password';
            toggleBtn.innerHTML = '<i class="fas fa-eye"></i>';
        }
    });
});


</script>
