<?php
/**
 * Rotas da API - APENAS VALIDAÇÃO DE CHAVE
 * Sistema de Dashboard E1Copy AI
 *
 * Conforme solicitado:
 * - Plugin: apenas validação de chave
 * - Dashboard: apenas validação de chave
 */

// =====================================================
// APIS ATIVAS - APENAS VALIDAÇÃO DE CHAVE
// =====================================================

// Endpoints de validação de chave (para plugin e dashboard)
$router->get('/api/v1/verify-key', 'Api@verifyKey');
$router->post('/api/v1/verify-key', 'Api@verifyKey');
$router->post('/api/v1/validate', 'Api@validateKey');
$router->post('/api/v1/status', 'Api@keyStatus');

// Endpoints administrativos (apenas para dashboard admin)
$router->get('/api/v1/client-status/{key}', 'Api@clientStatus');
$router->post('/api/v1/suspend-key', 'Api@suspendKey');
$router->post('/api/v1/activate-key', 'Api@activateKey');
$router->get('/api/v1/usage-stats/{key}', 'Api@usageStats');

// Health check (sem autenticação)
$router->get('/api/v1/health', 'Api@healthCheck');

// =====================================================
// APIS REMOVIDAS (conforme solicitado)
// =====================================================
// - /api/v1/generate-content (removida do plugin)
// - /api/v1/screenshot (removida do plugin)
// - /api/v1/plans (removida)
// - /api/v1/auth/token (removida)
// - /api/v1/register-site (removida)
// - /api/v1/pending-posts (removida do dashboard)
// - /api/v1/pending-posts/by-client (removida do dashboard)
// - /api/v1/pending-posts/by-site (removida do dashboard)
// - /api/v1/client-posts (removida do dashboard)
// - /api/v1/client-posts/test (removida do dashboard)
// - /api/v1/client-posts/pending (removida do dashboard)

// Endpoint específico para Product Reviews (N8N)
$router->get('/api/v1/product-reviews/pending', 'ApiController@getProductReviewsPending');

// Endpoints para processamento de posts via N8N
$router->get('/api/v1/posts/pending', 'ApiPosts@pendingPosts');
$router->post('/api/v1/posts/{id}/completed', 'ApiPosts@markAsProcessed');
$router->post('/api/v1/posts/{id}/failed', 'ApiPosts@markAsFailed');

// Endpoints para Posts Clients (Dashboard)
$router->get('/api/v1/client-posts', 'ApiClientPosts@index');
$router->get('/api/v1/client-posts/stats', 'ApiClientPosts@stats');
$router->get('/api/v1/client-posts/{id}', 'ApiClientPosts@show');
