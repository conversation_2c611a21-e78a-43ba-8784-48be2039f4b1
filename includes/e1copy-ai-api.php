<?php
if (!defined('ABSPATH')) exit; // Evita acesso direto

function e1copy_ai_generate_post($prompt) {
    // Verificar se a API está válida (verificação em tempo real)
    if (!e1copy_ai_is_activated(true)) {
        return new WP_Error('api_invalid', 'Licença do E1Copy AI inválida ou suspensa. Verifique sua licença em app.melhorcupom.shop');
    }

    $apiKey = get_option('e1copy_ai_api_key');
    if (empty($apiKey)) {
        return new WP_Error('no_api_key', 'A chave da API E1Copy AI não está configurada.');
    }

    // Pega a instrução do prompt configurada
    $instruction = get_option('e1copy_ai_prompt_instruction', 'escreva um artigo de três mil palavras otimizado para SEO');

    // Combina o prompt com a instrução personalizada
    $finalPrompt = rtrim($prompt) . ' ' . $instruction;

    // URL da API Groq
    $url = 'https://api.groq.com/openai/v1/chat/completions';

    // Dados para enviar à API
    $data = [
        "model" => "llama-3.3-70b-versatile",  // Modelo escolhido
        "temperature" => 1,
        "max_completion_tokens" => 8192,  // Corrigido: valor inteiro
        "messages" => [
            [
                "role" => "user",
                "content" => wp_kses_post($finalPrompt) // Sanitizando o prompt
            ]
        ]
    ];

    // Configurações da requisição para a API
    $args = [
        'headers' => [
            'Content-Type'  => 'application/json',
            'Authorization' => 'Bearer ' . esc_attr($apiKey),
        ],
        'body'    => json_encode($data),
        'timeout' => 60,
    ];

    // Envia a requisição para a API Groq
    $response = wp_remote_post($url, $args);

    if (is_wp_error($response)) {
        return $response; // Se erro, retorna o objeto de erro
    }

    // Pega o conteúdo da resposta
    $body = json_decode(wp_remote_retrieve_body($response), true);

    // Valida a resposta
    if (!isset($body['choices'][0]['message']['content'])) {
        return new WP_Error('invalid_response', 'Resposta inválida da API Groq.');
    }

    // Retorna o conteúdo gerado
    return trim($body['choices'][0]['message']['content']);
}
