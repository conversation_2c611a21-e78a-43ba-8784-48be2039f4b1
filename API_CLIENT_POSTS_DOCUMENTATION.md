# API Client Posts - Documentação

## 📋 Visão Geral

API para listar e gerenciar posts criados pelos clientes no dashboard do E1Copy AI. Esta API fornece acesso aos posts da tabela `client_posts` com filtros avançados e informações completas.

**Base URL:** `https://app.melhorcupom.shop`

**Versão:** v1

**Autenticação:** Não requer autenticação (dados públicos de usuários ativos)

## 🔗 Endpoints Disponíveis

### 1. Listar Posts dos Clientes
**GET** `/api/v1/client-posts`

Lista posts dos clientes com filtros opcionais.

#### Parâmetros de Query

| Parâmetro | Tipo | Descrição | Exemplo |
|-----------|------|-----------|---------|
| `status` | string | Filtrar por status | `?status=pending` |
| `post_type` | string | Filtrar por tipo | `?post_type=product_review` |
| `user_id` | int | Filtrar por usuário específico | `?user_id=4` |
| `site_id` | int | Filtrar por site específico | `?site_id=6` |
| `limit` | int | Limite de resultados (max 100) | `?limit=50` |
| `offset` | int | Offset para paginação | `?offset=0` |
| `include_content` | boolean | Incluir conteúdo completo | `?include_content=true` |

#### Status Disponíveis
- `draft` - Rascunho
- `pending` - Pendente
- `processing` - Processando
- `completed` - Concluído
- `failed` - Falhou
- `published` - Publicado

#### Tipos de Post
- `article` - Artigo
- `product_review` - Review de Produto

#### Exemplo de Resposta
```json
{
  "success": true,
  "data": {
    "posts": [
      {
        "id": 1,
        "site_id": 2,
        "user_id": 3,
        "post_type": "product_review",
        "title": "Review do iPhone 15",
        "excerpt": "Análise completa do novo iPhone...",
        "content": "Conteúdo completo do post...",
        "keywords": "iphone, review, smartphone",
        "category": "Tecnologia",
        "tags": ["iPhone", "Apple", "Smartphone"],
        "slug": "review-iphone-15",
        "product_name": "iPhone 15",
        "product_url": "https://apple.com/iphone-15",
        "product_price": 999.99,
        "product_rating": 4.5,
        "product_pros": ["Câmera excelente", "Performance"],
        "product_cons": ["Preço alto", "Bateria"],
        "affiliate_link": "https://affiliate.link",
        "status": "pending",
        "wordpress_post_id": null,
        "error_message": null,
        "created_at": "2024-01-15 10:30:00",
        "updated_at": "2024-01-15 10:30:00",
        "published_at": null,
        "site_name": "Meu Blog Tech",
        "site_url": "https://meublogtech.com",
        "user_name": "João Silva",
        "user_email": "<EMAIL>",
        "api_key": "abc123...",
        "images": [
          "/uploads/posts/image1.jpg",
          "/uploads/posts/image2.jpg"
        ]
      }
    ],
    "posts_by_client": [
      {
        "client_info": {
          "user_id": 3,
          "user_name": "João Silva",
          "user_email": "<EMAIL>"
        },
        "posts": [...],
        "total_posts": 5
      }
    ],
    "posts_by_site": [
      {
        "site_info": {
          "site_id": 2,
          "site_name": "Meu Blog Tech",
          "site_url": "https://meublogtech.com",
          "user_name": "João Silva",
          "user_email": "<EMAIL>",
          "api_key": "abc123..."
        },
        "posts": [...],
        "total_posts": 3
      }
    ],
    "stats": {
      "total_posts": 150,
      "returned_posts": 50,
      "total_clients": 25,
      "total_sites": 40,
      "limit": 50,
      "offset": 0,
      "has_more": true
    },
    "filters_applied": {
      "status": "pending",
      "post_type": null,
      "user_id": null,
      "site_id": null,
      "include_content": true
    }
  },
  "timestamp": "2024-01-15 15:30:00"
}
```

### 2. Buscar Post Específico
**GET** `/api/v1/client-posts/{id}`

Retorna um post específico por ID.

#### Exemplo de Resposta
```json
{
  "success": true,
  "data": {
    "id": 1,
    "site_id": 2,
    "user_id": 3,
    "post_type": "product_review",
    "title": "Review do iPhone 15",
    "content": "Conteúdo completo do post...",
    // ... outros campos
  },
  "timestamp": "2024-01-15 15:30:00"
}
```

### 3. Estatísticas dos Posts
**GET** `/api/v1/client-posts/stats`

Retorna estatísticas gerais dos posts dos clientes.

#### Exemplo de Resposta
```json
{
  "success": true,
  "data": {
    "general_stats": {
      "total_posts": 500,
      "total_clients": 50,
      "total_sites": 75,
      "pending_posts": 25,
      "processing_posts": 5,
      "completed_posts": 300,
      "published_posts": 150,
      "failed_posts": 15,
      "draft_posts": 5,
      "articles": 300,
      "product_reviews": 200
    },
    "daily_stats": [
      {
        "date": "2024-01-15",
        "posts_created": 10,
        "posts_published": 8
      }
    ],
    "top_clients": [
      {
        "user_name": "João Silva",
        "user_email": "<EMAIL>",
        "total_posts": 25,
        "published_posts": 20
      }
    ]
  },
  "timestamp": "2024-01-15 15:30:00"
}
```

## 🧪 Exemplos de Uso

### 1. Posts Pendentes
```bash
curl -X GET "https://app.melhorcupom.shop/api/v1/client-posts?status=pending"
```

### 2. Reviews de Produtos Pendentes
```bash
curl -X GET "https://app.melhorcupom.shop/api/v1/client-posts?status=pending&post_type=product_review"
```

### 3. Posts de um Cliente Específico
```bash
curl -X GET "https://app.melhorcupom.shop/api/v1/client-posts?user_id=4"
```

### 4. Posts com Paginação
```bash
curl -X GET "https://app.melhorcupom.shop/api/v1/client-posts?limit=20&offset=40"
```

### 5. Posts sem Conteúdo Completo (mais rápido)
```bash
curl -X GET "https://app.melhorcupom.shop/api/v1/client-posts?include_content=false"
```

### 6. Post Específico
```bash
curl -X GET "https://app.melhorcupom.shop/api/v1/client-posts/123"
```

### 7. Estatísticas
```bash
curl -X GET "https://app.melhorcupom.shop/api/v1/client-posts/stats"
```

## 🔒 Autenticação

Esta API não requer autenticação, mas retorna apenas dados de usuários ativos.

## 📋 Códigos de Status HTTP

| Código | Descrição | Quando Ocorre |
|--------|-----------|---------------|
| `200` | Sucesso | Requisição processada com sucesso |
| `404` | Não Encontrado | Post específico não existe |
| `500` | Erro Interno | Erro no servidor ou banco de dados |

## ⚠️ Tratamento de Erros

### Estrutura de Erro
```json
{
  "success": false,
  "error": "Mensagem de erro detalhada",
  "timestamp": "2024-01-15 15:30:00"
}
```

### Tipos de Erro Comuns

1. **Post não encontrado (404)**
```json
{
  "success": false,
  "error": "Post não encontrado",
  "timestamp": "2024-01-15 15:30:00"
}
```

2. **Erro de banco de dados (500)**
```json
{
  "success": false,
  "error": "Database connection failed",
  "timestamp": "2024-01-15 15:30:00"
}
```

3. **Parâmetros inválidos**
- Limite maior que 100: automaticamente ajustado para 100
- Offset negativo: automaticamente ajustado para 0
- Status inválido: ignorado (retorna todos os status)

## 📊 Campos dos Posts

### Campos Básicos
- `id` - ID único do post
- `site_id` - ID do site
- `user_id` - ID do usuário
- `post_type` - Tipo do post (article/product_review)
- `title` - Título do post
- `content` - Conteúdo completo (opcional)
- `excerpt` - Resumo do post
- `keywords` - Palavras-chave
- `category` - Categoria
- `tags` - Array de tags
- `slug` - Slug do post
- `status` - Status atual
- `created_at` - Data de criação
- `updated_at` - Data de atualização
- `published_at` - Data de publicação

### Campos de Produto (para product_review)
- `product_name` - Nome do produto
- `product_url` - URL do produto
- `product_price` - Preço do produto
- `product_rating` - Avaliação (1-5)
- `product_pros` - Array de pontos positivos
- `product_cons` - Array de pontos negativos
- `affiliate_link` - Link de afiliado

### Campos de Site/Cliente
- `site_name` - Nome do site
- `site_url` - URL do site
- `user_name` - Nome do cliente
- `user_email` - Email do cliente
- `api_key` - Chave de API do site

### Campos de Imagens
- `images` - Array com URLs das imagens

## ⚡ Performance e Limitações

### Limitações
- **Limite máximo:** 100 posts por requisição
- **Rate limiting:** Não implementado (use com responsabilidade)
- **Timeout:** 30 segundos por requisição
- **Dados:** Apenas usuários com status 'active'

### Melhores Práticas

1. **Para consultas rápidas:**
   ```bash
   # Use include_content=false para listas
   GET /api/v1/client-posts?include_content=false&limit=50
   ```

2. **Para paginação eficiente:**
   ```bash
   # Primeira página
   GET /api/v1/client-posts?limit=20&offset=0

   # Próxima página
   GET /api/v1/client-posts?limit=20&offset=20
   ```

3. **Para filtros específicos:**
   ```bash
   # Combine filtros para resultados precisos
   GET /api/v1/client-posts?status=pending&post_type=product_review&user_id=4
   ```

4. **Para monitoramento:**
   ```bash
   # Use stats para visão geral
   GET /api/v1/client-posts/stats
   ```

### Índices Otimizados
- `status` + `user_id`
- `post_type` + `created_at`
- `site_id` + `status`
- `created_at` (para ordenação)

## 🔄 Versionamento

- **Versão atual:** v1
- **Compatibilidade:** Mantida para versões anteriores
- **Mudanças:** Documentadas em changelog

## 📞 Suporte

Para dúvidas ou problemas com a API:
- Verifique os logs de erro no servidor
- Confirme que os dados existem no banco
- Teste com parâmetros simples primeiro

## 🚀 Roadmap

Funcionalidades planejadas:
- [ ] Filtro por data de criação
- [ ] Ordenação customizável
- [ ] Busca por texto no título/conteúdo
- [ ] Exportação em diferentes formatos
- [ ] Webhooks para notificações
- [ ] Rate limiting configurável
