# Correção de Duplicações na API

## 🔍 Problema Identificado

A API estava retornando posts duplicados (mesmo ID 13 aparecia 2 vezes) devido a:
1. **<PERSON><PERSON><PERSON><PERSON> chaves API** para o mesmo site/usuário
2. **JOIN com api_keys** causando duplicação quando há múltiplas chaves
3. **Falta de DISTINCT** na query principal

## ✅ Correções Aplicadas

### 1. **Query Principal Simplificada**
- ✅ Removido JOIN com `api_keys` da query principal
- ✅ Adicionado `DISTINCT` para evitar duplicações
- ✅ Busca de API keys separadamente

**ANTES (Problemático):**
```sql
SELECT cp.*, ak.api_key  -- Causava duplicações
FROM client_posts cp
LEFT JOIN client_sites cs ON cp.site_id = cs.id
LEFT JOIN users u ON cs.user_id = u.id
LEFT JOIN api_keys ak ON cs.api_key_id = ak.id  -- ❌ Duplicações aqui
```

**DEPOIS (Corrigido):**
```sql
SELECT DISTINCT cp.*, cs.site_name, u.name, u.email
FROM client_posts cp
LEFT JOIN client_sites cs ON cp.site_id = cs.id
LEFT JOIN users u ON cs.user_id = u.id
-- ✅ Sem JOIN com api_keys na query principal
```

### 2. **Busca de API Keys Separada**
```php
// Buscar API keys apenas para sites únicos
$siteIds = array_unique(array_column($posts, 'site_id'));
$apiKeysData = $this->db->fetchAll("
    SELECT cs.id as site_id, ak.api_key
    FROM client_sites cs
    LEFT JOIN api_keys ak ON cs.api_key_id = ak.id
    WHERE cs.id IN ($siteIdsStr)
");
```

### 3. **Remoção de Duplicações Adicional**
```php
// Garantir posts únicos por ID
$uniquePosts = [];
$seenIds = [];

foreach ($posts as $post) {
    if (!in_array($post['id'], $seenIds)) {
        $uniquePosts[] = $post;
        $seenIds[] = $post['id'];
    }
}
```

## 🔧 Fluxo Corrigido

### 1. **Busca Principal**
- Query com `DISTINCT` para posts únicos
- Sem JOIN com `api_keys` (evita duplicações)

### 2. **Busca de API Keys**
- Query separada apenas para sites únicos
- Mapeamento `site_id → api_key`

### 3. **Processamento**
- Adicionar API key a cada post
- Processar URLs das imagens
- Remover duplicações por ID

### 4. **Organização**
- Agrupar por cliente (únicos)
- Agrupar por site (únicos)
- Resposta final sem duplicações

## 📊 Resultado Esperado

**ANTES (Duplicado):**
```json
{
  "data": {
    "total_pending": 2,        // ❌ Contagem errada
    "all_posts": [
      {"id": 13, ...},         // ❌ Duplicado
      {"id": 13, ...}          // ❌ Duplicado
    ]
  }
}
```

**DEPOIS (Único):**
```json
{
  "data": {
    "total_pending": 1,        // ✅ Contagem correta
    "all_posts": [
      {"id": 13, ...}          // ✅ Único
    ],
    "posts_by_client": [
      {
        "user_info": {...},
        "posts": [
          {"id": 13, ...}      // ✅ Único
        ]
      }
    ],
    "posts_by_site": [
      {
        "site_info": {
          "api_key": "e1copy_..."  // ✅ API key incluída
        },
        "posts": [
          {"id": 13, ...}      // ✅ Único
        ]
      }
    ]
  }
}
```

## 🧪 Teste da Correção

```bash
curl -X GET "https://app.melhorcupom.shop/api/v1/client-posts/pending"
```

**Verificar:**
1. ✅ `total_pending` = 1 (não 2)
2. ✅ `all_posts` contém apenas 1 post
3. ✅ `posts_by_client[0].posts` contém apenas 1 post
4. ✅ `posts_by_site[0].posts` contém apenas 1 post
5. ✅ `api_key` está presente em cada site

## 🔍 Logs de Debug

Os logs agora mostram:
```
Posts com dados completos: 1
Posts únicos após remoção de duplicações: 1
Organização concluída - Sites: 1, Clientes: 1
```

## ✅ Benefícios da Correção

1. **✅ Eliminação de Duplicações**
   - Posts únicos por ID
   - Contagem correta
   - Dados consistentes

2. **✅ Performance Melhorada**
   - Query principal mais simples
   - Busca de API keys otimizada
   - Menos dados transferidos

3. **✅ Dados Completos**
   - API keys incluídas
   - Informações de usuário e site
   - URLs de imagens processadas

4. **✅ Compatibilidade N8N**
   - Estrutura organizada
   - Dados únicos e confiáveis
   - Fácil processamento

## 🎯 **DUPLICAÇÕES ELIMINADAS!**

A API agora retorna:
- ✅ Posts únicos (sem duplicações)
- ✅ Contagem correta
- ✅ API keys incluídas
- ✅ Dados organizados por cliente e site
- ✅ Compatível com N8N
