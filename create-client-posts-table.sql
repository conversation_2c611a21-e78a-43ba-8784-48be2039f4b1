USE e1copy_dashboard;

CREATE TABLE IF NOT EXISTS client_posts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    site_id INT NOT NULL,
    user_id INT NOT NULL,
    post_type ENUM('article', 'product_review') NOT NULL,
    title VARCHAR(500) NOT NULL,
    content LONGTEXT,
    excerpt TEXT,
    keywords TEXT,
    category VARCHAR(255),
    tags TEXT,
    slug VARCHAR(255),
    
    -- Campos específicos para produto review
    product_name VARCHAR(255) NULL,
    product_url TEXT NULL,
    product_price DECIMAL(10,2) NULL,
    product_rating DECIMAL(2,1) NULL,
    product_pros TEXT NULL,
    product_cons TEXT NULL,
    affiliate_link TEXT NULL,
    
    -- Status e controle
    status ENUM('draft', 'pending', 'processing', 'completed', 'failed', 'published') DEFAULT 'draft',
    wordpress_post_id INT NULL,
    error_message TEXT NULL,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    published_at TIMESTAMP NULL,
    
    -- Foreign keys
    FOREIGN KEY (site_id) REFERENCES client_sites(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- Indexes
    INDEX idx_site_status (site_id, status),
    INDEX idx_user_status (user_id, status),
    INDEX idx_post_type (post_type),
    INDEX idx_created_at (created_at)
);
