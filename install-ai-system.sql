-- Script SQL para instalar o sistema de IA
-- Execute este script no seu banco de dados MySQL

-- Tabela para armazenar templates de conteúdo
CREATE TABLE IF NOT EXISTS content_templates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    html_template TEXT NOT NULL,
    variables JSON, -- Array de variáveis disponíveis
    required_variables JSON, -- Array de variáveis obrigatórias
    is_default BOOLEAN DEFAULT FALSE,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_status (status),
    INDEX idx_is_default (is_default)
);

-- Tabela para configurações de IA
CREATE TABLE IF NOT EXISTS ai_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(255) NOT NULL UNIQUE,
    setting_value TEXT,
    setting_type ENUM('string', 'json', 'boolean', 'number') DEFAULT 'string',
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_setting_key (setting_key)
);

-- Tabela para posts gerados
CREATE TABLE IF NOT EXISTS generated_posts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    site_id INT NULL, -- Referência para client_sites se existir
    site_url VARCHAR(500) NOT NULL,
    site_name VARCHAR(255),
    template_id INT NULL,
    
    -- Dados do post
    post_title VARCHAR(500),
    post_content LONGTEXT,
    post_excerpt TEXT,
    post_slug VARCHAR(200),
    post_category VARCHAR(255),
    post_tags TEXT, -- JSON array
    post_status ENUM('draft', 'published', 'failed') DEFAULT 'draft',
    
    -- Dados da geração
    prompt_used TEXT,
    ai_response LONGTEXT,
    variables_used JSON,
    
    -- Status e controle
    generation_status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending',
    publish_status ENUM('pending', 'published', 'failed') DEFAULT 'pending',
    error_message TEXT,
    
    -- WordPress integration
    wp_post_id INT NULL, -- ID do post no WordPress
    wp_response TEXT, -- Resposta da API do WordPress
    
    -- Timestamps
    generated_at TIMESTAMP NULL,
    published_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (template_id) REFERENCES content_templates(id) ON DELETE SET NULL,
    
    INDEX idx_user_id (user_id),
    INDEX idx_site_url (site_url),
    INDEX idx_generation_status (generation_status),
    INDEX idx_publish_status (publish_status),
    INDEX idx_created_at (created_at)
);

-- Tabela para fila de processamento
CREATE TABLE IF NOT EXISTS content_queue (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    site_id INT NULL,
    site_url VARCHAR(500) NOT NULL,
    site_name VARCHAR(255),
    template_id INT NULL,
    
    -- Configurações da geração
    topic VARCHAR(500), -- Tópico/tema do post
    keywords TEXT, -- Palavras-chave (JSON array)
    custom_variables JSON, -- Variáveis personalizadas para este post
    
    -- Controle da fila
    priority INT DEFAULT 0, -- Prioridade (maior = mais prioritário)
    status ENUM('pending', 'processing', 'completed', 'failed', 'cancelled') DEFAULT 'pending',
    attempts INT DEFAULT 0,
    max_attempts INT DEFAULT 3,
    
    -- Agendamento
    scheduled_for TIMESTAMP NULL, -- Quando deve ser processado
    
    -- Logs
    error_message TEXT,
    last_attempt_at TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (template_id) REFERENCES content_templates(id) ON DELETE SET NULL,
    
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_priority (priority),
    INDEX idx_scheduled_for (scheduled_for),
    INDEX idx_created_at (created_at)
);

-- Tabela para logs de execução
CREATE TABLE IF NOT EXISTS ai_execution_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    site_url VARCHAR(500) NOT NULL,
    action_type ENUM('generate_content', 'publish_post', 'queue_process', 'site_check') NOT NULL,

    -- Dados da execução
    request_data JSON,
    response_data JSON,
    execution_time_ms INT, -- Tempo de execução em milissegundos

    -- Status
    status ENUM('success', 'error', 'warning') NOT NULL,
    error_message TEXT,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,

    INDEX idx_user_id (user_id),
    INDEX idx_site_url (site_url),
    INDEX idx_action_type (action_type),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- Tabela para posts criados pelo painel do cliente
CREATE TABLE IF NOT EXISTS client_posts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    site_id INT NOT NULL,
    user_id INT NOT NULL,
    post_type ENUM('article', 'product_review') NOT NULL,
    title VARCHAR(500) NOT NULL,
    content LONGTEXT,
    excerpt TEXT,
    keywords TEXT,
    category VARCHAR(255),
    tags TEXT,
    slug VARCHAR(255),

    -- Campos específicos para produto review
    product_name VARCHAR(255) NULL,
    product_url TEXT NULL,
    product_price DECIMAL(10,2) NULL,
    product_rating DECIMAL(2,1) NULL,
    product_pros TEXT NULL,
    product_cons TEXT NULL,
    affiliate_link TEXT NULL,

    -- Status e controle
    status ENUM('draft', 'pending', 'processing', 'completed', 'failed', 'published') DEFAULT 'draft',
    wordpress_post_id INT NULL,
    error_message TEXT NULL,

    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    published_at TIMESTAMP NULL,

    -- Foreign keys
    FOREIGN KEY (site_id) REFERENCES client_sites(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,

    -- Indexes
    INDEX idx_site_status (site_id, status),
    INDEX idx_user_status (user_id, status),
    INDEX idx_post_type (post_type),
    INDEX idx_created_at (created_at)
);

-- Inserir configurações padrão de IA
INSERT IGNORE INTO ai_settings (setting_key, setting_value, setting_type, description) VALUES
('groq_api_key', '', 'string', 'Chave da API do Groq'),
('groq_model', 'llama3-8b-8192', 'string', 'Modelo do Groq a ser usado'),
('default_template_id', '1', 'number', 'ID do template padrão'),
('max_tokens', '2000', 'number', 'Máximo de tokens por geração'),
('temperature', '0.7', 'number', 'Temperatura da IA (0-1)'),
('default_variables', '["titulo", "subtitulo", "palavra_chave", "conteudo", "slug", "categoria", "tags", "resumo"]', 'json', 'Variáveis padrão disponíveis'),
('required_variables', '["titulo", "conteudo"]', 'json', 'Variáveis obrigatórias'),
('post_generation_enabled', 'true', 'boolean', 'Habilitar geração automática de posts'),
('queue_processing_enabled', 'true', 'boolean', 'Habilitar processamento da fila'),
('max_posts_per_day', '10', 'number', 'Máximo de posts por dia por cliente');

-- Inserir template padrão
INSERT IGNORE INTO content_templates (id, name, description, html_template, variables, required_variables, is_default) VALUES
(1, 'Template Padrão', 'Template básico para posts', 
'<h1>{{titulo}}</h1>
<h2>{{subtitulo}}</h2>
<div class="post-meta">
    <span class="category">{{categoria}}</span>
    <span class="tags">{{tags}}</span>
</div>
<div class="post-excerpt">
    <p><strong>{{resumo}}</strong></p>
</div>
<div class="post-content">
    {{conteudo}}
</div>',
'["titulo", "subtitulo", "palavra_chave", "conteudo", "slug", "categoria", "tags", "resumo"]',
'["titulo", "conteudo"]',
true);
