-- =====================================================
-- CORREÇÃO DE DUPLICAÇÕES NA API - SCRIPT SQL
-- Execute este script no phpMyAdmin para corrigir problemas
-- =====================================================

-- 1. VERIFICAR ESTRUTURA ATUAL DAS TABELAS
-- =====================================================

-- Verificar se há posts duplicados na tabela
SELECT 
    'Posts duplicados por ID' as verificacao,
    id, 
    COUNT(*) as quantidade
FROM client_posts 
GROUP BY id 
HAVING COUNT(*) > 1;

-- Verificar se há múltiplas API keys para o mesmo site
SELECT 
    'Sites com múltiplas API keys' as verificacao,
    cs.id as site_id,
    cs.site_name,
    cs.site_url,
    COUNT(ak.id) as total_api_keys,
    GROUP_CONCAT(ak.api_key SEPARATOR ', ') as api_keys
FROM client_sites cs
LEFT JOIN api_keys ak ON cs.api_key_id = ak.id
GROUP BY cs.id
HAVING COUNT(ak.id) > 1;

-- Verificar relacionamentos entre posts, sites e usuários
SELECT 
    'Relacionamentos posts-sites-users' as verificacao,
    cp.id as post_id,
    cp.user_id as post_user_id,
    cp.site_id,
    cs.user_id as site_user_id,
    CASE 
        WHEN cp.user_id = cs.user_id THEN 'OK'
        ELSE 'INCONSISTENTE'
    END as status_relacionamento
FROM client_posts cp
LEFT JOIN client_sites cs ON cp.site_id = cs.id
WHERE cp.status = 'pending'
ORDER BY cp.id;

-- =====================================================
-- 2. LIMPEZA DE DADOS DUPLICADOS
-- =====================================================

-- Remover posts verdadeiramente duplicados (mesmo conteúdo)
DELETE cp1 FROM client_posts cp1
INNER JOIN client_posts cp2 
WHERE cp1.id > cp2.id 
AND cp1.title = cp2.title 
AND cp1.user_id = cp2.user_id 
AND cp1.site_id = cp2.site_id 
AND cp1.created_at = cp2.created_at;

-- =====================================================
-- 3. CORRIGIR RELACIONAMENTOS INCONSISTENTES
-- =====================================================

-- Corrigir posts onde user_id não bate com o user_id do site
UPDATE client_posts cp
INNER JOIN client_sites cs ON cp.site_id = cs.id
SET cp.user_id = cs.user_id
WHERE cp.user_id != cs.user_id;

-- =====================================================
-- 4. OTIMIZAR ESTRUTURA DAS TABELAS
-- =====================================================

-- Adicionar índices únicos para evitar duplicações futuras
ALTER TABLE client_posts 
ADD UNIQUE INDEX unique_post_per_site_user (site_id, user_id, title(100), created_at);

-- Remover o índice se já existir (ignorar erro se não existir)
-- ALTER TABLE client_posts DROP INDEX unique_post_per_site_user;

-- =====================================================
-- 5. CORRIGIR PROBLEMA DE MÚLTIPLAS API KEYS
-- =====================================================

-- Para sites com múltiplas API keys, manter apenas a mais recente ativa
UPDATE client_sites cs
SET api_key_id = (
    SELECT ak.id 
    FROM api_keys ak 
    WHERE ak.user_id = cs.user_id 
    AND ak.status = 'active'
    ORDER BY ak.created_at DESC 
    LIMIT 1
)
WHERE cs.api_key_id IS NULL OR cs.api_key_id NOT IN (
    SELECT id FROM api_keys WHERE status = 'active'
);

-- =====================================================
-- 6. VERIFICAÇÕES FINAIS
-- =====================================================

-- Verificar posts pendentes únicos por usuário
SELECT 
    'Posts pendentes por usuário' as verificacao,
    u.id as user_id,
    u.name as user_name,
    u.email,
    COUNT(DISTINCT cp.id) as posts_pendentes
FROM users u
LEFT JOIN client_posts cp ON u.id = cp.user_id AND cp.status = 'pending'
WHERE u.status = 'active'
GROUP BY u.id
ORDER BY posts_pendentes DESC;

-- Verificar se ainda há duplicações na query da API
SELECT 
    'Teste query API - posts únicos' as verificacao,
    COUNT(DISTINCT cp.id) as posts_unicos,
    COUNT(*) as total_registros,
    CASE 
        WHEN COUNT(DISTINCT cp.id) = COUNT(*) THEN 'SEM DUPLICAÇÕES'
        ELSE 'AINDA HÁ DUPLICAÇÕES'
    END as status
FROM client_posts cp
LEFT JOIN client_sites cs ON cp.site_id = cs.id
LEFT JOIN users u ON cp.user_id = u.id
WHERE cp.status = 'pending'
AND u.status = 'active';

-- Verificar estrutura final dos relacionamentos
SELECT 
    'Estrutura final' as verificacao,
    cp.id,
    cp.title,
    cp.user_id,
    u.name as user_name,
    u.email,
    cs.site_name,
    cs.site_url,
    ak.api_key
FROM client_posts cp
LEFT JOIN client_sites cs ON cp.site_id = cs.id
LEFT JOIN users u ON cp.user_id = u.id
LEFT JOIN api_keys ak ON cs.api_key_id = ak.id
WHERE cp.status = 'pending'
AND u.status = 'active'
ORDER BY cp.user_id, cp.created_at;

-- =====================================================
-- 7. SCRIPT DE VERIFICAÇÃO CONTÍNUA
-- =====================================================

-- Query para monitorar duplicações futuras
-- Execute periodicamente para verificar se o problema retorna
SELECT 
    'Monitoramento duplicações' as tipo,
    cp.id,
    cp.title,
    cp.user_id,
    u.email,
    COUNT(*) as ocorrencias
FROM client_posts cp
LEFT JOIN client_sites cs ON cp.site_id = cs.id
LEFT JOIN users u ON cp.user_id = u.id
LEFT JOIN api_keys ak ON cs.api_key_id = ak.id
WHERE cp.status = 'pending'
AND u.status = 'active'
GROUP BY cp.id, cp.title, cp.user_id, u.email
HAVING COUNT(*) > 1;

-- =====================================================
-- INSTRUÇÕES DE USO:
-- =====================================================
-- 1. Faça backup do banco antes de executar
-- 2. Execute as queries uma por vez no phpMyAdmin
-- 3. Verifique os resultados das queries SELECT antes de executar as UPDATE/DELETE
-- 4. Teste a API após executar o script
-- 5. Use a query de monitoramento para verificar se o problema retorna
-- =====================================================
