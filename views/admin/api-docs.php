<?php
ob_start();
$title = 'Documentação da API - E1Copy AI';
?>

<style>
.api-endpoint {
    background: #f8f9fa;
    border-left: 4px solid #007bff;
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 0 5px 5px 0;
}

.api-endpoint h5 {
    color: #007bff;
    margin-bottom: 10px;
}

.method-badge {
    font-size: 0.75rem;
    padding: 4px 8px;
    border-radius: 3px;
    font-weight: bold;
    margin-right: 10px;
}

.method-get { background: #28a745; color: white; }
.method-post { background: #007bff; color: white; }
.method-put { background: #ffc107; color: black; }
.method-delete { background: #dc3545; color: white; }

.code-block {
    background: #2d3748;
    color: #e2e8f0;
    padding: 15px;
    border-radius: 5px;
    overflow-x: auto;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
}

.stats-card {
    transition: transform 0.2s;
}

.stats-card:hover {
    transform: translateY(-2px);
}
</style>

<!-- Cabe<PERSON>lho -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">
            <i class="fas fa-book me-2"></i>
            Documentação da API
        </h1>
        <p class="text-muted mb-0">Guia completo da API E1Copy AI Dashboard</p>
    </div>
    <div>
        <span class="badge bg-success">v1.0</span>
    </div>
</div>

<!-- Estatísticas da API -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <h3 class="text-primary"><?= number_format($apiStats['total_requests'] ?? 0) ?></h3>
                <p class="text-muted mb-0">Requisições (30 dias)</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <h3 class="text-info"><?= number_format($apiStats['unique_users'] ?? 0) ?></h3>
                <p class="text-muted mb-0">Usuários Únicos</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <h3 class="text-success"><?= number_format($apiStats['successful_requests'] ?? 0) ?></h3>
                <p class="text-muted mb-0">Sucessos</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <h3 class="text-danger"><?= number_format($apiStats['failed_requests'] ?? 0) ?></h3>
                <p class="text-muted mb-0">Falhas</p>
            </div>
        </div>
    </div>
</div>

<!-- Visão Geral -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-info-circle me-2"></i>
            Visão Geral
        </h5>
    </div>
    <div class="card-body">
        <div class="alert alert-warning">
            <h6><i class="fas fa-exclamation-triangle me-2"></i>APIs Atualizadas</h6>
            <p class="mb-0">Conforme solicitado, o sistema agora possui apenas APIs de <strong>validação de chave</strong>. Todas as outras APIs foram removidas.</p>
        </div>

        <p>A API E1Copy AI Dashboard agora fornece apenas endpoints de validação de chave para integração com o plugin WordPress. Todas as requisições requerem autenticação via chave de API.</p>

        <div class="row">
            <div class="col-md-6">
                <h6><i class="fas fa-server me-2"></i>URL Base</h6>
                <div class="code-block">
<?= url('') ?>
                </div>
            </div>
            <div class="col-md-6">
                <h6><i class="fas fa-key me-2"></i>Autenticação</h6>
                <div class="code-block">
X-E1Copy-API-Key: e1copy_...
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Fluxo de Funcionamento -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-flow-chart me-2"></i>
            Fluxo de Funcionamento
        </h5>
    </div>
    <div class="card-body">
        <div class="alert alert-info">
            <h6><i class="fas fa-info-circle me-2"></i>Fluxo Simplificado</h6>
            <p class="mb-0">O sistema agora funciona apenas com validação de chaves. Outras funcionalidades foram removidas.</p>
        </div>

        <div class="row">
            <div class="col-md-12">
                <ol class="list-group list-group-numbered">
                    <li class="list-group-item">
                        <strong>Cliente cadastra site</strong> no dashboard e recebe chave de licença
                    </li>
                    <li class="list-group-item">
                        <strong>Plugin WordPress</strong> é configurado com a chave de licença
                    </li>
                    <li class="list-group-item">
                        <strong>Plugin valida chave</strong> via API <code>/api/v1/validate</code>
                    </li>
                    <li class="list-group-item">
                        <strong>Dashboard monitora</strong> o status das chaves via endpoints administrativos
                    </li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Endpoints Ativos -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-plug me-2"></i>
            Endpoints Ativos - Apenas Validação de Chave
        </h5>
    </div>
    <div class="card-body">

        <div class="alert alert-success">
            <h6><i class="fas fa-check-circle me-2"></i>APIs Ativas</h6>
            <p class="mb-0">Apenas endpoints de validação de chave estão ativos no sistema.</p>
        </div>

        <!-- Validar Chave -->
        <div class="api-endpoint">
            <h5>
                <span class="method-badge method-post">POST</span>
                /api/v1/validate
            </h5>
            <p><strong>Descrição:</strong> Valida uma chave de API e retorna informações de uso</p>
            <p><strong>Autenticação:</strong> Chave de API no corpo da requisição</p>

            <h6>Parâmetros:</h6>
            <div class="code-block">
{
  "api_key": "e1copy_..."
}
            </div>

            <h6>Resposta de Sucesso:</h6>
            <div class="code-block">
{
  "success": true,
  "data": {
    "valid": true,
    "status": "active",
    "user_id": 123,
    "monthly_usage": 45,
    "monthly_limit": 500,
    "remaining": 455
  }
}
            </div>

            <h6>Exemplo cURL:</h6>
            <div class="code-block">
curl -X POST <?= url('') ?>/api/v1/validate \
  -H "Content-Type: application/json" \
  -d '{"api_key": "e1copy_..."}'
            </div>
        </div>

        <!-- Verificar Chave (GET/POST) -->
        <div class="api-endpoint">
            <h5>
                <span class="method-badge method-get">GET</span>
                <span class="method-badge method-post">POST</span>
                /api/v1/verify-key
            </h5>
            <p><strong>Descrição:</strong> Verifica se uma chave de API é válida (aceita GET e POST)</p>
            <p><strong>Autenticação:</strong> Chave de API no parâmetro ou corpo da requisição</p>

            <h6>Parâmetros (GET):</h6>
            <div class="code-block">
GET /api/v1/verify-key?api_key=e1copy_...
            </div>

            <h6>Parâmetros (POST):</h6>
            <div class="code-block">
{
  "api_key": "e1copy_..."
}
            </div>

            <h6>Resposta de Sucesso:</h6>
            <div class="code-block">
{
  "success": true,
  "data": {
    "valid": true,
    "user_id": 123,
    "status": "active"
  }
}
            </div>
        </div>

        <!-- Status da Chave -->
        <div class="api-endpoint">
            <h5>
                <span class="method-badge method-post">POST</span>
                /api/v1/status
            </h5>
            <p><strong>Descrição:</strong> Retorna informações detalhadas sobre o status da chave</p>
            <p><strong>Autenticação:</strong> Chave de API no corpo da requisição</p>

            <h6>Parâmetros:</h6>
            <div class="code-block">
{
  "api_key": "e1copy_..."
}
            </div>

            <h6>Resposta de Sucesso:</h6>
            <div class="code-block">
{
  "success": true,
  "data": {
    "key_status": "active",
    "user_status": "active",
    "subscription_status": "active",
    "plan_name": "Profissional",
    "expires_at": "2024-12-31T23:59:59Z",
    "monthly_usage": 45,
    "monthly_limit": 500,
    "last_used_at": "2024-01-01T12:00:00Z"
  }
}
            </div>
        </div>

        <!-- Health Check -->
        <div class="api-endpoint">
            <h5>
                <span class="method-badge method-get">GET</span>
                /api/v1/health
            </h5>
            <p><strong>Descrição:</strong> Verifica se a API está funcionando</p>
            <p><strong>Autenticação:</strong> Não requerida</p>

            <h6>Resposta de Sucesso:</h6>
            <div class="code-block">
{
  "success": true,
  "data": {
    "status": "ok",
    "timestamp": "2024-01-01T12:00:00Z",
    "version": "1.0.0"
  }
}
            </div>
        </div>

        <div class="alert alert-danger">
            <h6><i class="fas fa-exclamation-triangle me-2"></i>APIs Removidas</h6>
            <p class="mb-2">As seguintes APIs foram removidas conforme solicitado:</p>
            <ul class="mb-0">
                <li><strong>Plugin:</strong> /api/v1/generate-content, /api/v1/screenshot</li>
                <li><strong>Dashboard:</strong> /api/v1/pending-posts, /api/v1/client-posts, /api/v1/plans</li>
                <li><strong>Auxiliares:</strong> /api/v1/auth/token, /api/v1/register-site</li>
            </ul>
        </div>

    </div>
</div>

<!-- Endpoints Administrativos -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-shield-alt me-2"></i>
            Endpoints Administrativos
        </h5>
    </div>
    <div class="card-body">

        <div class="alert alert-warning">
            <h6><i class="fas fa-exclamation-triangle me-2"></i>Acesso Restrito</h6>
            <p class="mb-0">Estes endpoints são apenas para administradores do sistema.</p>
        </div>

        <!-- Status do Cliente -->
        <div class="api-endpoint">
            <h5>
                <span class="method-badge method-get">GET</span>
                /api/v1/client-status/{key}
            </h5>
            <p><strong>Descrição:</strong> Retorna status de um cliente específico</p>
            <p><strong>Autenticação:</strong> Acesso administrativo</p>
        </div>

        <!-- Suspender Chave -->
        <div class="api-endpoint">
            <h5>
                <span class="method-badge method-post">POST</span>
                /api/v1/suspend-key
            </h5>
            <p><strong>Descrição:</strong> Suspende uma chave de API</p>
            <p><strong>Autenticação:</strong> Chave administrativa</p>
        </div>

        <!-- Ativar Chave -->
        <div class="api-endpoint">
            <h5>
                <span class="method-badge method-post">POST</span>
                /api/v1/activate-key
            </h5>
            <p><strong>Descrição:</strong> Ativa uma chave de API</p>
            <p><strong>Autenticação:</strong> Chave administrativa</p>
        </div>

        <!-- Estatísticas de Uso -->
        <div class="api-endpoint">
            <h5>
                <span class="method-badge method-get">GET</span>
                /api/v1/usage-stats/{key}
            </h5>
            <p><strong>Descrição:</strong> Retorna estatísticas de uso de uma chave</p>
            <p><strong>Autenticação:</strong> Acesso administrativo</p>
        </div>

    </div>
</div>

<!-- Códigos de Erro -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-exclamation-triangle me-2"></i>
            Códigos de Erro
        </h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Código</th>
                        <th>Descrição</th>
                        <th>Causa Comum</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><span class="badge bg-warning">400</span></td>
                        <td>Bad Request</td>
                        <td>Parâmetros inválidos ou ausentes</td>
                    </tr>
                    <tr>
                        <td><span class="badge bg-danger">401</span></td>
                        <td>Unauthorized</td>
                        <td>Chave de API inválida ou expirada</td>
                    </tr>
                    <tr>
                        <td><span class="badge bg-danger">403</span></td>
                        <td>Forbidden</td>
                        <td>Acesso negado</td>
                    </tr>
                    <tr>
                        <td><span class="badge bg-warning">404</span></td>
                        <td>Not Found</td>
                        <td>Endpoint ou recurso não encontrado</td>
                    </tr>
                    <tr>
                        <td><span class="badge bg-warning">429</span></td>
                        <td>Too Many Requests</td>
                        <td>Limite de requisições excedido</td>
                    </tr>
                    <tr>
                        <td><span class="badge bg-danger">500</span></td>
                        <td>Internal Server Error</td>
                        <td>Erro interno do servidor</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Exemplos de Uso -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-code me-2"></i>
            Exemplos de Uso
        </h5>
    </div>
    <div class="card-body">

        <h6>Validação de Chave (Plugin WordPress)</h6>
        <div class="code-block">
// JavaScript (Plugin WordPress)
fetch('<?= url('') ?>/api/v1/validate', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        api_key: 'e1copy_...'
    })
})
.then(response => response.json())
.then(data => {
    if (data.success) {
        console.log('Chave válida:', data.data);
    } else {
        console.error('Chave inválida:', data.message);
    }
});
        </div>

        <h6>Verificação de Status (Dashboard)</h6>
        <div class="code-block">
// PHP (Dashboard)
$response = file_get_contents('<?= url('') ?>/api/v1/status', false, stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => 'Content-Type: application/json',
        'content' => json_encode(['api_key' => 'e1copy_...'])
    ]
]));

$data = json_decode($response, true);
if ($data['success']) {
    echo "Status: " . $data['data']['key_status'];
}
        </div>

    </div>
</div>

<?php
$content = ob_get_clean();
include __DIR__ . '/../layouts/app.php';
?>
