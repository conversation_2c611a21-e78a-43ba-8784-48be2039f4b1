<?php
/**
 * Teste de Configuração após atualização do .env
 * Sistema de Dashboard E1Copy AI
 */

echo "=== TESTE DE CONFIGURAÇÃO ===\n";

// Carregar bootstrap
require_once __DIR__ . '/bootstrap.php';

echo "1. Verificando carregamento do .env...\n";

// Verificar se as variáveis foram carregadas
$envVars = [
    'DB_HOST' => $_ENV['DB_HOST'] ?? 'NÃO DEFINIDO',
    'DB_NAME' => $_ENV['DB_NAME'] ?? 'NÃO DEFINIDO',
    'DB_USER' => $_ENV['DB_USER'] ?? 'NÃO DEFINIDO',
    'DB_PASS' => $_ENV['DB_PASS'] ?? 'NÃO DEFINIDO',
    'APP_URL' => $_ENV['APP_URL'] ?? 'NÃO DEFINIDO',
    'APP_DEBUG' => $_ENV['APP_DEBUG'] ?? 'NÃO DEFINIDO'
];

foreach ($envVars as $key => $value) {
    if ($key === 'DB_PASS') {
        $displayValue = str_repeat('*', strlen($value));
    } else {
        $displayValue = $value;
    }
    echo "   $key: $displayValue\n";
}

echo "\n2. Testando configurações da aplicação...\n";

try {
    $appConfig = config('app');
    echo "   Nome da aplicação: " . $appConfig['name'] . "\n";
    echo "   URL da aplicação: " . $appConfig['url'] . "\n";
    echo "   Debug: " . ($appConfig['debug'] ? 'Ativado' : 'Desativado') . "\n";
    echo "   Timezone: " . $appConfig['timezone'] . "\n";
} catch (Exception $e) {
    echo "   ❌ Erro ao carregar configurações: " . $e->getMessage() . "\n";
}

echo "\n3. Testando configurações do banco de dados...\n";

try {
    $dbConfig = config('database');
    echo "   Host: " . $dbConfig['host'] . "\n";
    echo "   Database: " . $dbConfig['database'] . "\n";
    echo "   Username: " . $dbConfig['username'] . "\n";
    echo "   Password: " . str_repeat('*', strlen($dbConfig['password'])) . "\n";
    echo "   Charset: " . $dbConfig['charset'] . "\n";
} catch (Exception $e) {
    echo "   ❌ Erro ao carregar configurações do banco: " . $e->getMessage() . "\n";
}

echo "\n4. Testando conexão com o banco de dados...\n";

try {
    $db = Database::getInstance();
    $connection = $db->getConnection();
    echo "   ✅ Conexão estabelecida com sucesso!\n";
    
    // Testar uma query simples
    $result = $db->fetch("SELECT 1 as test");
    if ($result && $result['test'] == 1) {
        echo "   ✅ Query de teste executada com sucesso!\n";
    } else {
        echo "   ❌ Falha na query de teste\n";
    }
    
    // Verificar se as tabelas principais existem
    $tables = ['users', 'client_sites', 'api_keys', 'client_posts'];
    echo "   Verificando tabelas:\n";
    
    foreach ($tables as $table) {
        try {
            $result = $db->fetch("SELECT COUNT(*) as count FROM $table LIMIT 1");
            echo "     ✅ $table: " . $result['count'] . " registros\n";
        } catch (Exception $e) {
            echo "     ❌ $table: Erro - " . $e->getMessage() . "\n";
        }
    }
    
} catch (Exception $e) {
    echo "   ❌ Erro na conexão: " . $e->getMessage() . "\n";
}

echo "\n5. Testando função url()...\n";

try {
    $baseUrl = url('');
    $testUrl = url('/test');
    $apiUrl = url('/api/v1/validate');
    
    echo "   Base URL: $baseUrl\n";
    echo "   Test URL: $testUrl\n";
    echo "   API URL: $apiUrl\n";
    
    if (strpos($baseUrl, 'http://localhost/app') !== false) {
        echo "   ✅ URLs sendo geradas corretamente!\n";
    } else {
        echo "   ⚠️  URLs podem não estar corretas\n";
    }
} catch (Exception $e) {
    echo "   ❌ Erro ao gerar URLs: " . $e->getMessage() . "\n";
}

echo "\n6. Testando configurações de upload...\n";

try {
    $uploadConfig = config('upload');
    echo "   Base path: " . $uploadConfig['base_path'] . "\n";
    echo "   Public URL base: " . $uploadConfig['public_url_base'] . "\n";
    
    // Verificar se o diretório de upload existe
    $uploadDir = __DIR__ . $uploadConfig['base_path'];
    if (is_dir($uploadDir)) {
        echo "   ✅ Diretório de upload existe: $uploadDir\n";
        
        if (is_writable($uploadDir)) {
            echo "   ✅ Diretório de upload é gravável\n";
        } else {
            echo "   ⚠️  Diretório de upload não é gravável\n";
        }
    } else {
        echo "   ⚠️  Diretório de upload não existe: $uploadDir\n";
        echo "   Tentando criar...\n";
        
        if (mkdir($uploadDir, 0755, true)) {
            echo "   ✅ Diretório criado com sucesso!\n";
        } else {
            echo "   ❌ Falha ao criar diretório\n";
        }
    }
} catch (Exception $e) {
    echo "   ❌ Erro nas configurações de upload: " . $e->getMessage() . "\n";
}

echo "\n7. Testando APIs ativas...\n";

try {
    // Simular uma requisição para verificar se as rotas estão funcionando
    $_SERVER['REQUEST_METHOD'] = 'GET';
    $_SERVER['REQUEST_URI'] = '/api/v1/health';
    
    echo "   Testando endpoint /api/v1/health...\n";
    
    // Verificar se o controller existe
    if (class_exists('ApiController')) {
        echo "   ✅ ApiController encontrado\n";
        
        $controller = new ApiController();
        if (method_exists($controller, 'healthCheck')) {
            echo "   ✅ Método healthCheck existe\n";
        } else {
            echo "   ❌ Método healthCheck não encontrado\n";
        }
    } else {
        echo "   ❌ ApiController não encontrado\n";
    }
    
} catch (Exception $e) {
    echo "   ❌ Erro ao testar APIs: " . $e->getMessage() . "\n";
}

echo "\n8. Verificando permissões de arquivos...\n";

$files = [
    '.env' => 'Arquivo de configuração',
    'bootstrap.php' => 'Bootstrap da aplicação',
    'index.php' => 'Ponto de entrada',
    '.htaccess' => 'Configurações do Apache'
];

foreach ($files as $file => $description) {
    $filePath = __DIR__ . '/' . $file;
    if (file_exists($filePath)) {
        $perms = substr(sprintf('%o', fileperms($filePath)), -4);
        echo "   ✅ $file ($description): $perms\n";
    } else {
        echo "   ❌ $file não encontrado\n";
    }
}

echo "\n=== RESUMO ===\n";
echo "✅ Configurações do .env carregadas\n";
echo "✅ URL da aplicação: " . config('app.url') . "\n";
echo "✅ Banco de dados: " . config('database.host') . "/" . config('database.database') . "\n";
echo "✅ Debug: " . (config('app.debug') ? 'Ativado' : 'Desativado') . "\n";

echo "\n🎯 PRÓXIMOS PASSOS:\n";
echo "1. Acesse: " . url('/') . "\n";
echo "2. Teste o login no sistema\n";
echo "3. Verifique se as APIs estão funcionando\n";
echo "4. Teste a documentação em: " . url('/admin/api-docs') . "\n";

echo "\n=== TESTE CONCLUÍDO ===\n";
