<?php
require_once "bootstrap.php";
$db = Database::getInstance();

// Verificar post ID 1
$post = $db->fetch("SELECT id FROM client_posts WHERE id = 1");
if ($post) {
    // Gerar URLs das imagens baseado nos arquivos físicos
    $imageUrls = [
        "/app/uploads/posts/1/image_1_1750609221.png",
        "/app/uploads/posts/1/image_2_1750609221.png", 
        "/app/uploads/posts/1/image_3_1750609221.png",
        "/app/uploads/posts/1/image_4_1750609221.png",
        "/app/uploads/posts/1/image_5_1750609221.png"
    ];
    
    $coverUrl = "/app/uploads/posts/1/cover_1750609221.png";
    
    $result = $db->update("client_posts", [
        "product_images" => json_encode($imageUrls),
        "post_cover" => $coverUrl
    ], "id = :id", ["id" => 1]);
    
    if ($result) {
        echo "Post ID 1 atualizado com sucesso!\n";
        
        // Verificar se foi salvo
        $updated = $db->fetch("SELECT product_images, post_cover FROM client_posts WHERE id = 1");
        echo "product_images: " . $updated["product_images"] . "\n";
        echo "post_cover: " . $updated["post_cover"] . "\n";
    } else {
        echo "Erro ao atualizar\n";
    }
} else {
    echo "Post ID 1 não encontrado\n";
}
?>
