<?php
/**
 * Middleware de Autenticação
 * Sistema de Dashboard E1Copy AI
 */

class AuthMiddleware {
    public function handle() {
        if (!Auth::check()) {
            // Se for uma requisição AJAX, retornar JSON
            if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
                strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
                http_response_code(401);
                header('Content-Type: application/json');
                echo json_encode(['error' => 'Não autenticado']);
                exit;
            }
            
            // Salvar URL de destino para redirecionamento após login
            $_SESSION['intended_url'] = $_SERVER['REQUEST_URI'];
            
            // Redirecionar para login
            redirect(url('/login'));
        }
        
        return true;
    }
}
