<?php
/**
 * Script para corrigir URLs de imagens com diretórios temporários
 */

// Incluir configuração do banco
$dbConfig = require 'config/database.php';

// Conectar ao banco
try {
    $pdo = new PDO(
        "mysql:host=" . $dbConfig['host'] . ";dbname=" . $dbConfig['database'] . ";charset=utf8mb4",
        $dbConfig['username'],
        $dbConfig['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    
    echo "=== CORRIGINDO URLs DE IMAGENS ===\n";

    // Buscar posts com URLs temporárias
    $stmt = $pdo->prepare("
        SELECT id, product_images, post_cover
        FROM client_posts
        WHERE product_images LIKE '%temp_%' OR post_cover LIKE '%temp_%'
    ");
    $stmt->execute();
    $postsWithTempUrls = $stmt->fetchAll();
    
    echo "Posts encontrados com URLs temporárias: " . count($postsWithTempUrls) . "\n\n";
    
    foreach ($postsWithTempUrls as $post) {
        echo "Corrigindo post ID: " . $post['id'] . "\n";
        
        $updateData = [];
        
        // Corrigir URLs das imagens do produto
        if (!empty($post['product_images'])) {
            $productImages = json_decode($post['product_images'], true);
            if (is_array($productImages)) {
                $correctedImages = [];
                
                foreach ($productImages as $imageUrl) {
                    // Extrair nome do arquivo da URL temporária
                    if (preg_match('/\/temp_\d+_\d+\/(.+)$/', $imageUrl, $matches)) {
                        $fileName = $matches[1];
                        $correctedUrl = '/uploads/posts/' . $post['id'] . '/' . $fileName;
                        $correctedImages[] = $correctedUrl;
                        echo "  - Imagem corrigida: $imageUrl -> $correctedUrl\n";
                    } else {
                        // Se não é URL temporária, manter como está
                        $correctedImages[] = $imageUrl;
                    }
                }
                
                if (!empty($correctedImages)) {
                    $updateData['product_images'] = json_encode($correctedImages);
                }
            }
        }
        
        // Corrigir URL da capa
        if (!empty($post['post_cover'])) {
            if (preg_match('/\/temp_\d+_\d+\/(.+)$/', $post['post_cover'], $matches)) {
                $fileName = $matches[1];
                $correctedUrl = '/uploads/posts/' . $post['id'] . '/' . $fileName;
                $updateData['post_cover'] = $correctedUrl;
                echo "  - Capa corrigida: " . $post['post_cover'] . " -> $correctedUrl\n";
            }
        }
        
        // Atualizar no banco
        if (!empty($updateData)) {
            $setParts = [];
            $params = ['id' => $post['id']];

            foreach ($updateData as $key => $value) {
                $setParts[] = "$key = :$key";
                $params[$key] = $value;
            }

            $sql = "UPDATE client_posts SET " . implode(', ', $setParts) . " WHERE id = :id";
            $stmt = $pdo->prepare($sql);
            $result = $stmt->execute($params);
            echo "  - Update no banco: " . ($result ? 'SUCCESS' : 'FAILED') . "\n";
        }
        
        echo "\n";
    }
    
    echo "=== CORREÇÃO CONCLUÍDA ===\n";
    
    // Verificar se ainda há URLs temporárias
    $stmt = $pdo->prepare("
        SELECT id, product_images, post_cover
        FROM client_posts
        WHERE product_images LIKE '%temp_%' OR post_cover LIKE '%temp_%'
    ");
    $stmt->execute();
    $remainingTempUrls = $stmt->fetchAll();

    echo "URLs temporárias restantes: " . count($remainingTempUrls) . "\n";
    
} catch (Exception $e) {
    echo "ERRO: " . $e->getMessage() . "\n";
}
