<?php

class ClientPostsController extends Controller {
    
    /**
     * Lista os posts do cliente
     */
    public function index() {
        $userId = $_SESSION['user_id'];

        // Limpar posts concluídos antigos (manter apenas os últimos 20)
        $this->cleanupCompletedPosts($userId);

        $search = $this->input('search', '');
        $status = $this->input('status', '');
        $postType = $this->input('post_type', '');
        $page = max(1, (int)$this->input('page', 1));
        $perPage = 10;
        $offset = ($page - 1) * $perPage;
        
        // Construir condições da query
        $conditions = ['cp.user_id = :user_id'];
        $params = ['user_id' => $userId];
        
        if ($search) {
            $conditions[] = "(cp.title LIKE :search OR cp.content LIKE :search OR cp.keywords LIKE :search)";
            $params['search'] = "%{$search}%";
        }
        
        if ($status) {
            $conditions[] = "cp.status = :status";
            $params['status'] = $status;
        }
        
        if ($postType) {
            $conditions[] = "cp.post_type = :post_type";
            $params['post_type'] = $postType;
        }
        
        $whereClause = implode(' AND ', $conditions);
        
        // Buscar posts
        $posts = $this->db->fetchAll("
            SELECT 
                cp.*,
                cs.site_name,
                cs.site_url
            FROM client_posts cp
            LEFT JOIN client_sites cs ON cp.site_id = cs.id
            WHERE {$whereClause}
            ORDER BY cp.created_at DESC
            LIMIT {$perPage} OFFSET {$offset}
        ", $params);
        
        // Contar total
        $totalPosts = $this->db->fetch("
            SELECT COUNT(*) as total
            FROM client_posts cp
            WHERE {$whereClause}
        ", $params)['total'];
        
        // Buscar sites do usuário
        $sites = $this->db->fetchAll("
            SELECT cs.id, cs.site_name, cs.site_url, cs.status
            FROM client_sites cs
            WHERE cs.user_id = :user_id AND cs.status = 'connected'
            ORDER BY cs.site_name
        ", ['user_id' => $userId]);
        
        // Estatísticas
        $stats = $this->db->fetch("
            SELECT 
                COUNT(*) as total,
                SUM(CASE WHEN status = 'draft' THEN 1 ELSE 0 END) as drafts,
                SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
                SUM(CASE WHEN status = 'published' THEN 1 ELSE 0 END) as published,
                SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed
            FROM client_posts
            WHERE user_id = :user_id
        ", ['user_id' => $userId]);
        
        $totalPages = ceil($totalPosts / $perPage);
        
        $this->view('client.posts.index', [
            'posts' => $posts,
            'sites' => $sites,
            'stats' => $stats,
            'search' => $search,
            'status' => $status,
            'postType' => $postType,
            'page' => $page,
            'totalPages' => $totalPages,
            'totalPosts' => $totalPosts
        ]);
    }
    
    /**
     * Mostra formulário para criar novo post
     */
    public function create() {
        $userId = $_SESSION['user_id'];
        
        // Buscar sites do usuário
        $sites = $this->db->fetchAll("
            SELECT cs.id, cs.site_name, cs.site_url, cs.status
            FROM client_sites cs
            WHERE cs.user_id = :user_id AND cs.status = 'connected'
            ORDER BY cs.site_name
        ", ['user_id' => $userId]);
        
        if (empty($sites)) {
            $this->redirect('/client/sites?error=' . urlencode('Você precisa ter pelo menos um site conectado para criar posts.'));
            return;
        }
        
        $this->view('client.posts.create', [
            'sites' => $sites
        ]);
    }
    
    /**
     * Mostra formulário específico baseado no tipo de post
     */
    public function createForm() {
        $postType = $this->input('type');
        $siteId = $this->input('site_id');
        $userId = $_SESSION['user_id'];
        
        if (!in_array($postType, ['article', 'product_review'])) {
            $this->redirect('/client/posts/create?error=' . urlencode('Tipo de post inválido.'));
            return;
        }
        
        // Verificar se o site pertence ao usuário
        $site = $this->db->fetch("
            SELECT * FROM client_sites 
            WHERE id = :site_id AND user_id = :user_id AND status = 'connected'
        ", ['site_id' => $siteId, 'user_id' => $userId]);
        
        if (!$site) {
            $this->redirect('/client/posts/create?error=' . urlencode('Site não encontrado ou não conectado.'));
            return;
        }
        
        $this->view('client.posts.form', [
            'postType' => $postType,
            'site' => $site
        ]);
    }
    
    /**
     * Salva o post
     */
    public function store() {
        try {
            $userId = $_SESSION['user_id'];
            $postType = $this->input('post_type');
            $siteId = $this->input('site_id');
            $action = $this->input('action', 'save_draft'); // save_draft ou publish
            
            // Validações básicas
            if (!in_array($postType, ['article', 'product_review'])) {
                throw new Exception('Tipo de post inválido.');
            }
            
            // Verificar se o site pertence ao usuário
            $site = $this->db->fetch("
                SELECT * FROM client_sites 
                WHERE id = :site_id AND user_id = :user_id AND status = 'connected'
            ", ['site_id' => $siteId, 'user_id' => $userId]);
            
            if (!$site) {
                throw new Exception('Site não encontrado ou não conectado.');
            }
            
            // Dados comuns
            $data = [
                'site_id' => $siteId,
                'user_id' => $userId,
                'post_type' => $postType,
                'title' => $this->input('title'),
                'content' => $this->input('content'),
                'excerpt' => $this->input('excerpt'),
                'slug' => $this->generateSlug($this->input('title')),
                'status' => $action === 'publish' ? 'pending' : 'draft'
            ];
            
            // Validações específicas por tipo de post
            if ($postType === 'article') {
                if (empty($data['title'])) {
                    throw new Exception('Título é obrigatório.');
                }

                if (empty($data['content'])) {
                    throw new Exception('Conteúdo é obrigatório.');
                }
            }
            
            // Campos específicos para product review
            if ($postType === 'product_review') {
                $data['product_name'] = $this->input('product_name');
                $data['product_description'] = $this->input('product_description');
                $data['keyword'] = $this->input('keyword');
                $data['category'] = $this->input('category');
                $existingTags = $this->input('existing_tags');
                $data['existing_tags'] = is_array($existingTags) ? implode(', ', array_filter($existingTags)) : $existingTags;
                $data['youtube_video'] = $this->input('youtube_video');

                // Gerar título automaticamente a partir do nome do produto
                $data['title'] = $data['product_name'] . ' - Review Completo';
                $data['content'] = $data['product_description']; // Usar descrição como conteúdo inicial


                // Processar imagens do produto (arquivos enviados)
                $productImages = [];
                if (isset($_FILES['product_images'])) {
                    error_log("=== UPLOAD DEBUG ===");
                    error_log("FILES array: " . print_r($_FILES['product_images'], true));

                    // Criar diretório para o post (será criado após inserir no banco)
                    $tempImages = [];
                    for ($i = 0; $i < count($_FILES['product_images']['name']); $i++) {
                        error_log("Processando imagem $i:");
                        error_log("  Nome: " . $_FILES['product_images']['name'][$i]);
                        error_log("  Erro: " . $_FILES['product_images']['error'][$i]);
                        error_log("  Tamanho: " . $_FILES['product_images']['size'][$i]);

                        if ($_FILES['product_images']['error'][$i] === UPLOAD_ERR_OK && !empty($_FILES['product_images']['name'][$i])) {
                            $tempImages[] = [
                                'name' => $_FILES['product_images']['name'][$i],
                                'tmp_name' => $_FILES['product_images']['tmp_name'][$i],
                                'type' => $_FILES['product_images']['type'][$i],
                                'size' => $_FILES['product_images']['size'][$i]
                            ];
                            error_log("  ✓ Imagem válida adicionada");
                        } else {
                            error_log("  ✗ Imagem inválida ou vazia");
                        }
                    }
                    $_SESSION['temp_images'] = $tempImages; // Armazenar temporariamente
                    error_log("Total de imagens válidas: " . count($tempImages));
                    error_log("=== FIM UPLOAD DEBUG ===");
                }
                $data['product_images'] = null; // Será preenchido após upload

                // Validações obrigatórias
                if (empty($data['product_name'])) {
                    throw new Exception('Nome do produto é obrigatório para reviews.');
                }

                if (empty($data['product_description'])) {
                    throw new Exception('Descrição do produto é obrigatória para reviews.');
                }

                if (empty($data['keyword'])) {
                    throw new Exception('Palavra-chave principal é obrigatória para reviews.');
                }

                if (empty($data['category'])) {
                    throw new Exception('Categoria é obrigatória para reviews.');
                }

                // Validar imagens mínimas (contar apenas arquivos válidos)
                $validImageCount = 0;
                if (isset($_FILES['product_images']) && isset($_SESSION['temp_images'])) {
                    $validImageCount = count($_SESSION['temp_images']);
                }

                error_log("Validação de imagens na criação - Count: $validImageCount");

                // Temporariamente removendo validação de 5 imagens para debug
                // if ($validImageCount < 5) {
                //     throw new Exception('São necessárias pelo menos 5 imagens do produto.');
                // }
            }
            
            // Inserir no banco
            $postId = $this->db->insert('client_posts', $data);

            // Processar upload de imagens após criar o post
            if ($postType === 'product_review') {
                $updateData = [];

                // Upload das imagens do produto
                if (isset($_SESSION['temp_images'])) {
                    error_log("=== UPLOAD PARA DISCO ===");
                    error_log("Post ID: $postId");
                    error_log("Temp images: " . print_r($_SESSION['temp_images'], true));

                    $uploadedImages = $this->uploadPostImages($postId, $_SESSION['temp_images']);

                    error_log("URLs das imagens uploadadas: " . json_encode($uploadedImages));
                    error_log("=== FIM UPLOAD PARA DISCO ===");

                    $updateData['product_images'] = json_encode($uploadedImages);
                    unset($_SESSION['temp_images']);
                }

                // Upload da capa do post
                if (isset($_FILES['post_cover']) && $_FILES['post_cover']['error'] === UPLOAD_ERR_OK) {
                    $coverUrl = $this->uploadPostCover($postId, $_FILES['post_cover']);
                    if ($coverUrl) {
                        $updateData['post_cover'] = $coverUrl;
                    }
                }

                // Atualizar post com URLs das imagens
                if (!empty($updateData)) {
                    $this->db->update('client_posts', $updateData, 'id = :id', ['id' => $postId]);
                }
            }

            $message = $action === 'publish'
                ? 'Post criado e enviado para publicação!'
                : 'Rascunho salvo com sucesso!';

            $this->redirect('/client/posts?success=' . urlencode($message));
            
        } catch (Exception $e) {
            // Redirecionar de volta ao formulário com os dados
            $redirectUrl = '/client/posts/create/form?type=' . urlencode($postType) . '&site_id=' . urlencode($siteId) . '&error=' . urlencode($e->getMessage());
            $this->redirect($redirectUrl);
        }
    }
    
    /**
     * Mostra detalhes do post
     */
    public function show($id) {
        $userId = $_SESSION['user_id'];
        
        $post = $this->db->fetch("
            SELECT 
                cp.*,
                cs.site_name,
                cs.site_url
            FROM client_posts cp
            LEFT JOIN client_sites cs ON cp.site_id = cs.id
            WHERE cp.id = :id AND cp.user_id = :user_id
        ", ['id' => $id, 'user_id' => $userId]);
        
        if (!$post) {
            $this->redirect('/client/posts?error=' . urlencode('Post não encontrado.'));
            return;
        }
        
        $this->view('client.posts.show', [
            'post' => $post
        ]);
    }
    
    /**
     * Edita o post
     */
    public function edit($id) {
        $userId = $_SESSION['user_id'];
        
        $post = $this->db->fetch("
            SELECT
                cp.*,
                cs.site_name,
                cs.site_url
            FROM client_posts cp
            LEFT JOIN client_sites cs ON cp.site_id = cs.id
            WHERE cp.id = :id AND cp.user_id = :user_id
        ", ['id' => $id, 'user_id' => $userId]);

        // Log de debug para verificar dados do post
        error_log("Post data for edit - ID: $id");
        error_log("Product images: " . ($post['product_images'] ?? 'NULL'));
        error_log("Post cover: " . ($post['post_cover'] ?? 'NULL'));
        
        if (!$post) {
            $this->redirect('/client/posts?error=' . urlencode('Post não encontrado.'));
            return;
        }
        
        // Não permitir edição de posts já publicados
        if (in_array($post['status'], ['completed', 'published'])) {
            $this->redirect('/client/posts?error=' . urlencode('Posts publicados não podem ser editados.'));
            return;
        }
        
        // Buscar sites do usuário
        $sites = $this->db->fetchAll("
            SELECT cs.id, cs.site_name, cs.site_url, cs.status
            FROM client_sites cs
            WHERE cs.user_id = :user_id AND cs.status = 'connected'
            ORDER BY cs.site_name
        ", ['user_id' => $userId]);
        
        $this->view('client.posts.edit', [
            'post' => $post,
            'sites' => $sites
        ]);
    }
    
    /**
     * Atualiza o post
     */
    public function update($id) {
        try {
            $userId = $_SESSION['user_id'];
            $action = $this->input('action', 'save_draft');

            // Debug logs
            error_log("=== UPDATE POST DEBUG ===");
            error_log("Post ID: $id");
            error_log("User ID: $userId");
            error_log("Action: $action");
            if (isset($_FILES['product_images'])) {
                error_log("FILES product_images: " . print_r($_FILES['product_images'], true));
            }
            if (isset($_FILES['post_cover'])) {
                error_log("FILES post_cover: " . print_r($_FILES['post_cover'], true));
            }

            // Verificar se o post existe e pertence ao usuário
            $post = $this->db->fetch("
                SELECT * FROM client_posts
                WHERE id = :id AND user_id = :user_id
            ", ['id' => $id, 'user_id' => $userId]);

            if (!$post) {
                throw new Exception('Post não encontrado.');
            }

            error_log("Post encontrado: " . $post['title']);
            error_log("Post type: " . $post['post_type']);

            // Não permitir edição de posts já publicados
            if (in_array($post['status'], ['completed', 'published'])) {
                throw new Exception('Posts publicados não podem ser editados.');
            }
            
            // Dados para atualização
            $data = [
                'title' => $this->input('title'),
                'content' => $this->input('content'),
                'excerpt' => $this->input('excerpt'),
                'slug' => $this->generateSlug($this->input('title')),
                'status' => $action === 'publish' ? 'pending' : 'draft'
            ];
            
            // Validações específicas por tipo de post
            if ($post['post_type'] === 'article') {
                if (empty($data['title'])) {
                    throw new Exception('Título é obrigatório.');
                }

                if (empty($data['content'])) {
                    throw new Exception('Conteúdo é obrigatório.');
                }
            }
            
            // Campos específicos para product review
            if ($post['post_type'] === 'product_review') {
                $data['product_name'] = $this->input('product_name');
                $data['product_description'] = $this->input('product_description');
                $data['keyword'] = $this->input('keyword');
                $data['category'] = $this->input('category');
                $existingTags = $this->input('existing_tags');
                $data['existing_tags'] = is_array($existingTags) ? implode(', ', array_filter($existingTags)) : $existingTags;
                $data['youtube_video'] = $this->input('youtube_video');

                // Gerar título automaticamente a partir do nome do produto
                $data['title'] = $data['product_name'] . ' - Review Completo';
                $data['content'] = $data['product_description']; // Usar descrição como conteúdo inicial


                // Processar imagens do produto (arquivos enviados)
                error_log("Verificando upload de imagens do produto...");
                error_log("isset(\$_FILES['product_images']): " . (isset($_FILES['product_images']) ? 'SIM' : 'NÃO'));
                if (isset($_FILES['product_images'])) {
                    error_log("product_images name[0]: " . ($_FILES['product_images']['name'][0] ?? 'VAZIO'));
                    error_log("product_images empty check: " . (empty($_FILES['product_images']['name'][0]) ? 'VAZIO' : 'NÃO VAZIO'));
                }

                if (isset($_FILES['product_images']) && !empty($_FILES['product_images']['name'][0])) {
                    error_log("Processando upload de imagens do produto...");
                    // Há novas imagens para upload
                    $tempImages = [];
                    for ($i = 0; $i < count($_FILES['product_images']['name']); $i++) {
                        error_log("Imagem $i: " . $_FILES['product_images']['name'][$i] . " (erro: " . $_FILES['product_images']['error'][$i] . ")");
                        if ($_FILES['product_images']['error'][$i] === UPLOAD_ERR_OK) {
                            $tempImages[] = [
                                'name' => $_FILES['product_images']['name'][$i],
                                'tmp_name' => $_FILES['product_images']['tmp_name'][$i],
                                'type' => $_FILES['product_images']['type'][$i],
                                'size' => $_FILES['product_images']['size'][$i]
                            ];
                        }
                    }

                    error_log("Total de imagens válidas: " . count($tempImages));

                    if (!empty($tempImages)) {
                        error_log("Removendo imagens antigas...");
                        // Remover imagens antigas
                        $this->deletePostImages($id, $post['product_images'], $post['post_cover']);

                        error_log("Fazendo upload de novas imagens...");
                        // Upload novas imagens
                        $uploadedImages = $this->uploadPostImages($id, $tempImages);
                        $data['product_images'] = json_encode($uploadedImages);
                        error_log("URLs das novas imagens: " . json_encode($uploadedImages));
                    }
                } else {
                    error_log("Nenhuma nova imagem para upload");
                }

                // Processar upload da capa do post
                if (isset($_FILES['post_cover']) && $_FILES['post_cover']['error'] === UPLOAD_ERR_OK) {
                    // Remover capa antiga se existir
                    if (!empty($post['post_cover'])) {
                        $oldCoverPath = str_replace('/app/uploads/posts/', '', $post['post_cover']);
                        $fullPath = __DIR__ . '/../uploads/posts/' . $oldCoverPath;
                        if (file_exists($fullPath)) {
                            unlink($fullPath);
                        }
                    }

                    // Upload nova capa
                    $coverUrl = $this->uploadPostCover($id, $_FILES['post_cover']);
                    if ($coverUrl) {
                        $data['post_cover'] = $coverUrl;
                    }
                }
                // Se não há nova capa, manter a existente

                // Validações obrigatórias
                if (empty($data['product_name'])) {
                    throw new Exception('Nome do produto é obrigatório para reviews.');
                }

                if (empty($data['product_description'])) {
                    throw new Exception('Descrição do produto é obrigatória para reviews.');
                }

                if (empty($data['keyword'])) {
                    throw new Exception('Palavra-chave principal é obrigatória para reviews.');
                }

                if (empty($data['category'])) {
                    throw new Exception('Categoria é obrigatória para reviews.');
                }

                // Validar imagens apenas se há novas imagens sendo enviadas
                if (isset($_FILES['product_images']) && !empty($_FILES['product_images']['name'][0])) {
                    $validImageCount = 0;
                    for ($i = 0; $i < count($_FILES['product_images']['name']); $i++) {
                        if ($_FILES['product_images']['error'][$i] === UPLOAD_ERR_OK &&
                            !empty($_FILES['product_images']['name'][$i])) {
                            $validImageCount++;
                        }
                    }

                    error_log("Validação de imagens na edição - Count: $validImageCount");

                    // Temporariamente removendo validação de 5 imagens para debug
                    // if ($validImageCount < 5) {
                    //     throw new Exception('São necessárias pelo menos 5 imagens do produto.');
                    // }
                }
                // Se não há novas imagens, manter as existentes (não validar)
            }
            
            // Atualizar no banco
            error_log("Dados para atualização: " . print_r($data, true));
            $result = $this->db->update('client_posts', $data, 'id = :id', ['id' => $id]);
            error_log("Resultado da atualização: " . ($result ? 'SUCESSO' : 'FALHA'));
            error_log("=== FIM UPDATE POST DEBUG ===");

            $message = $action === 'publish'
                ? 'Post atualizado e enviado para publicação!'
                : 'Rascunho atualizado com sucesso!';

            $this->redirect('/client/posts?success=' . urlencode($message));
            
        } catch (Exception $e) {
            $this->redirect('/client/posts/' . $id . '/edit?error=' . urlencode($e->getMessage()));
        }
    }
    
    /**
     * Exclui o post
     */
    public function delete($id) {
        try {
            $userId = $_SESSION['user_id'];

            // Log de debug temporário
            error_log("DELETE method called for post ID: $id by user ID: $userId");
            
            // Verificar se o post existe e pertence ao usuário
            $post = $this->db->fetch("
                SELECT * FROM client_posts 
                WHERE id = :id AND user_id = :user_id
            ", ['id' => $id, 'user_id' => $userId]);
            
            if (!$post) {
                throw new Exception('Post não encontrado.');
            }
            
            // Não permitir exclusão de posts já publicados
            if (in_array($post['status'], ['completed', 'published'])) {
                throw new Exception('Posts publicados não podem ser excluídos.');
            }

            // Remover imagens associadas ao post
            if ($post['post_type'] === 'product_review') {
                $this->deletePostImages($post['id'], $post['product_images'], $post['post_cover']);
            }

            // Excluir
            $this->db->delete('client_posts', 'id = :id', ['id' => $id]);
            
            $this->redirect('/client/posts?success=' . urlencode('Post excluído com sucesso!'));
            
        } catch (Exception $e) {
            $this->redirect('/client/posts?error=' . urlencode($e->getMessage()));
        }
    }
    
    /**
     * Publica o post (muda status para pending)
     */
    public function publish($id) {
        try {
            $userId = $_SESSION['user_id'];

            // Verificar se o post existe e pertence ao usuário
            $post = $this->db->fetch("
                SELECT * FROM client_posts
                WHERE id = :id AND user_id = :user_id
            ", ['id' => $id, 'user_id' => $userId]);

            if (!$post) {
                throw new Exception('Post não encontrado.');
            }

            // Só pode publicar rascunhos ou posts que falharam
            if (!in_array($post['status'], ['draft', 'failed'])) {
                throw new Exception('Apenas rascunhos ou posts que falharam podem ser publicados.');
            }

            // Atualizar status
            $this->db->update('client_posts', ['status' => 'pending'], 'id = :id', ['id' => $id]);

            $this->redirect('/client/posts?success=' . urlencode('Post enviado para publicação!'));

        } catch (Exception $e) {
            $this->redirect('/client/posts?error=' . urlencode($e->getMessage()));
        }
    }

    /**
     * Gera slug a partir do título
     */
    private function generateSlug($title) {
        $slug = strtolower($title);
        $slug = preg_replace('/[^a-z0-9\s-]/', '', $slug);
        $slug = preg_replace('/[\s-]+/', '-', $slug);
        $slug = trim($slug, '-');
        return $slug;
    }

    /**
     * Limpa posts concluídos antigos, mantendo apenas os últimos 20
     */
    private function cleanupCompletedPosts($userId) {
        try {
            // Buscar posts concluídos do usuário, ordenados por data de conclusão (mais recentes primeiro)
            $completedPosts = $this->db->fetchAll("
                SELECT id, product_images
                FROM client_posts
                WHERE user_id = :user_id AND status = 'completed'
                ORDER BY updated_at DESC
            ", ['user_id' => $userId]);

            // Se há mais de 20 posts concluídos, remover os mais antigos
            if (count($completedPosts) > 20) {
                $postsToDelete = array_slice($completedPosts, 20); // Pegar posts além dos 20 primeiros

                foreach ($postsToDelete as $post) {
                    // Remover imagens associadas ao post
                    $this->deletePostImages($post['id'], $post['product_images'], $post['post_cover']);

                    // Remover post do banco
                    $this->db->delete('client_posts', 'id = :id', ['id' => $post['id']]);
                }
            }
        } catch (Exception $e) {
            // Log do erro, mas não interromper o fluxo principal
            error_log("Erro na limpeza de posts concluídos: " . $e->getMessage());
        }
    }

    /**
     * Faz upload das imagens do post
     */
    private function uploadPostImages($postId, $tempImages) {
        $uploadedImages = [];

        // Obter configurações de upload do .env
        $uploadPath = $_ENV['UPLOAD_PATH'] ?? '/uploads';
        $uploadUrlPrefix = $_ENV['UPLOAD_URL_PREFIX'] ?? '/uploads';

        // Definir o caminho físico correto para salvar os arquivos
        if (isset($_SERVER['DOCUMENT_ROOT']) && !empty($_SERVER['DOCUMENT_ROOT'])) {
            $uploadDir = $_SERVER['DOCUMENT_ROOT'] . $uploadPath . '/posts/' . $postId;
        } else {
            // Fallback para caminho relativo
            $uploadDir = __DIR__ . '/../uploads/posts/' . $postId;
        }

        error_log("=== uploadPostImages ===");
        error_log("Upload dir: $uploadDir");
        error_log("Temp images count: " . count($tempImages));

        // Criar diretório se não existir
        if (!is_dir($uploadDir)) {
            $created = mkdir($uploadDir, 0755, true);
            error_log("Diretório criado: " . ($created ? 'SIM' : 'NÃO'));
        } else {
            error_log("Diretório já existe");
        }

        foreach ($tempImages as $index => $image) {
            try {
                error_log("Processando imagem $index: " . $image['name']);

                // Validar tipo de arquivo
                $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
                if (!in_array($image['type'], $allowedTypes)) {
                    error_log("Tipo não permitido: " . $image['type']);
                    continue;
                }

                // Validar tamanho (máximo 5MB)
                if ($image['size'] > 5 * 1024 * 1024) {
                    error_log("Arquivo muito grande: " . $image['size']);
                    continue;
                }

                // Gerar nome único para o arquivo
                $extension = pathinfo($image['name'], PATHINFO_EXTENSION);
                $fileName = 'image_' . ($index + 1) . '_' . time() . '.' . $extension;
                $filePath = $uploadDir . '/' . $fileName;

                error_log("Tentando mover de: " . $image['tmp_name']);
                error_log("Para: $filePath");

                // Mover arquivo
                if (move_uploaded_file($image['tmp_name'], $filePath)) {
                    // Gerar URL pública para a imagem usando configuração do .env
                    $publicUrl = $uploadUrlPrefix . '/posts/' . $postId . '/' . $fileName;
                    $uploadedImages[] = $publicUrl;
                    error_log("✓ Upload bem-sucedido: $publicUrl");
                } else {
                    error_log("✗ Falha no move_uploaded_file");
                }
            } catch (Exception $e) {
                error_log("Erro no upload da imagem {$image['name']}: " . $e->getMessage());
            }
        }

        error_log("Total de imagens uploadadas: " . count($uploadedImages));
        error_log("=== fim uploadPostImages ===");

        return $uploadedImages;
    }

    /**
     * Faz upload da capa do post
     */
    private function uploadPostCover($postId, $coverFile) {
        try {
            // Obter configurações de upload do .env
            $uploadPath = $_ENV['UPLOAD_PATH'] ?? '/uploads';
            $uploadUrlPrefix = $_ENV['UPLOAD_URL_PREFIX'] ?? '/uploads';

            // Definir o caminho físico correto para salvar os arquivos
            if (isset($_SERVER['DOCUMENT_ROOT']) && !empty($_SERVER['DOCUMENT_ROOT'])) {
                $uploadDir = $_SERVER['DOCUMENT_ROOT'] . $uploadPath . '/posts/' . $postId;
            } else {
                // Fallback para caminho relativo
                $uploadDir = __DIR__ . '/../uploads/posts/' . $postId;
            }

            // Criar diretório se não existir
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0755, true);
            }

            // Validar tipo de arquivo
            $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
            if (!in_array($coverFile['type'], $allowedTypes)) {
                return null;
            }

            // Validar tamanho (máximo 5MB)
            if ($coverFile['size'] > 5 * 1024 * 1024) {
                return null;
            }

            // Gerar nome único para o arquivo
            $extension = pathinfo($coverFile['name'], PATHINFO_EXTENSION);
            $fileName = 'cover_' . time() . '.' . $extension;
            $filePath = $uploadDir . '/' . $fileName;

            // Mover arquivo
            if (move_uploaded_file($coverFile['tmp_name'], $filePath)) {
                // Gerar URL pública para a imagem usando configuração do .env
                return $uploadUrlPrefix . '/posts/' . $postId . '/' . $fileName;
            }

            return null;
        } catch (Exception $e) {
            error_log("Erro no upload da capa: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Remove imagens associadas a um post
     */
    private function deletePostImages($postId, $productImagesJson, $postCover = null) {
        try {
            // Obter configurações de upload do .env
            $uploadPath = $_ENV['UPLOAD_PATH'] ?? '/uploads';
            $uploadUrlPrefix = $_ENV['UPLOAD_URL_PREFIX'] ?? '/uploads';

            // Remover imagens do produto
            if (!empty($productImagesJson)) {
                $productImages = json_decode($productImagesJson, true);
                if (is_array($productImages)) {
                    foreach ($productImages as $imageUrl) {
                        // Converter URL para caminho do arquivo (suporta ambos os formatos)
                        $imagePath = str_replace([$uploadUrlPrefix . '/posts/', '/app/uploads/posts/'], '', $imageUrl);

                        // Definir caminho físico
                        if (isset($_SERVER['DOCUMENT_ROOT']) && !empty($_SERVER['DOCUMENT_ROOT'])) {
                            $fullPath = $_SERVER['DOCUMENT_ROOT'] . $uploadPath . '/posts/' . $imagePath;
                        } else {
                            $fullPath = __DIR__ . '/../uploads/posts/' . $imagePath;
                        }

                        if (file_exists($fullPath)) {
                            unlink($fullPath);
                        }
                    }
                }
            }

            // Remover capa do post
            if (!empty($postCover)) {
                $coverPath = str_replace([$uploadUrlPrefix . '/posts/', '/app/uploads/posts/'], '', $postCover);

                // Definir caminho físico
                if (isset($_SERVER['DOCUMENT_ROOT']) && !empty($_SERVER['DOCUMENT_ROOT'])) {
                    $fullPath = $_SERVER['DOCUMENT_ROOT'] . $uploadPath . '/posts/' . $coverPath;
                } else {
                    $fullPath = __DIR__ . '/../uploads/posts/' . $coverPath;
                }

                if (file_exists($fullPath)) {
                    unlink($fullPath);
                }
            }

            // Remover diretório do post se existir e estiver vazio
            if (isset($_SERVER['DOCUMENT_ROOT']) && !empty($_SERVER['DOCUMENT_ROOT'])) {
                $postDir = $_SERVER['DOCUMENT_ROOT'] . $uploadPath . '/posts/' . $postId;
            } else {
                $postDir = __DIR__ . '/../uploads/posts/' . $postId;
            }

            if (is_dir($postDir) && count(scandir($postDir)) == 2) { // apenas . e ..
                rmdir($postDir);
            }
        } catch (Exception $e) {
            error_log("Erro ao remover imagens do post {$postId}: " . $e->getMessage());
        }
    }
}
