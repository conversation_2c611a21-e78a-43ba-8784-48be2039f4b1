<?php
/**
 * Script de Teste do Sistema
 * Sistema de Dashboard E1Copy AI
 */

echo "=== E1Copy AI Dashboard - Teste do Sistema ===\n\n";

// Carregar bootstrap
require_once __DIR__ . '/bootstrap.php';

try {
    echo "1. Testando configurações...\n";
    echo "   - APP_URL: " . config('app.url') . "\n";
    echo "   - APP_DEBUG: " . (config('app.debug') ? 'true' : 'false') . "\n";
    echo "   - DB_HOST: " . config('database.host') . "\n";
    echo "   - DB_NAME: " . config('database.database') . "\n";
    echo "   ✓ Configurações carregadas!\n\n";
    
    echo "2. Testando conexão com banco de dados...\n";
    $db = Database::getInstance();
    $pdo = $db->getConnection();
    echo "   ✓ Conexão estabelecida!\n\n";
    
    echo "3. Testando tabelas do banco...\n";
    $tables = ['users', 'plans', 'subscriptions', 'api_keys', 'api_usage', 'settings', 'login_attempts'];
    
    foreach ($tables as $table) {
        try {
            $result = $db->fetch("SELECT COUNT(*) as count FROM {$table}");
            echo "   ✓ Tabela '{$table}': {$result['count']} registros\n";
        } catch (Exception $e) {
            echo "   ❌ Tabela '{$table}': ERRO - " . $e->getMessage() . "\n";
        }
    }
    
    echo "\n4. Testando usuário administrador...\n";
    $admin = $db->fetch("SELECT * FROM users WHERE email = '<EMAIL>' AND role = 'admin'");
    if ($admin) {
        echo "   ✓ Usuário admin encontrado: {$admin['name']} ({$admin['email']})\n";
        echo "   ✓ Status: {$admin['status']}\n";
    } else {
        echo "   ❌ Usuário admin não encontrado!\n";
    }
    
    echo "\n5. Testando planos...\n";
    $plans = $db->fetchAll("SELECT * FROM plans WHERE status = 'active'");
    echo "   ✓ Planos ativos encontrados: " . count($plans) . "\n";
    foreach ($plans as $plan) {
        echo "     - {$plan['name']}: R$ {$plan['price']}\n";
    }
    
    echo "\n6. Testando configurações...\n";
    $settings = $db->fetchAll("SELECT * FROM settings");
    echo "   ✓ Configurações encontradas: " . count($settings) . "\n";
    foreach ($settings as $setting) {
        $value = $setting['value'] ?: '(vazio)';
        if (strlen($value) > 50) {
            $value = substr($value, 0, 47) . '...';
        }
        echo "     - {$setting['key_name']}: {$value}\n";
    }
    
    echo "\n7. Testando sistema de autenticação...\n";
    // Simular verificação de senha
    if (password_verify('admin123', $admin['password'])) {
        echo "   ✓ Hash de senha funcionando corretamente\n";
    } else {
        echo "   ❌ Problema com hash de senha\n";
    }
    
    echo "\n8. Testando URLs do sistema...\n";
    $urls = [
        'Home' => url('/'),
        'Login Cliente' => url('/login'),
        'Login Admin' => url('/admin/login'),
        'Dashboard Cliente' => url('/client/dashboard'),
        'Dashboard Admin' => url('/admin/dashboard'),
        'API Health' => url('/api/v1/health')
    ];
    
    foreach ($urls as $name => $url) {
        echo "   - {$name}: {$url}\n";
    }
    
    echo "\n🎉 Todos os testes concluídos!\n\n";
    echo "=== RESUMO ===\n";
    echo "✓ Sistema configurado e funcionando\n";
    echo "✓ Banco de dados conectado\n";
    echo "✓ Tabelas criadas\n";
    echo "✓ Dados iniciais inseridos\n";
    echo "✓ Autenticação funcionando\n\n";
    echo "Acesse: https://app.melhorcupom.shop/admin/login\n";
    echo "Email: <EMAIL>\n";
    echo "Senha: admin123\n\n";
    
} catch (Exception $e) {
    echo "❌ Erro durante os testes: " . $e->getMessage() . "\n";
    echo "\nDetalhes do erro:\n";
    echo $e->getTraceAsString() . "\n";
    exit(1);
}
