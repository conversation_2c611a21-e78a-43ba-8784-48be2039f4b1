<?php
/**
 * Script para verificar e configurar chave de API REST
 */

// Carregar WordPress
define('WP_USE_THEMES', false);
require_once(dirname(__FILE__) . '/../../../wp-blog-header.php');

echo "=== Verificação da Chave de API REST ===\n\n";

// Verificar se a chave existe
$api_key = get_option('e1copy_ai_rest_api_key');

if (empty($api_key)) {
    echo "❌ Nenhuma chave de API REST configurada!\n";
    echo "Gerando nova chave...\n";
    
    // Gerar nova chave
    $new_key = bin2hex(random_bytes(16)); // 32 caracteres
    update_option('e1copy_ai_rest_api_key', $new_key);
    
    echo "✅ Nova chave gerada: $new_key\n";
    $api_key = $new_key;
} else {
    echo "✅ Chave de API REST encontrada: " . substr($api_key, 0, 8) . "...\n";
}

echo "\n=== Testando Endpoints ===\n\n";

// Testar endpoint de teste
echo "1. Testando endpoint de teste...\n";
$test_url = home_url('/wp-json/e1copy-ai/v1/test');
$response = wp_remote_get($test_url);
if (!is_wp_error($response)) {
    $body = wp_remote_retrieve_body($response);
    $data = json_decode($body, true);
    if ($data && $data['success']) {
        echo "✅ Endpoint de teste funcionando\n";
    } else {
        echo "❌ Endpoint de teste com erro: $body\n";
    }
} else {
    echo "❌ Erro ao acessar endpoint de teste: " . $response->get_error_message() . "\n";
}

// Testar endpoint de verificação de chave
echo "\n2. Testando verificação de chave...\n";
$verify_url = home_url('/wp-json/e1copy-ai/v1/verify-key?api_key=' . $api_key);
$response = wp_remote_get($verify_url);
if (!is_wp_error($response)) {
    $body = wp_remote_retrieve_body($response);
    $data = json_decode($body, true);
    if ($data && $data['success']) {
        echo "✅ Verificação de chave funcionando\n";
    } else {
        echo "❌ Verificação de chave com erro: $body\n";
    }
} else {
    echo "❌ Erro ao verificar chave: " . $response->get_error_message() . "\n";
}

// Testar endpoint de posts
echo "\n3. Testando endpoint de posts...\n";
$posts_url = home_url('/wp-json/e1copy-ai/v1/posts?api_key=' . $api_key);
$response = wp_remote_get($posts_url);
if (!is_wp_error($response)) {
    $body = wp_remote_retrieve_body($response);
    $data = json_decode($body, true);
    if ($data && isset($data['success'])) {
        if ($data['success']) {
            $post_count = isset($data['posts']) ? count($data['posts']) : 0;
            echo "✅ Endpoint de posts funcionando - $post_count posts encontrados\n";
        } else {
            echo "❌ Endpoint de posts com erro: " . ($data['message'] ?? 'Erro desconhecido') . "\n";
        }
    } else {
        echo "❌ Resposta inválida do endpoint de posts: $body\n";
    }
} else {
    echo "❌ Erro ao acessar endpoint de posts: " . $response->get_error_message() . "\n";
}

// Verificar tabela de posts
echo "\n=== Verificando Tabela de Posts ===\n\n";
global $wpdb;
$table_name = $wpdb->prefix . 'e1copy_my_posts';

$table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;
if ($table_exists) {
    echo "✅ Tabela $table_name existe\n";
    
    // Verificar estrutura
    $columns = $wpdb->get_results("SHOW COLUMNS FROM $table_name");
    $has_processed = false;
    foreach ($columns as $column) {
        if ($column->Field === 'processed') {
            $has_processed = true;
            echo "✅ Coluna 'processed' existe (tipo: {$column->Type})\n";
            break;
        }
    }
    
    if (!$has_processed) {
        echo "❌ Coluna 'processed' não existe - adicionando...\n";
        $wpdb->query("ALTER TABLE $table_name ADD COLUMN processed tinyint(1) DEFAULT 0");
        echo "✅ Coluna 'processed' adicionada\n";
    }
    
    // Contar posts
    $total_posts = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
    $unprocessed_posts = $wpdb->get_var("SELECT COUNT(*) FROM $table_name WHERE processed = 0");
    $processed_posts = $wpdb->get_var("SELECT COUNT(*) FROM $table_name WHERE processed = 1");
    
    echo "📊 Total de posts: $total_posts\n";
    echo "📊 Posts não processados: $unprocessed_posts\n";
    echo "📊 Posts processados: $processed_posts\n";
    
} else {
    echo "❌ Tabela $table_name não existe\n";
}

echo "\n=== Informações para o Dashboard ===\n\n";
echo "Site URL: " . home_url() . "\n";
echo "API Key: $api_key\n";
echo "Endpoint de posts: " . home_url('/wp-json/e1copy-ai/v1/posts') . "\n";
echo "Endpoint de teste: " . home_url('/wp-json/e1copy-ai/v1/test') . "\n";

echo "\n=== Teste cURL ===\n\n";
echo "curl -X GET \"" . home_url('/wp-json/e1copy-ai/v1/posts') . "\" \\\n";
echo "     -H \"X-E1Copy-API-Key: $api_key\"\n";

echo "\n✅ Verificação concluída!\n";
?>
