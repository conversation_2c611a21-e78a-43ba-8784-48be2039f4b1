<?php
/**
 * Serviço de Integração com API do Groq
 * Sistema de Dashboard E1Copy AI
 */

class GroqService {
    private $apiKey;
    private $model;
    private $baseUrl = 'https://api.groq.com/openai/v1';
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
        $this->loadSettings();
    }
    
    private function loadSettings() {
        $settings = $this->db->fetchAll("SELECT setting_key, setting_value FROM ai_settings WHERE setting_key IN ('groq_api_key', 'groq_model')");
        
        foreach ($settings as $setting) {
            switch ($setting['setting_key']) {
                case 'groq_api_key':
                    $this->apiKey = $setting['setting_value'];
                    break;
                case 'groq_model':
                    $this->model = $setting['setting_value'] ?: 'llama3-8b-8192';
                    break;
            }
        }
    }
    
    /**
     * Gerar conteúdo usando a API do Groq
     */
    public function generateContent($prompt, $options = []) {
        if (empty($this->apiKey)) {
            throw new Exception('Chave da API do Groq não configurada');
        }
        
        $maxTokens = $options['max_tokens'] ?? $this->getSetting('max_tokens', 2000);
        $temperature = $options['temperature'] ?? $this->getSetting('temperature', 0.7);
        
        $data = [
            'model' => $this->model,
            'messages' => [
                [
                    'role' => 'system',
                    'content' => 'Você é um assistente especializado em criar conteúdo para blogs e sites. Gere conteúdo de alta qualidade, original e otimizado para SEO.'
                ],
                [
                    'role' => 'user',
                    'content' => $prompt
                ]
            ],
            'max_tokens' => (int) $maxTokens,
            'temperature' => (float) $temperature,
            'stream' => false
        ];
        
        $response = $this->makeRequest('/chat/completions', $data);
        
        if (isset($response['choices'][0]['message']['content'])) {
            return $response['choices'][0]['message']['content'];
        }
        
        throw new Exception('Resposta inválida da API do Groq');
    }
    
    /**
     * Gerar post completo baseado em template e variáveis
     */
    public function generatePost($templateId, $topic, $keywords = [], $customVariables = []) {
        // Buscar template
        $template = $this->db->fetch("SELECT * FROM content_templates WHERE id = :id AND status = 'active'", ['id' => $templateId]);
        
        if (!$template) {
            throw new Exception('Template não encontrado ou inativo');
        }
        
        // Decodificar variáveis do template
        $templateVariables = json_decode($template['variables'], true) ?: [];
        $requiredVariables = json_decode($template['required_variables'], true) ?: [];
        
        // Construir prompt para gerar as variáveis
        $prompt = $this->buildPrompt($topic, $keywords, $templateVariables, $customVariables);
        
        // Gerar conteúdo via Groq
        $aiResponse = $this->generateContent($prompt);
        
        // Processar resposta da IA para extrair variáveis
        $variables = $this->parseAiResponse($aiResponse, $templateVariables);
        
        // Validar variáveis obrigatórias
        foreach ($requiredVariables as $required) {
            if (empty($variables[$required])) {
                throw new Exception("Variável obrigatória '{$required}' não foi gerada pela IA");
            }
        }
        
        // Aplicar template
        $finalContent = $this->applyTemplate($template['html_template'], $variables);
        
        return [
            'content' => $finalContent,
            'variables' => $variables,
            'ai_response' => $aiResponse,
            'template_used' => $template,
            'prompt_used' => $prompt
        ];
    }
    
    /**
     * Construir prompt para geração de conteúdo
     */
    private function buildPrompt($topic, $keywords, $templateVariables, $customVariables) {
        $prompt = "Crie um post completo sobre o tópico: '{$topic}'\n\n";
        
        if (!empty($keywords)) {
            $prompt .= "Palavras-chave a incluir: " . implode(', ', $keywords) . "\n\n";
        }
        
        $prompt .= "Gere o conteúdo seguindo exatamente este formato JSON:\n{\n";
        
        foreach ($templateVariables as $variable) {
            $description = $this->getVariableDescription($variable);
            $customValue = $customVariables[$variable] ?? null;
            
            if ($customValue) {
                $prompt .= "  \"{$variable}\": \"{$customValue}\",\n";
            } else {
                $prompt .= "  \"{$variable}\": \"{$description}\",\n";
            }
        }
        
        $prompt = rtrim($prompt, ",\n") . "\n}\n\n";
        
        $prompt .= "INSTRUÇÕES IMPORTANTES:\n";
        $prompt .= "- Retorne APENAS o JSON válido, sem texto adicional\n";
        $prompt .= "- O conteúdo deve ser original e de alta qualidade\n";
        $prompt .= "- Use HTML simples no conteúdo (p, h3, h4, ul, li, strong, em)\n";
        $prompt .= "- O título deve ser atrativo e otimizado para SEO\n";
        $prompt .= "- O slug deve ser URL-friendly (sem acentos, espaços ou caracteres especiais)\n";
        $prompt .= "- As tags devem ser separadas por vírgula\n";
        $prompt .= "- O conteúdo deve ter pelo menos 500 palavras\n";
        
        return $prompt;
    }
    
    /**
     * Processar resposta da IA para extrair variáveis
     */
    private function parseAiResponse($response, $templateVariables) {
        // Tentar extrair JSON da resposta
        $jsonStart = strpos($response, '{');
        $jsonEnd = strrpos($response, '}');
        
        if ($jsonStart !== false && $jsonEnd !== false) {
            $jsonString = substr($response, $jsonStart, $jsonEnd - $jsonStart + 1);
            $variables = json_decode($jsonString, true);
            
            if ($variables) {
                // Limpar e validar variáveis
                $cleanVariables = [];
                foreach ($templateVariables as $var) {
                    $cleanVariables[$var] = $variables[$var] ?? '';
                }
                return $cleanVariables;
            }
        }
        
        // Se não conseguiu extrair JSON, tentar parsing manual
        return $this->fallbackParsing($response, $templateVariables);
    }
    
    /**
     * Parsing manual como fallback
     */
    private function fallbackParsing($response, $templateVariables) {
        $variables = [];
        
        // Tentar extrair título
        if (preg_match('/título["\']?\s*:\s*["\']([^"\']+)["\']?/i', $response, $matches)) {
            $variables['titulo'] = $matches[1];
        }
        
        // Tentar extrair conteúdo
        if (preg_match('/conteúdo["\']?\s*:\s*["\'](.+?)["\']?(?=,|\}|$)/is', $response, $matches)) {
            $variables['conteudo'] = $matches[1];
        }
        
        // Preencher variáveis faltantes com valores padrão
        foreach ($templateVariables as $var) {
            if (!isset($variables[$var])) {
                $variables[$var] = $this->getDefaultValue($var);
            }
        }
        
        return $variables;
    }
    
    /**
     * Aplicar template com variáveis
     */
    private function applyTemplate($template, $variables) {
        $content = $template;
        
        foreach ($variables as $key => $value) {
            $content = str_replace("{{$key}}", $value, $content);
        }
        
        // Remover variáveis não substituídas
        $content = preg_replace('/\{\{[^}]+\}\}/', '', $content);
        
        return $content;
    }
    
    /**
     * Fazer requisição para API do Groq
     */
    private function makeRequest($endpoint, $data) {
        $url = $this->baseUrl . $endpoint;
        
        $headers = [
            'Authorization: Bearer ' . $this->apiKey,
            'Content-Type: application/json'
        ];
        
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($data),
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_TIMEOUT => 60,
            CURLOPT_SSL_VERIFYPEER => true
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            throw new Exception('Erro na requisição: ' . $error);
        }
        
        if ($httpCode !== 200) {
            throw new Exception("Erro da API do Groq (HTTP {$httpCode}): " . $response);
        }
        
        $decoded = json_decode($response, true);
        
        if (!$decoded) {
            throw new Exception('Resposta inválida da API do Groq');
        }
        
        return $decoded;
    }
    
    /**
     * Obter configuração
     */
    private function getSetting($key, $default = null) {
        $setting = $this->db->fetch("SELECT setting_value FROM ai_settings WHERE setting_key = :key", ['key' => $key]);
        return $setting ? $setting['setting_value'] : $default;
    }
    
    /**
     * Obter descrição da variável para o prompt
     */
    private function getVariableDescription($variable) {
        $descriptions = [
            'titulo' => 'Título atrativo e otimizado para SEO',
            'subtitulo' => 'Subtítulo complementar',
            'conteudo' => 'Conteúdo principal do post em HTML',
            'resumo' => 'Resumo/excerpt do post',
            'slug' => 'URL slug (sem acentos, espaços ou caracteres especiais)',
            'categoria' => 'Categoria principal do post',
            'tags' => 'Tags separadas por vírgula',
            'palavra_chave' => 'Palavra-chave principal'
        ];
        
        return $descriptions[$variable] ?? "Gere conteúdo para {$variable}";
    }
    
    /**
     * Obter valor padrão para variável
     */
    private function getDefaultValue($variable) {
        $defaults = [
            'titulo' => 'Título do Post',
            'subtitulo' => '',
            'conteudo' => '<p>Conteúdo do post.</p>',
            'resumo' => 'Resumo do post.',
            'slug' => 'titulo-do-post',
            'categoria' => 'Geral',
            'tags' => 'blog, conteúdo',
            'palavra_chave' => 'palavra-chave'
        ];
        
        return $defaults[$variable] ?? '';
    }
}
