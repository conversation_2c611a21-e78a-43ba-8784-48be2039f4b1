<?php
if (!defined('ABSPATH')) exit; // Evita acesso direto

/**
 * Atualiza a estrutura do banco de dados para adicionar a coluna 'processed'
 */
function e1copy_update_database_structure() {
    global $wpdb;
    $table_name = $wpdb->prefix . 'e1copy_my_posts';
    
    // Verificar se a tabela existe
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;
    
    if (!$table_exists) {
        // Se a tabela não existir, criar com a estrutura completa
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE $table_name (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            title text NOT NULL,
            keyword text NOT NULL,
            categories text,
            tags text,
            status varchar(20) DEFAULT 'draft',
            image_bank varchar(255),
            subtitle_quantity int DEFAULT 0,
            article_options text,
            youtube_video text,
            cta text,
            cta_text text,
            cta_link text,
            processed tinyint(1) DEFAULT 0,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY  (id)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
        
        error_log('E1Copy AI: Tabela criada com sucesso: ' . $table_name);
    } else {
        // Verificar se a coluna 'processed' existe
        $column_exists = $wpdb->get_results("SHOW COLUMNS FROM $table_name LIKE 'processed'");
        
        if (empty($column_exists)) {
            // Adicionar a coluna 'processed' se não existir
            $wpdb->query("ALTER TABLE $table_name ADD COLUMN processed tinyint(1) DEFAULT 0 AFTER cta_link");
            error_log('E1Copy AI: Coluna processed adicionada com sucesso à tabela ' . $table_name);
        }
    }
}

// Executar a atualização do banco de dados
e1copy_update_database_structure();
