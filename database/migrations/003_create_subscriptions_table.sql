-- Tabela de assinaturas dos clientes
CREATE TABLE IF NOT EXISTS subscriptions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    plan_id INT NOT NULL,
    status ENUM('active', 'cancelled', 'expired', 'suspended') NOT NULL DEFAULT 'active',
    starts_at TIMESTAMP NOT NULL,
    ends_at TIMESTAMP NULL,
    next_billing_date TIMESTAMP NULL,
    payment_method VARCHAR(50),
    payment_status ENUM('paid', 'pending', 'failed', 'cancelled') NOT NULL DEFAULT 'pending',
    stripe_subscription_id VARCHAR(255) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIG<PERSON> KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIG<PERSON> KEY (plan_id) REFERENCES plans(id) ON DELETE RESTRICT,
    
    INDEX idx_user_id (user_id),
    INDEX idx_plan_id (plan_id),
    INDEX idx_status (status),
    INDEX idx_payment_status (payment_status),
    INDEX idx_ends_at (ends_at),
    INDEX idx_next_billing_date (next_billing_date)
);
