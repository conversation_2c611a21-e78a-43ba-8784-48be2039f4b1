<?php
/**
 * Controller de Configurações de IA
 * Sistema de Dashboard E1Copy AI
 */

class AiSettingsController extends Controller {
    
    public function index() {
        try {
            // Verificar se as tabelas existem
            $this->db->fetch("SELECT 1 FROM ai_settings LIMIT 1");
            $tablesExist = true;
        } catch (Exception $e) {
            $tablesExist = false;
        }

        if (!$tablesExist) {
            // Mostrar página de instalação
            $this->view('admin.ai-settings.install', [
                'message' => 'Sistema de IA não instalado. Execute o arquivo install-ai-system.sql no seu banco de dados.'
            ]);
            return;
        }

        try {
            // Buscar todas as configurações de IA
            $settings = $this->db->fetchAll("SELECT * FROM ai_settings ORDER BY setting_key");

            // Organizar configurações por categoria
            $settingsGrouped = [];
            foreach ($settings as $setting) {
                $category = $this->getSettingCategory($setting['setting_key']);
                $settingsGrouped[$category][] = $setting;
            }

            // Buscar templates disponíveis
            $templates = $this->db->fetchAll("SELECT * FROM content_templates ORDER BY is_default DESC, name ASC");

            // Buscar estatísticas de uso
            try {
                $usageStats = $this->db->fetch("
                    SELECT
                        COUNT(*) as total_generated,
                        COUNT(CASE WHEN generation_status = 'completed' THEN 1 END) as successful,
                        COUNT(CASE WHEN generation_status = 'failed' THEN 1 END) as failed,
                        COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as last_30_days
                    FROM generated_posts
                ");
            } catch (Exception $e) {
                $usageStats = [
                    'total_generated' => 0,
                    'successful' => 0,
                    'failed' => 0,
                    'last_30_days' => 0
                ];
            }

            $this->view('admin.ai-settings.index', [
                'settingsGrouped' => $settingsGrouped,
                'templates' => $templates,
                'usageStats' => $usageStats
            ]);

        } catch (Exception $e) {
            $this->view('admin.ai-settings.error', [
                'error' => 'Erro ao carregar configurações: ' . $e->getMessage()
            ]);
        }
    }
    
    public function update() {
        // Verificar CSRF token
        if (!$this->verifyCsrfToken($this->input('_token'))) {
            $this->flash('error', 'Token de segurança inválido');
            return redirect(url('/admin/ai-settings'));
        }
        
        $settings = $this->input('settings', []);
        
        try {
            $this->db->beginTransaction();
            
            foreach ($settings as $key => $value) {
                // Validar e converter valor baseado no tipo
                $settingData = $this->db->fetch("SELECT setting_type FROM ai_settings WHERE setting_key = :key", ['key' => $key]);
                
                if ($settingData) {
                    $convertedValue = $this->convertSettingValue($value, $settingData['setting_type']);
                    
                    $this->db->update('ai_settings', 
                        ['setting_value' => $convertedValue], 
                        'setting_key = :key', 
                        ['key' => $key]
                    );
                }
            }
            
            $this->db->commit();
            $this->flash('success', 'Configurações atualizadas com sucesso!');
            
        } catch (Exception $e) {
            $this->db->rollback();
            $this->flash('error', 'Erro ao atualizar configurações: ' . $e->getMessage());
        }
        
        return redirect(url('/admin/ai-settings'));
    }
    
    public function templates() {
        $templates = $this->db->fetchAll("SELECT * FROM content_templates ORDER BY is_default DESC, name ASC");
        
        $this->view('admin.ai-settings.templates', [
            'templates' => $templates
        ]);
    }
    
    public function saveTemplate() {
        // Verificar CSRF token
        if (!$this->verifyCsrfToken($this->input('_token'))) {
            return $this->json(['success' => false, 'message' => 'Token de segurança inválido'], 400);
        }
        
        // Validar dados
        $validation = $this->validate([
            'name' => 'required|min:2|max:255',
            'html_template' => 'required|min:10',
            'variables' => 'required',
            'required_variables' => 'required'
        ]);
        
        if ($validation !== true) {
            return $this->json(['success' => false, 'message' => 'Dados inválidos', 'errors' => $validation], 422);
        }
        
        try {
            $variables = json_decode($this->input('variables'), true);
            $requiredVariables = json_decode($this->input('required_variables'), true);
            
            if (!$variables || !$requiredVariables) {
                return $this->json(['success' => false, 'message' => 'Variáveis devem ser um JSON válido'], 400);
            }
            
            $templateData = [
                'name' => $this->input('name'),
                'description' => $this->input('description'),
                'html_template' => $this->input('html_template'),
                'variables' => json_encode($variables),
                'required_variables' => json_encode($requiredVariables),
                'status' => 'active'
            ];
            
            $templateId = $this->db->insert('content_templates', $templateData);
            
            if ($templateId) {
                return $this->json([
                    'success' => true,
                    'message' => 'Template criado com sucesso!',
                    'template_id' => $this->db->lastInsertId()
                ]);
            } else {
                return $this->json(['success' => false, 'message' => 'Erro ao criar template'], 500);
            }
            
        } catch (Exception $e) {
            return $this->json(['success' => false, 'message' => 'Erro: ' . $e->getMessage()], 500);
        }
    }
    
    public function updateTemplate($id) {
        // Verificar CSRF token
        if (!$this->verifyCsrfToken($this->input('_token'))) {
            return $this->json(['success' => false, 'message' => 'Token de segurança inválido'], 400);
        }
        
        $template = $this->db->fetch("SELECT * FROM content_templates WHERE id = :id", ['id' => $id]);
        
        if (!$template) {
            return $this->json(['success' => false, 'message' => 'Template não encontrado'], 404);
        }
        
        $action = $this->input('action');
        
        try {
            switch ($action) {
                case 'set_default':
                    // Remover default de todos os templates
                    $this->db->query("UPDATE content_templates SET is_default = FALSE");
                    
                    // Definir este como padrão
                    $this->db->update('content_templates', ['is_default' => true], 'id = :id', ['id' => $id]);
                    
                    // Atualizar configuração
                    $this->db->update('ai_settings', ['setting_value' => $id], 'setting_key = :key', ['key' => 'default_template_id']);
                    
                    return $this->json(['success' => true, 'message' => 'Template definido como padrão!']);
                    
                case 'toggle_status':
                    $newStatus = $template['status'] === 'active' ? 'inactive' : 'active';
                    $this->db->update('content_templates', ['status' => $newStatus], 'id = :id', ['id' => $id]);
                    
                    return $this->json(['success' => true, 'message' => 'Status do template atualizado!']);
                    
                default:
                    return $this->json(['success' => false, 'message' => 'Ação inválida'], 400);
            }
            
        } catch (Exception $e) {
            return $this->json(['success' => false, 'message' => 'Erro: ' . $e->getMessage()], 500);
        }
    }
    
    public function deleteTemplate($id) {
        // Verificar CSRF token
        if (!$this->verifyCsrfToken($this->input('_token'))) {
            return $this->json(['success' => false, 'message' => 'Token de segurança inválido'], 400);
        }
        
        $template = $this->db->fetch("SELECT * FROM content_templates WHERE id = :id", ['id' => $id]);
        
        if (!$template) {
            return $this->json(['success' => false, 'message' => 'Template não encontrado'], 404);
        }
        
        if ($template['is_default']) {
            return $this->json(['success' => false, 'message' => 'Não é possível excluir o template padrão'], 400);
        }
        
        try {
            $this->db->delete('content_templates', 'id = :id', ['id' => $id]);
            return $this->json(['success' => true, 'message' => 'Template excluído com sucesso!']);
            
        } catch (Exception $e) {
            return $this->json(['success' => false, 'message' => 'Erro: ' . $e->getMessage()], 500);
        }
    }
    
    private function getSettingCategory($key) {
        if (strpos($key, 'groq_') === 0) {
            return 'Groq API';
        } elseif (strpos($key, 'default_') === 0) {
            return 'Padrões';
        } elseif (strpos($key, 'max_') === 0 || strpos($key, 'limit') !== false) {
            return 'Limites';
        } elseif (strpos($key, 'queue_') === 0) {
            return 'Fila';
        } else {
            return 'Geral';
        }
    }
    
    private function convertSettingValue($value, $type) {
        switch ($type) {
            case 'boolean':
                return $value === 'true' || $value === '1' || $value === 1 ? 'true' : 'false';
            case 'number':
                return is_numeric($value) ? (string) $value : '0';
            case 'json':
                if (is_string($value)) {
                    $decoded = json_decode($value, true);
                    return $decoded !== null ? $value : '[]';
                }
                return json_encode($value);
            default:
                return (string) $value;
        }
    }
}
