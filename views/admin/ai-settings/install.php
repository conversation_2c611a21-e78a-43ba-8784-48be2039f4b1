<?php
$title = 'Sistema de IA - Instalação - E1Copy AI';
$pageTitle = 'Sistema de Geração de Conteúdo com IA';
ob_start();
?>

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Sistema de IA Não Instalado
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>Instalação Necessária</h6>
                        <p class="mb-0">
                            O sistema de geração de conteúdo com IA ainda não foi instalado. 
                            Para usar essas funcionalidades, você precisa executar o script de instalação no banco de dados.
                        </p>
                    </div>
                    
                    <h6>📋 Passos para Instalação:</h6>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <span class="badge bg-primary me-2">1</span>
                                        Executar Script SQL
                                    </h6>
                                    <p class="card-text">
                                        Execute o arquivo <code>install-ai-system.sql</code> no seu banco de dados MySQL:
                                    </p>
                                    <div class="bg-dark text-light p-2 rounded">
                                        <code>mysql -u usuario -p database &lt; app/install-ai-system.sql</code>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <span class="badge bg-success me-2">2</span>
                                        Configurar API Groq
                                    </h6>
                                    <p class="card-text">
                                        Após a instalação, configure sua chave da API do Groq nas configurações.
                                    </p>
                                    <div class="bg-dark text-light p-2 rounded">
                                        <code>Chave API → Configurações de IA</code>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <span class="badge bg-info me-2">3</span>
                                        Configurar Cron (Opcional)
                                    </h6>
                                    <p class="card-text">
                                        Para processamento automático da fila:
                                    </p>
                                    <div class="bg-dark text-light p-2 rounded">
                                        <code>*/5 * * * * php /path/to/app/cron/process-queue.php</code>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <span class="badge bg-warning me-2">4</span>
                                        Recarregar Página
                                    </h6>
                                    <p class="card-text">
                                        Após executar o SQL, recarregue esta página para acessar as configurações.
                                    </p>
                                    <button class="btn btn-primary" onclick="location.reload()">
                                        <i class="fas fa-sync me-1"></i>
                                        Recarregar
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <h6>🚀 Funcionalidades que serão habilitadas:</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>Geração automática de posts com IA</li>
                                <li><i class="fas fa-check text-success me-2"></i>Templates HTML personalizáveis</li>
                                <li><i class="fas fa-check text-success me-2"></i>Fila de processamento</li>
                                <li><i class="fas fa-check text-success me-2"></i>Integração com WordPress</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>Relatórios detalhados</li>
                                <li><i class="fas fa-check text-success me-2"></i>Monitoramento em tempo real</li>
                                <li><i class="fas fa-check text-success me-2"></i>Logs de execução</li>
                                <li><i class="fas fa-check text-success me-2"></i>Controle por cliente</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="alert alert-warning mt-3">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>Importante</h6>
                        <p class="mb-0">
                            Certifique-se de fazer backup do seu banco de dados antes de executar o script de instalação.
                            O script criará novas tabelas mas não modificará dados existentes.
                        </p>
                    </div>
                    
                    <div class="text-center mt-4">
                        <a href="<?= url('/admin/dashboard') ?>" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>
                            Voltar ao Dashboard
                        </a>
                        <button class="btn btn-primary ms-2" onclick="location.reload()">
                            <i class="fas fa-sync me-1"></i>
                            Verificar Instalação
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
$content = ob_get_clean();
include __DIR__ . '/../../layouts/app.php';
?>
