<?php
/**
 * Script de debug para testar upload de imagens
 */

require_once "bootstrap.php";

echo "=== DEBUG UPLOAD DE IMAGENS ===\n";

// 1. Verificar estrutura de diretórios
echo "1. Verificando estrutura de diretórios:\n";

$uploadDir = __DIR__ . '/uploads';
echo "   Diretório base: $uploadDir\n";
echo "   Existe: " . (is_dir($uploadDir) ? 'SIM' : 'NÃO') . "\n";
echo "   Permissões: " . (is_dir($uploadDir) ? substr(sprintf('%o', fileperms($uploadDir)), -4) : 'N/A') . "\n";

$postsDir = $uploadDir . '/posts';
echo "   Diretório posts: $postsDir\n";
echo "   Existe: " . (is_dir($postsDir) ? 'SIM' : 'NÃO') . "\n";
echo "   Permissões: " . (is_dir($postsDir) ? substr(sprintf('%o', fileperms($postsDir)), -4) : 'N/A') . "\n";

// 2. Testar criação de diretório
echo "\n2. Testando criação de diretório:\n";
$testDir = $postsDir . '/test_' . time();
echo "   Tentando criar: $testDir\n";

if (mkdir($testDir, 0755, true)) {
    echo "   ✓ Diretório criado com sucesso\n";
    echo "   Permissões: " . substr(sprintf('%o', fileperms($testDir)), -4) . "\n";
    
    // Testar criação de arquivo
    $testFile = $testDir . '/test.txt';
    if (file_put_contents($testFile, 'teste') !== false) {
        echo "   ✓ Arquivo de teste criado com sucesso\n";
        unlink($testFile);
    } else {
        echo "   ✗ Erro ao criar arquivo de teste\n";
    }
    
    rmdir($testDir);
} else {
    echo "   ✗ Erro ao criar diretório\n";
    echo "   Erro: " . (error_get_last()['message'] ?? 'Desconhecido') . "\n";
}

// 3. Verificar configurações PHP
echo "\n3. Configurações PHP:\n";
echo "   upload_max_filesize: " . ini_get('upload_max_filesize') . "\n";
echo "   post_max_size: " . ini_get('post_max_size') . "\n";
echo "   max_file_uploads: " . ini_get('max_file_uploads') . "\n";
echo "   file_uploads: " . (ini_get('file_uploads') ? 'ON' : 'OFF') . "\n";
echo "   upload_tmp_dir: " . (ini_get('upload_tmp_dir') ?: 'padrão do sistema') . "\n";

// 4. Verificar se há posts recentes para debug
echo "\n4. Posts recentes:\n";
try {
    $db = Database::getInstance();
    $recentPosts = $db->fetchAll("
        SELECT id, title, post_type, product_images, post_cover, created_at 
        FROM client_posts 
        ORDER BY created_at DESC 
        LIMIT 5
    ");
    
    foreach ($recentPosts as $post) {
        echo "   Post ID: {$post['id']}\n";
        echo "   Título: {$post['title']}\n";
        echo "   Tipo: {$post['post_type']}\n";
        echo "   Imagens: " . ($post['product_images'] ? 'SIM' : 'NÃO') . "\n";
        echo "   Capa: " . ($post['post_cover'] ? 'SIM' : 'NÃO') . "\n";
        echo "   Data: {$post['created_at']}\n";
        
        // Verificar se arquivos existem fisicamente
        if ($post['product_images']) {
            $images = json_decode($post['product_images'], true);
            if (is_array($images)) {
                echo "   URLs das imagens:\n";
                foreach ($images as $imageUrl) {
                    echo "     - $imageUrl\n";
                    $imagePath = str_replace('/app/uploads/posts/', '', $imageUrl);
                    $fullPath = __DIR__ . '/uploads/posts/' . $imagePath;
                    echo "       Arquivo existe: " . (file_exists($fullPath) ? 'SIM' : 'NÃO') . "\n";
                }
            }
        }
        
        if ($post['post_cover']) {
            echo "   URL da capa: {$post['post_cover']}\n";
            $coverPath = str_replace('/app/uploads/posts/', '', $post['post_cover']);
            $fullPath = __DIR__ . '/uploads/posts/' . $coverPath;
            echo "   Arquivo da capa existe: " . (file_exists($fullPath) ? 'SIM' : 'NÃO') . "\n";
        }
        
        echo "\n";
    }
} catch (Exception $e) {
    echo "   Erro ao consultar banco: " . $e->getMessage() . "\n";
}

// 5. Simular processamento de upload
echo "5. Simulando processamento de upload:\n";

// Simular dados de $_FILES
$simulatedFiles = [
    'product_images' => [
        'name' => ['test1.jpg', 'test2.png'],
        'type' => ['image/jpeg', 'image/png'],
        'tmp_name' => ['/tmp/test1', '/tmp/test2'],
        'error' => [UPLOAD_ERR_OK, UPLOAD_ERR_OK],
        'size' => [1024, 2048]
    ]
];

echo "   Dados simulados de \$_FILES:\n";
print_r($simulatedFiles);

// Simular processamento como no controller
$tempImages = [];
for ($i = 0; $i < count($simulatedFiles['product_images']['name']); $i++) {
    if ($simulatedFiles['product_images']['error'][$i] === UPLOAD_ERR_OK) {
        $tempImages[] = [
            'name' => $simulatedFiles['product_images']['name'][$i],
            'tmp_name' => $simulatedFiles['product_images']['tmp_name'][$i],
            'type' => $simulatedFiles['product_images']['type'][$i],
            'size' => $simulatedFiles['product_images']['size'][$i]
        ];
    }
}

echo "   Imagens processadas: " . count($tempImages) . "\n";
foreach ($tempImages as $index => $image) {
    echo "     Imagem $index: {$image['name']} ({$image['type']}, {$image['size']} bytes)\n";
}

echo "\n=== FIM DEBUG ===\n";
