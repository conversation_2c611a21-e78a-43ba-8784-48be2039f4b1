<?php
/**
 * Página de Diagnóstico de Chaves
 * Sistema de Dashboard E1Copy AI
 */

// Verificar se é admin
if (!Auth::check() || !Auth::isAdmin()) {
    redirect(url('/admin/login'));
}

$title = 'Diagnóstico de Chaves - E1Copy AI';
$pageTitle = 'Diagnóstico de Chaves';

// Processar ação de correção
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'fix_key') {
    if (!$this->verifyCsrfToken($_POST['_token'])) {
        $error = 'Token de segurança inválido';
    } else {
        $apiKey = $_POST['api_key'];
        
        try {
            $db = Database::getInstance();
            $db->beginTransaction();
            
            // Buscar dados da chave
            $keyData = $db->fetch(
                "SELECT ak.*, u.status as user_status, s.status as subscription_status
                 FROM api_keys ak
                 JOIN users u ON ak.user_id = u.id
                 JOIN subscriptions s ON ak.subscription_id = s.id
                 WHERE ak.api_key = :key",
                ['key' => $apiKey]
            );
            
            if ($keyData) {
                // Ativar usuário
                if ($keyData['user_status'] !== 'active') {
                    $db->update('users', ['status' => 'active'], 'id = :id', ['id' => $keyData['user_id']]);
                }
                
                // Ativar chave
                if ($keyData['status'] !== 'active') {
                    $db->update('api_keys', ['status' => 'active'], 'id = :id', ['id' => $keyData['id']]);
                }
                
                // Ativar assinatura
                if ($keyData['subscription_status'] !== 'active') {
                    $db->update('subscriptions', [
                        'status' => 'active',
                        'payment_status' => 'paid'
                    ], 'id = :id', ['id' => $keyData['subscription_id']]);
                }
                
                $db->commit();
                $success = 'Chave corrigida com sucesso!';
            } else {
                $error = 'Chave não encontrada';
            }
            
        } catch (Exception $e) {
            $db->rollback();
            $error = 'Erro ao corrigir chave: ' . $e->getMessage();
        }
    }
}

// Buscar chave específica se fornecida
$searchKey = $_GET['key'] ?? '';
$keyDetails = null;

if ($searchKey) {
    $db = Database::getInstance();
    $keyDetails = $db->fetch(
        "SELECT ak.*, u.name as user_name, u.email, u.status as user_status, 
                s.status as subscription_status, s.payment_status, s.starts_at, s.ends_at,
                p.name as plan_name
         FROM api_keys ak
         JOIN users u ON ak.user_id = u.id
         JOIN subscriptions s ON ak.subscription_id = s.id
         JOIN plans p ON s.plan_id = p.id
         WHERE ak.api_key = :key",
        ['key' => $searchKey]
    );
    
    if ($keyDetails) {
        // Testar validação
        $verification = Auth::verifyApiKey($searchKey);
        $keyDetails['verification'] = $verification;
    }
}

ob_start();
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-search me-2"></i>
                        Diagnóstico de Chaves API
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (isset($success)): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            <?= e($success) ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (isset($error)): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <?= e($error) ?>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Formulário de busca -->
                    <form method="GET" class="mb-4">
                        <div class="row">
                            <div class="col-md-10">
                                <input type="text" class="form-control" name="key" 
                                       placeholder="Cole a chave API para diagnóstico..." 
                                       value="<?= e($searchKey) ?>">
                            </div>
                            <div class="col-md-2">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-search me-1"></i>
                                    Diagnosticar
                                </button>
                            </div>
                        </div>
                    </form>
                    
                    <?php if ($keyDetails): ?>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">Informações da Chave</h6>
                                    </div>
                                    <div class="card-body">
                                        <table class="table table-sm">
                                            <tr>
                                                <td><strong>Chave:</strong></td>
                                                <td><code><?= e(substr($searchKey, 0, 20)) ?>...</code></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Nome:</strong></td>
                                                <td><?= e($keyDetails['name']) ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Status:</strong></td>
                                                <td>
                                                    <span class="badge bg-<?= $keyDetails['status'] === 'active' ? 'success' : 'danger' ?>">
                                                        <?= e($keyDetails['status']) ?>
                                                    </span>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><strong>Usuário:</strong></td>
                                                <td><?= e($keyDetails['user_name']) ?> (<?= e($keyDetails['email']) ?>)</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Status Usuário:</strong></td>
                                                <td>
                                                    <span class="badge bg-<?= $keyDetails['user_status'] === 'active' ? 'success' : 'danger' ?>">
                                                        <?= e($keyDetails['user_status']) ?>
                                                    </span>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><strong>Plano:</strong></td>
                                                <td><?= e($keyDetails['plan_name']) ?></td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">Status da Assinatura</h6>
                                    </div>
                                    <div class="card-body">
                                        <table class="table table-sm">
                                            <tr>
                                                <td><strong>Status:</strong></td>
                                                <td>
                                                    <span class="badge bg-<?= $keyDetails['subscription_status'] === 'active' ? 'success' : 'danger' ?>">
                                                        <?= e($keyDetails['subscription_status']) ?>
                                                    </span>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><strong>Pagamento:</strong></td>
                                                <td>
                                                    <span class="badge bg-<?= $keyDetails['payment_status'] === 'paid' ? 'success' : 'warning' ?>">
                                                        <?= e($keyDetails['payment_status']) ?>
                                                    </span>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><strong>Início:</strong></td>
                                                <td><?= date('d/m/Y H:i', strtotime($keyDetails['starts_at'])) ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Fim:</strong></td>
                                                <td><?= $keyDetails['ends_at'] ? date('d/m/Y H:i', strtotime($keyDetails['ends_at'])) : 'Vitalício' ?></td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Resultado da validação -->
                        <div class="card mt-3">
                            <div class="card-header">
                                <h6 class="mb-0">Resultado da Validação</h6>
                            </div>
                            <div class="card-body">
                                <?php if ($keyDetails['verification']['valid']): ?>
                                    <div class="alert alert-success">
                                        <i class="fas fa-check-circle me-2"></i>
                                        <strong>Chave VÁLIDA!</strong> Todas as verificações passaram.
                                    </div>
                                <?php else: ?>
                                    <div class="alert alert-danger">
                                        <i class="fas fa-times-circle me-2"></i>
                                        <strong>Chave INVÁLIDA:</strong> <?= e($keyDetails['verification']['reason']) ?>
                                    </div>
                                    
                                    <!-- Botão para corrigir -->
                                    <form method="POST" class="mt-3">
                                        <input type="hidden" name="_token" value="<?= $csrf_token ?>">
                                        <input type="hidden" name="action" value="fix_key">
                                        <input type="hidden" name="api_key" value="<?= e($searchKey) ?>">
                                        <button type="submit" class="btn btn-warning" onclick="return confirm('Tem certeza que deseja corrigir esta chave? Isso ativará o usuário, a chave e a assinatura.')">
                                            <i class="fas fa-wrench me-1"></i>
                                            Corrigir Automaticamente
                                        </button>
                                    </form>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                    <?php elseif ($searchKey): ?>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Chave não encontrada no sistema.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
$content = ob_get_clean();
include __DIR__ . '/../layouts/app.php';
?>
