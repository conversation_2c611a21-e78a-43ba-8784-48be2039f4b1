<?php
/**
 * <PERSON>ript para corrigir problemas rapidamente
 */

require_once __DIR__ . '/bootstrap.php';

echo "🔧 Executando correções rápidas...\n\n";

$db = Database::getInstance();

try {
    // 1. Verificar se tabela client_sites existe
    echo "1️⃣ Verificando tabela client_sites...\n";
    $tableExists = $db->fetch("SHOW TABLES LIKE 'client_sites'");
    
    if (!$tableExists) {
        echo "❌ Tabela client_sites não existe. Criando...\n";
        
        $sql = "
        CREATE TABLE client_sites (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            site_name VARCHAR(255) NOT NULL,
            site_url VARCHAR(500) NOT NULL,
            status ENUM('pending', 'connected', 'disconnected', 'suspended') DEFAULT 'pending',
            plugin_version VARCHAR(50) NULL,
            last_connection TIMESTAMP NULL,
            api_key_id INT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            
            INDEX idx_user_id (user_id),
            INDEX idx_status (status)
        );
        ";
        
        $db->getConnection()->exec($sql);
        echo "✅ Tabela client_sites criada!\n";
    } else {
        echo "✅ Tabela client_sites já existe!\n";
    }
    
    echo "\n";
    
    // 2. Verificar chaves de API
    echo "2️⃣ Verificando chaves de API...\n";
    $apiKeys = $db->fetchAll("SELECT api_key, name, status FROM api_keys LIMIT 5");
    
    if (empty($apiKeys)) {
        echo "❌ Nenhuma chave de API encontrada!\n";
    } else {
        echo "✅ Chaves encontradas:\n";
        foreach ($apiKeys as $key) {
            echo "   - {$key['api_key']} ({$key['name']}) - {$key['status']}\n";
        }
    }
    
    echo "\n";
    
    // 3. Testar input JSON
    echo "3️⃣ Testando processamento de JSON...\n";
    
    // Simular dados JSON
    $testJson = '{"api_key":"test123","test":"value"}';
    
    // Simular ambiente
    $_SERVER['CONTENT_TYPE'] = 'application/json';
    
    // Criar um controller temporário para testar
    class TestController extends Controller {
        public function testInput($key) {
            return $this->input($key);
        }
    }
    
    // Simular input stream
    $tempFile = tempnam(sys_get_temp_dir(), 'json_test');
    file_put_contents($tempFile, $testJson);
    
    // Redirecionar php://input
    $originalInput = 'php://input';
    
    // Testar método input
    $controller = new TestController();
    
    // Simular $_POST vazio e JSON no corpo
    $_POST = [];
    $_GET = [];
    
    echo "📦 JSON de teste: $testJson\n";
    
    // Testar decodificação manual
    $decoded = json_decode($testJson, true);
    if ($decoded) {
        echo "✅ JSON decodificado com sucesso: " . print_r($decoded, true) . "\n";
    } else {
        echo "❌ Erro ao decodificar JSON\n";
    }
    
    echo "\n";
    
    // 4. Testar endpoint diretamente
    echo "4️⃣ Testando endpoint validate...\n";
    
    // Simular requisição POST com JSON
    $_SERVER['REQUEST_METHOD'] = 'POST';
    $_SERVER['CONTENT_TYPE'] = 'application/json';
    
    // Criar dados de teste
    $testApiKey = 'e1copy_58211d1987fae226a0e2e73dd00ebe939aa9f';
    
    // Verificar se a chave existe
    $keyCheck = $db->fetch("SELECT * FROM api_keys WHERE api_key = :key", ['key' => $testApiKey]);
    
    if ($keyCheck) {
        echo "✅ Chave de teste encontrada no banco\n";
        echo "   Status: {$keyCheck['status']}\n";
        echo "   User ID: {$keyCheck['user_id']}\n";
    } else {
        echo "❌ Chave de teste não encontrada no banco\n";
        
        // Listar primeiras 3 chaves
        $sampleKeys = $db->fetchAll("SELECT api_key FROM api_keys LIMIT 3");
        echo "📋 Chaves disponíveis:\n";
        foreach ($sampleKeys as $key) {
            echo "   - {$key['api_key']}\n";
        }
    }
    
    echo "\n✅ Correções concluídas!\n";
    
} catch (Exception $e) {
    echo "❌ Erro: " . $e->getMessage() . "\n";
    echo "📍 Arquivo: " . $e->getFile() . ":" . $e->getLine() . "\n";
}
?>
