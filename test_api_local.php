<?php
/**
 * Teste local da API sem fazer requisição HTTP
 */

// Simular ambiente de requisição
$_SERVER['REQUEST_METHOD'] = 'GET';
$_SERVER['REQUEST_URI'] = '/api/v1/client-posts/test';
$_SERVER['HTTP_HOST'] = 'localhost';
$_SERVER['HTTPS'] = 'off';

// Ativar exibição de erros
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "=== TESTE LOCAL DA API ===\n";

try {
    // Carregar bootstrap
    require_once "bootstrap.php";
    echo "1. Bootstrap carregado\n";
    
    // Instanciar controller
    require_once "controllers/ApiController.php";
    $controller = new ApiController();
    echo "2. Controller instanciado\n";
    
    // Testar método diretamente
    echo "3. Testando método testPendingPosts...\n";
    
    // Capturar output
    ob_start();
    $result = $controller->testPendingPosts();
    $output = ob_get_clean();
    
    echo "4. Resultado:\n";
    echo $output . "\n";
    
    echo "5. Testando método getPendingClientPosts...\n";
    
    // Capturar output
    ob_start();
    $result2 = $controller->getPendingClientPosts();
    $output2 = ob_get_clean();
    
    echo "6. Resultado:\n";
    echo $output2 . "\n";
    
} catch (Exception $e) {
    echo "ERRO: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}

echo "\n=== FIM TESTE ===\n";
